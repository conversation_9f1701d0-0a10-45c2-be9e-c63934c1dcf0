"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/lib/services/limitHandler.ts":
/*!******************************************!*\
  !*** ./src/lib/services/limitHandler.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimitHandler: () => (/* binding */ LimitHandler)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n/* harmony import */ var _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/webhookLogger */ \"(app-pages-browser)/./src/lib/utils/webhookLogger.ts\");\n\n\n\n\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n// src/lib/services/limitHandler.ts\n// Manejo de límites alcanzados y notificaciones\n\n\n\nvar LimitHandler = /*#__PURE__*/ function() {\n    function LimitHandler() {\n        (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, LimitHandler);\n    }\n    (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(LimitHandler, null, [\n        {\n            key: \"checkUserLimits\",\n            value: /**\n     * Verificar estado de límites del usuario (versión servidor)\n     */ function() {\n                var _checkUserLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(userId) {\n                    var _yield$import, SupabaseAdminService, profile, limits, tokenStatus, planStatus;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {\n                        while(1)switch(_context.prev = _context.next){\n                            case 0:\n                                _context.prev = 0;\n                                _context.next = 3;\n                                return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_admin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(app-pages-browser)/./src/lib/supabase/admin.ts\"));\n                            case 3:\n                                _yield$import = _context.sent;\n                                SupabaseAdminService = _yield$import.SupabaseAdminService;\n                                _context.next = 7;\n                                return SupabaseAdminService.getUserProfile(userId);\n                            case 7:\n                                profile = _context.sent;\n                                if (profile) {\n                                    _context.next = 10;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", []);\n                            case 10:\n                                limits = []; // Verificar límites de tokens\n                                _context.next = 13;\n                                return this.checkTokenLimits(profile);\n                            case 13:\n                                tokenStatus = _context.sent;\n                                if (tokenStatus) {\n                                    limits.push(tokenStatus);\n                                }\n                                // Verificar límites de plan\n                                _context.next = 17;\n                                return this.checkPlanLimits(profile);\n                            case 17:\n                                planStatus = _context.sent;\n                                if (planStatus) {\n                                    limits.push(planStatus);\n                                }\n                                return _context.abrupt(\"return\", limits);\n                            case 22:\n                                _context.prev = 22;\n                                _context.t0 = _context[\"catch\"](0);\n                                console.error('Error checking user limits:', _context.t0);\n                                return _context.abrupt(\"return\", []);\n                            case 26:\n                            case \"end\":\n                                return _context.stop();\n                        }\n                    }, _callee, this, [\n                        [\n                            0,\n                            22\n                        ]\n                    ]);\n                }));\n                function checkUserLimits(_x) {\n                    return _checkUserLimits.apply(this, arguments);\n                }\n                return checkUserLimits;\n            }()\n        },\n        {\n            key: \"checkClientUserLimits\",\n            value: function() {\n                var _checkClientUserLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {\n                    var supabase, _yield$supabase$auth$, user, authError, _yield$supabase$from$, profile, profileError, limits, tokenStatus, planStatus;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {\n                        while(1)switch(_context2.prev = _context2.next){\n                            case 0:\n                                _context2.prev = 0;\n                                supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n                                _context2.next = 4;\n                                return supabase.auth.getUser();\n                            case 4:\n                                _yield$supabase$auth$ = _context2.sent;\n                                user = _yield$supabase$auth$.data.user;\n                                authError = _yield$supabase$auth$.error;\n                                if (!(authError || !user)) {\n                                    _context2.next = 9;\n                                    break;\n                                }\n                                return _context2.abrupt(\"return\", []);\n                            case 9:\n                                _context2.next = 11;\n                                return supabase.from('user_profiles').select('subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month, plan_expires_at').eq('user_id', user.id).single();\n                            case 11:\n                                _yield$supabase$from$ = _context2.sent;\n                                profile = _yield$supabase$from$.data;\n                                profileError = _yield$supabase$from$.error;\n                                if (!(profileError && profileError.code !== 'PGRST116')) {\n                                    _context2.next = 17;\n                                    break;\n                                }\n                                console.error(\"Error fetching profile for limits check:\", profileError);\n                                return _context2.abrupt(\"return\", []);\n                            case 17:\n                                if (profile) {\n                                    _context2.next = 19;\n                                    break;\n                                }\n                                return _context2.abrupt(\"return\", []);\n                            case 19:\n                                limits = []; // Verificar límites de tokens\n                                _context2.next = 22;\n                                return this.checkTokenLimits(profile);\n                            case 22:\n                                tokenStatus = _context2.sent;\n                                if (tokenStatus) {\n                                    limits.push(tokenStatus);\n                                }\n                                // Verificar límites de plan\n                                _context2.next = 26;\n                                return this.checkPlanLimits(profile);\n                            case 26:\n                                planStatus = _context2.sent;\n                                if (planStatus) {\n                                    limits.push(planStatus);\n                                }\n                                return _context2.abrupt(\"return\", limits);\n                            case 31:\n                                _context2.prev = 31;\n                                _context2.t0 = _context2[\"catch\"](0);\n                                console.error('Error checking client user limits:', _context2.t0);\n                                return _context2.abrupt(\"return\", []);\n                            case 35:\n                            case \"end\":\n                                return _context2.stop();\n                        }\n                    }, _callee2, this, [\n                        [\n                            0,\n                            31\n                        ]\n                    ]);\n                }));\n                function checkClientUserLimits() {\n                    return _checkClientUserLimits.apply(this, arguments);\n                }\n                return checkClientUserLimits;\n            }()\n        },\n        {\n            key: \"checkTokenLimits\",\n            value: function() {\n                var _checkTokenLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(profile) {\n                    var currentMonth, currentTokens, percentage, severity, message, actionRequired, suggestedAction, upgradeOptions;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee3$(_context3) {\n                        while(1)switch(_context3.prev = _context3.next){\n                            case 0:\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                                percentage = currentTokens / profile.monthly_token_limit * 100; // Determinar severidad\n                                severity = 'warning';\n                                message = '';\n                                actionRequired = false;\n                                suggestedAction = '';\n                                if (!(percentage >= 100)) {\n                                    _context3.next = 14;\n                                    break;\n                                }\n                                severity = 'exceeded';\n                                message = \"Has excedido tu l\\xEDmite mensual de tokens (\".concat(currentTokens.toLocaleString(), \"/\").concat(profile.monthly_token_limit.toLocaleString(), \")\");\n                                actionRequired = true;\n                                suggestedAction = 'Actualiza tu plan para obtener más tokens';\n                                _context3.next = 29;\n                                break;\n                            case 14:\n                                if (!(percentage >= 90)) {\n                                    _context3.next = 21;\n                                    break;\n                                }\n                                severity = 'limit_reached';\n                                message = \"Est\\xE1s cerca de tu l\\xEDmite mensual de tokens (\".concat(Math.round(percentage), \"% usado)\");\n                                actionRequired = true;\n                                suggestedAction = 'Considera actualizar tu plan antes de alcanzar el límite';\n                                _context3.next = 29;\n                                break;\n                            case 21:\n                                if (!(percentage >= 75)) {\n                                    _context3.next = 28;\n                                    break;\n                                }\n                                severity = 'warning';\n                                message = \"Has usado \".concat(Math.round(percentage), \"% de tus tokens mensuales\");\n                                actionRequired = false;\n                                suggestedAction = 'Monitorea tu uso para evitar alcanzar el límite';\n                                _context3.next = 29;\n                                break;\n                            case 28:\n                                return _context3.abrupt(\"return\", null);\n                            case 29:\n                                // Obtener opciones de upgrade\n                                upgradeOptions = this.getUpgradeOptions(profile.subscription_plan);\n                                return _context3.abrupt(\"return\", {\n                                    type: 'tokens',\n                                    severity: severity,\n                                    current: currentTokens,\n                                    limit: profile.monthly_token_limit,\n                                    percentage: Math.round(percentage),\n                                    message: message,\n                                    actionRequired: actionRequired,\n                                    suggestedAction: suggestedAction,\n                                    upgradeOptions: upgradeOptions\n                                });\n                            case 31:\n                            case \"end\":\n                                return _context3.stop();\n                        }\n                    }, _callee3, this);\n                }));\n                function checkTokenLimits(_x2) {\n                    return _checkTokenLimits.apply(this, arguments);\n                }\n                return checkTokenLimits;\n            }()\n        },\n        {\n            key: \"checkPlanLimits\",\n            value: function() {\n                var _checkPlanLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4(profile) {\n                    var expirationDate, now, daysUntilExpiration;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee4$(_context4) {\n                        while(1)switch(_context4.prev = _context4.next){\n                            case 0:\n                                if (!(profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                                    _context4.next = 2;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    type: 'plan',\n                                    severity: 'exceeded',\n                                    current: 0,\n                                    limit: 1,\n                                    percentage: 0,\n                                    message: 'Tu pago está pendiente de verificación',\n                                    actionRequired: true,\n                                    suggestedAction: 'Completa el proceso de pago para activar tu plan',\n                                    upgradeOptions: []\n                                });\n                            case 2:\n                                if (!profile.plan_expires_at) {\n                                    _context4.next = 12;\n                                    break;\n                                }\n                                expirationDate = new Date(profile.plan_expires_at);\n                                now = new Date();\n                                daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));\n                                if (!(daysUntilExpiration <= 0)) {\n                                    _context4.next = 10;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    type: 'plan',\n                                    severity: 'exceeded',\n                                    current: 0,\n                                    limit: 1,\n                                    percentage: 0,\n                                    message: 'Tu plan ha expirado',\n                                    actionRequired: true,\n                                    suggestedAction: 'Renueva tu suscripción para continuar usando las funciones premium',\n                                    upgradeOptions: this.getUpgradeOptions(profile.subscription_plan)\n                                });\n                            case 10:\n                                if (!(daysUntilExpiration <= 7)) {\n                                    _context4.next = 12;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    type: 'plan',\n                                    severity: 'warning',\n                                    current: daysUntilExpiration,\n                                    limit: 30,\n                                    percentage: Math.round((30 - daysUntilExpiration) / 30 * 100),\n                                    message: \"Tu plan expira en \".concat(daysUntilExpiration, \" d\\xEDa\").concat(daysUntilExpiration !== 1 ? 's' : ''),\n                                    actionRequired: false,\n                                    suggestedAction: 'Renueva tu suscripción para evitar la interrupción del servicio',\n                                    upgradeOptions: this.getUpgradeOptions(profile.subscription_plan)\n                                });\n                            case 12:\n                                return _context4.abrupt(\"return\", null);\n                            case 13:\n                            case \"end\":\n                                return _context4.stop();\n                        }\n                    }, _callee4, this);\n                }));\n                function checkPlanLimits(_x3) {\n                    return _checkPlanLimits.apply(this, arguments);\n                }\n                return checkPlanLimits;\n            }()\n        },\n        {\n            key: \"getUpgradeOptions\",\n            value: function getUpgradeOptions(currentPlan) {\n                var upgradeOptions = [];\n                if (currentPlan === 'free') {\n                    var usuarioPlan = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)('usuario');\n                    var proPlan = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)('pro');\n                    if (usuarioPlan) {\n                        var monthlyTokens = usuarioPlan.limits.monthlyTokens || 1000000;\n                        upgradeOptions.push({\n                            plan: 'usuario',\n                            benefits: [\n                                'Chat con preparador IA',\n                                \"\".concat(monthlyTokens.toLocaleString(), \" tokens mensuales\"),\n                                'Tests y flashcards ilimitados'\n                            ],\n                            newLimit: monthlyTokens\n                        });\n                    }\n                    if (proPlan) {\n                        upgradeOptions.push({\n                            plan: 'pro',\n                            benefits: [\n                                'Todas las funciones del plan Usuario',\n                                'Planificación de estudios con IA',\n                                'Resúmenes A1 y A2',\n                                \"\".concat(proPlan.limits.monthlyTokens.toLocaleString(), \" tokens mensuales\")\n                            ],\n                            newLimit: proPlan.limits.monthlyTokens\n                        });\n                    }\n                } else if (currentPlan === 'usuario') {\n                    var _proPlan = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)('pro');\n                    if (_proPlan) {\n                        upgradeOptions.push({\n                            plan: 'pro',\n                            benefits: [\n                                'Planificación de estudios con IA',\n                                'Resúmenes A1 y A2',\n                                'Funciones avanzadas',\n                                \"\".concat(_proPlan.limits.monthlyTokens.toLocaleString(), \" tokens mensuales\")\n                            ],\n                            newLimit: _proPlan.limits.monthlyTokens\n                        });\n                    }\n                }\n                return upgradeOptions;\n            }\n        },\n        {\n            key: \"createLimitNotification\",\n            value: function() {\n                var _createLimitNotification = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee5(userId, limitStatus) {\n                    var notification;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee5$(_context5) {\n                        while(1)switch(_context5.prev = _context5.next){\n                            case 0:\n                                notification = {\n                                    userId: userId,\n                                    type: \"limit_\".concat(limitStatus.type),\n                                    severity: limitStatus.severity === 'exceeded' ? 'error' : limitStatus.severity === 'limit_reached' ? 'warning' : 'info',\n                                    title: this.getNotificationTitle(limitStatus),\n                                    message: limitStatus.message,\n                                    metadata: {\n                                        limitType: limitStatus.type,\n                                        current: limitStatus.current,\n                                        limit: limitStatus.limit,\n                                        percentage: limitStatus.percentage\n                                    }\n                                }; // Agregar acción si es necesaria\n                                if (limitStatus.actionRequired && limitStatus.upgradeOptions && limitStatus.upgradeOptions.length > 0) {\n                                    notification.actionUrl = '/payment';\n                                    notification.actionText = 'Actualizar Plan';\n                                }\n                                // Log de la notificación\n                                _context5.next = 4;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, \"limit_notification_\".concat(limitStatus.type), false, 'system', 0, \"Limit notification: \".concat(limitStatus.severity));\n                            case 4:\n                                return _context5.abrupt(\"return\", notification);\n                            case 5:\n                            case \"end\":\n                                return _context5.stop();\n                        }\n                    }, _callee5, this);\n                }));\n                function createLimitNotification(_x4, _x5) {\n                    return _createLimitNotification.apply(this, arguments);\n                }\n                return createLimitNotification;\n            }()\n        },\n        {\n            key: \"getNotificationTitle\",\n            value: function getNotificationTitle(limitStatus) {\n                switch(limitStatus.type){\n                    case 'tokens':\n                        if (limitStatus.severity === 'exceeded') {\n                            return 'Límite de tokens excedido';\n                        } else if (limitStatus.severity === 'limit_reached') {\n                            return 'Límite de tokens casi alcanzado';\n                        } else {\n                            return 'Uso elevado de tokens';\n                        }\n                    case 'plan':\n                        if (limitStatus.severity === 'exceeded') {\n                            return 'Plan expirado o pago pendiente';\n                        } else {\n                            return 'Plan próximo a expirar';\n                        }\n                    default:\n                        return 'Límite alcanzado';\n                }\n            }\n        },\n        {\n            key: \"isActionBlocked\",\n            value: function() {\n                var _isActionBlocked = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee6(userId, action) {\n                    var tokensRequired, limits, tokenLimit, planLimit, _args6 = arguments;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee6$(_context6) {\n                        while(1)switch(_context6.prev = _context6.next){\n                            case 0:\n                                tokensRequired = _args6.length > 2 && _args6[2] !== undefined ? _args6[2] : 0;\n                                _context6.prev = 1;\n                                _context6.next = 4;\n                                return this.checkUserLimits(userId);\n                            case 4:\n                                limits = _context6.sent;\n                                if (!(tokensRequired > 0)) {\n                                    _context6.next = 11;\n                                    break;\n                                }\n                                tokenLimit = limits.find(function(l) {\n                                    return l.type === 'tokens';\n                                });\n                                if (!(tokenLimit && tokenLimit.severity === 'exceeded')) {\n                                    _context6.next = 9;\n                                    break;\n                                }\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Límite mensual de tokens excedido',\n                                    limitStatus: tokenLimit\n                                });\n                            case 9:\n                                if (!(tokenLimit && tokenLimit.current + tokensRequired > tokenLimit.limit)) {\n                                    _context6.next = 11;\n                                    break;\n                                }\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: \"Esta acci\\xF3n requiere \".concat(tokensRequired, \" tokens pero solo tienes \").concat(tokenLimit.limit - tokenLimit.current, \" disponibles\"),\n                                    limitStatus: tokenLimit\n                                });\n                            case 11:\n                                // Verificar límites de plan\n                                planLimit = limits.find(function(l) {\n                                    return l.type === 'plan' && l.severity === 'exceeded';\n                                });\n                                if (!planLimit) {\n                                    _context6.next = 14;\n                                    break;\n                                }\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: planLimit.message,\n                                    limitStatus: planLimit\n                                });\n                            case 14:\n                                return _context6.abrupt(\"return\", {\n                                    blocked: false\n                                });\n                            case 17:\n                                _context6.prev = 17;\n                                _context6.t0 = _context6[\"catch\"](1);\n                                console.error('Error checking if action is blocked:', _context6.t0);\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Error verificando límites'\n                                });\n                            case 21:\n                            case \"end\":\n                                return _context6.stop();\n                        }\n                    }, _callee6, this, [\n                        [\n                            1,\n                            17\n                        ]\n                    ]);\n                }));\n                function isActionBlocked(_x6, _x7) {\n                    return _isActionBlocked.apply(this, arguments);\n                }\n                return isActionBlocked;\n            }()\n        },\n        {\n            key: \"isClientActionBlocked\",\n            value: function() {\n                var _isClientActionBlocked = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee7(action) {\n                    var tokensRequired, limits, tokenLimit, planLimit, _args7 = arguments;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee7$(_context7) {\n                        while(1)switch(_context7.prev = _context7.next){\n                            case 0:\n                                tokensRequired = _args7.length > 1 && _args7[1] !== undefined ? _args7[1] : 0;\n                                _context7.prev = 1;\n                                _context7.next = 4;\n                                return this.checkClientUserLimits();\n                            case 4:\n                                limits = _context7.sent;\n                                if (!(tokensRequired > 0)) {\n                                    _context7.next = 11;\n                                    break;\n                                }\n                                tokenLimit = limits.find(function(l) {\n                                    return l.type === 'tokens';\n                                });\n                                if (!(tokenLimit && tokenLimit.severity === 'exceeded')) {\n                                    _context7.next = 9;\n                                    break;\n                                }\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Límite mensual de tokens excedido',\n                                    limitStatus: tokenLimit\n                                });\n                            case 9:\n                                if (!(tokenLimit && tokenLimit.current + tokensRequired > tokenLimit.limit)) {\n                                    _context7.next = 11;\n                                    break;\n                                }\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: \"Esta acci\\xF3n requiere \".concat(tokensRequired, \" tokens pero solo tienes \").concat(tokenLimit.limit - tokenLimit.current, \" disponibles\"),\n                                    limitStatus: tokenLimit\n                                });\n                            case 11:\n                                // Verificar límites de plan\n                                planLimit = limits.find(function(l) {\n                                    return l.type === 'plan' && l.severity === 'exceeded';\n                                });\n                                if (!planLimit) {\n                                    _context7.next = 14;\n                                    break;\n                                }\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: planLimit.message,\n                                    limitStatus: planLimit\n                                });\n                            case 14:\n                                return _context7.abrupt(\"return\", {\n                                    blocked: false\n                                });\n                            case 17:\n                                _context7.prev = 17;\n                                _context7.t0 = _context7[\"catch\"](1);\n                                console.error('Error checking if client action is blocked:', _context7.t0);\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Error verificando límites'\n                                });\n                            case 21:\n                            case \"end\":\n                                return _context7.stop();\n                        }\n                    }, _callee7, this, [\n                        [\n                            1,\n                            17\n                        ]\n                    ]);\n                }));\n                function isClientActionBlocked(_x8) {\n                    return _isClientActionBlocked.apply(this, arguments);\n                }\n                return isClientActionBlocked;\n            }()\n        },\n        {\n            key: \"recordUsage\",\n            value: function() {\n                var _recordUsage = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee8(userId, action, tokensUsed) {\n                    var _yield$import2, SupabaseAdminService, profile, currentMonth, currentTokens;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee8$(_context8) {\n                        while(1)switch(_context8.prev = _context8.next){\n                            case 0:\n                                _context8.prev = 0;\n                                if (!(tokensUsed > 0)) {\n                                    _context8.next = 15;\n                                    break;\n                                }\n                                _context8.next = 4;\n                                return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_admin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(app-pages-browser)/./src/lib/supabase/admin.ts\"));\n                            case 4:\n                                _yield$import2 = _context8.sent;\n                                SupabaseAdminService = _yield$import2.SupabaseAdminService;\n                                _context8.next = 8;\n                                return SupabaseAdminService.getUserProfile(userId);\n                            case 8:\n                                profile = _context8.sent;\n                                if (!profile) {\n                                    _context8.next = 15;\n                                    break;\n                                }\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                                _context8.next = 14;\n                                return SupabaseAdminService.upsertUserProfile(_objectSpread(_objectSpread({}, profile), {}, {\n                                    current_month_tokens: currentTokens + tokensUsed,\n                                    current_month: currentMonth,\n                                    updated_at: new Date().toISOString()\n                                }));\n                            case 14:\n                                console.log(\"\\u2705 Tokens actualizados: +\".concat(tokensUsed, \" para usuario \").concat(userId));\n                            case 15:\n                                _context8.next = 17;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, action, true, 'system', tokensUsed, \"Action completed successfully\");\n                            case 17:\n                                _context8.next = 22;\n                                break;\n                            case 19:\n                                _context8.prev = 19;\n                                _context8.t0 = _context8[\"catch\"](0);\n                                console.error('Error recording usage:', _context8.t0);\n                            case 22:\n                            case \"end\":\n                                return _context8.stop();\n                        }\n                    }, _callee8, null, [\n                        [\n                            0,\n                            19\n                        ]\n                    ]);\n                }));\n                function recordUsage(_x9, _x10, _x11) {\n                    return _recordUsage.apply(this, arguments);\n                }\n                return recordUsage;\n            }()\n        },\n        {\n            key: \"recordClientUsage\",\n            value: function() {\n                var _recordClientUsage = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee9(action, tokensUsed) {\n                    var supabase, _yield$supabase$auth$2, user, authError, _yield$supabase$from$2, profile, profileError, currentMonth, currentTokens, _yield$supabase$from$3, updateError;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee9$(_context9) {\n                        while(1)switch(_context9.prev = _context9.next){\n                            case 0:\n                                _context9.prev = 0;\n                                supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n                                _context9.next = 4;\n                                return supabase.auth.getUser();\n                            case 4:\n                                _yield$supabase$auth$2 = _context9.sent;\n                                user = _yield$supabase$auth$2.data.user;\n                                authError = _yield$supabase$auth$2.error;\n                                if (!(authError || !user)) {\n                                    _context9.next = 10;\n                                    break;\n                                }\n                                console.warn('Cannot record usage: user not authenticated');\n                                return _context9.abrupt(\"return\");\n                            case 10:\n                                if (!(tokensUsed > 0)) {\n                                    _context9.next = 27;\n                                    break;\n                                }\n                                _context9.next = 13;\n                                return supabase.from('user_profiles').select('subscription_plan, monthly_token_limit, current_month_tokens, current_month').eq('user_id', user.id).single();\n                            case 13:\n                                _yield$supabase$from$2 = _context9.sent;\n                                profile = _yield$supabase$from$2.data;\n                                profileError = _yield$supabase$from$2.error;\n                                if (!profileError) {\n                                    _context9.next = 19;\n                                    break;\n                                }\n                                console.error('Error fetching profile for usage recording:', profileError);\n                                return _context9.abrupt(\"return\");\n                            case 19:\n                                if (!profile) {\n                                    _context9.next = 27;\n                                    break;\n                                }\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0; // Actualizar tokens usando cliente normal\n                                _context9.next = 24;\n                                return supabase.from('user_profiles').update({\n                                    current_month_tokens: currentTokens + tokensUsed,\n                                    current_month: currentMonth,\n                                    updated_at: new Date().toISOString()\n                                }).eq('user_id', user.id);\n                            case 24:\n                                _yield$supabase$from$3 = _context9.sent;\n                                updateError = _yield$supabase$from$3.error;\n                                if (updateError) {\n                                    console.error('Error updating token usage:', updateError);\n                                } else {\n                                    console.log(\"\\u2705 Tokens actualizados: +\".concat(tokensUsed, \" para usuario \").concat(user.id));\n                                }\n                            case 27:\n                                _context9.next = 29;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(user.id, action, true, 'system', tokensUsed, \"Action completed successfully\");\n                            case 29:\n                                _context9.next = 34;\n                                break;\n                            case 31:\n                                _context9.prev = 31;\n                                _context9.t0 = _context9[\"catch\"](0);\n                                console.error('Error recording client usage:', _context9.t0);\n                            case 34:\n                            case \"end\":\n                                return _context9.stop();\n                        }\n                    }, _callee9, null, [\n                        [\n                            0,\n                            31\n                        ]\n                    ]);\n                }));\n                function recordClientUsage(_x12, _x13) {\n                    return _recordClientUsage.apply(this, arguments);\n                }\n                return recordClientUsage;\n            }()\n        }\n    ]);\n    return LimitHandler;\n}();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/limitHandler.ts\n"));

/***/ })

});