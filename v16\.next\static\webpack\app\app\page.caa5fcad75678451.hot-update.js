"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/lib/services/limitHandler.ts":
/*!******************************************!*\
  !*** ./src/lib/services/limitHandler.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LimitHandler: () => (/* binding */ LimitHandler)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n/* harmony import */ var _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/webhookLogger */ \"(app-pages-browser)/./src/lib/utils/webhookLogger.ts\");\n\n\n\n\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n// src/lib/services/limitHandler.ts\n// Manejo de límites alcanzados y notificaciones\n\n\n\nvar LimitHandler = /*#__PURE__*/ function() {\n    function LimitHandler() {\n        (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, LimitHandler);\n    }\n    (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(LimitHandler, null, [\n        {\n            key: \"checkUserLimits\",\n            value: /**\n     * Verificar estado de límites del usuario (versión servidor)\n     */ function() {\n                var _checkUserLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(userId) {\n                    var _yield$import, SupabaseAdminService, profile, limits, tokenStatus, planStatus;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {\n                        while(1)switch(_context.prev = _context.next){\n                            case 0:\n                                _context.prev = 0;\n                                _context.next = 3;\n                                return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_admin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(app-pages-browser)/./src/lib/supabase/admin.ts\"));\n                            case 3:\n                                _yield$import = _context.sent;\n                                SupabaseAdminService = _yield$import.SupabaseAdminService;\n                                _context.next = 7;\n                                return SupabaseAdminService.getUserProfile(userId);\n                            case 7:\n                                profile = _context.sent;\n                                if (profile) {\n                                    _context.next = 10;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", []);\n                            case 10:\n                                limits = []; // Verificar límites de tokens\n                                _context.next = 13;\n                                return this.checkTokenLimits(profile);\n                            case 13:\n                                tokenStatus = _context.sent;\n                                if (tokenStatus) {\n                                    limits.push(tokenStatus);\n                                }\n                                // Verificar límites de plan\n                                _context.next = 17;\n                                return this.checkPlanLimits(profile);\n                            case 17:\n                                planStatus = _context.sent;\n                                if (planStatus) {\n                                    limits.push(planStatus);\n                                }\n                                return _context.abrupt(\"return\", limits);\n                            case 22:\n                                _context.prev = 22;\n                                _context.t0 = _context[\"catch\"](0);\n                                console.error('Error checking user limits:', _context.t0);\n                                return _context.abrupt(\"return\", []);\n                            case 26:\n                            case \"end\":\n                                return _context.stop();\n                        }\n                    }, _callee, this, [\n                        [\n                            0,\n                            22\n                        ]\n                    ]);\n                }));\n                function checkUserLimits(_x) {\n                    return _checkUserLimits.apply(this, arguments);\n                }\n                return checkUserLimits;\n            }()\n        },\n        {\n            key: \"checkClientUserLimits\",\n            value: function() {\n                var _checkClientUserLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2() {\n                    var supabase, _yield$supabase$auth$, user, authError, _yield$supabase$from$, profile, profileError, limits, tokenStatus, planStatus;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {\n                        while(1)switch(_context2.prev = _context2.next){\n                            case 0:\n                                _context2.prev = 0;\n                                supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n                                _context2.next = 4;\n                                return supabase.auth.getUser();\n                            case 4:\n                                _yield$supabase$auth$ = _context2.sent;\n                                user = _yield$supabase$auth$.data.user;\n                                authError = _yield$supabase$auth$.error;\n                                if (!(authError || !user)) {\n                                    _context2.next = 9;\n                                    break;\n                                }\n                                return _context2.abrupt(\"return\", []);\n                            case 9:\n                                _context2.next = 11;\n                                return supabase.from('user_profiles').select('subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month, plan_expires_at').eq('user_id', user.id).single();\n                            case 11:\n                                _yield$supabase$from$ = _context2.sent;\n                                profile = _yield$supabase$from$.data;\n                                profileError = _yield$supabase$from$.error;\n                                if (!(profileError && profileError.code !== 'PGRST116')) {\n                                    _context2.next = 17;\n                                    break;\n                                }\n                                console.error(\"Error fetching profile for limits check:\", profileError);\n                                return _context2.abrupt(\"return\", []);\n                            case 17:\n                                if (profile) {\n                                    _context2.next = 19;\n                                    break;\n                                }\n                                return _context2.abrupt(\"return\", []);\n                            case 19:\n                                limits = []; // Verificar límites de tokens\n                                _context2.next = 22;\n                                return this.checkTokenLimits(profile);\n                            case 22:\n                                tokenStatus = _context2.sent;\n                                if (tokenStatus) {\n                                    limits.push(tokenStatus);\n                                }\n                                // Verificar límites de plan\n                                _context2.next = 26;\n                                return this.checkPlanLimits(profile);\n                            case 26:\n                                planStatus = _context2.sent;\n                                if (planStatus) {\n                                    limits.push(planStatus);\n                                }\n                                return _context2.abrupt(\"return\", limits);\n                            case 31:\n                                _context2.prev = 31;\n                                _context2.t0 = _context2[\"catch\"](0);\n                                console.error('Error checking client user limits:', _context2.t0);\n                                return _context2.abrupt(\"return\", []);\n                            case 35:\n                            case \"end\":\n                                return _context2.stop();\n                        }\n                    }, _callee2, this, [\n                        [\n                            0,\n                            31\n                        ]\n                    ]);\n                }));\n                function checkClientUserLimits() {\n                    return _checkClientUserLimits.apply(this, arguments);\n                }\n                return checkClientUserLimits;\n            }()\n        },\n        {\n            key: \"checkTokenLimits\",\n            value: function() {\n                var _checkTokenLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(profile) {\n                    var currentMonth, currentTokens, percentage, severity, message, actionRequired, suggestedAction, upgradeOptions;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee3$(_context3) {\n                        while(1)switch(_context3.prev = _context3.next){\n                            case 0:\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                                percentage = currentTokens / profile.monthly_token_limit * 100; // Determinar severidad\n                                severity = 'warning';\n                                message = '';\n                                actionRequired = false;\n                                suggestedAction = '';\n                                if (!(percentage >= 100)) {\n                                    _context3.next = 14;\n                                    break;\n                                }\n                                severity = 'exceeded';\n                                message = \"Has excedido tu l\\xEDmite mensual de tokens (\".concat(currentTokens.toLocaleString(), \"/\").concat(profile.monthly_token_limit.toLocaleString(), \")\");\n                                actionRequired = true;\n                                suggestedAction = 'Actualiza tu plan para obtener más tokens';\n                                _context3.next = 29;\n                                break;\n                            case 14:\n                                if (!(percentage >= 90)) {\n                                    _context3.next = 21;\n                                    break;\n                                }\n                                severity = 'limit_reached';\n                                message = \"Est\\xE1s cerca de tu l\\xEDmite mensual de tokens (\".concat(Math.round(percentage), \"% usado)\");\n                                actionRequired = true;\n                                suggestedAction = 'Considera actualizar tu plan antes de alcanzar el límite';\n                                _context3.next = 29;\n                                break;\n                            case 21:\n                                if (!(percentage >= 75)) {\n                                    _context3.next = 28;\n                                    break;\n                                }\n                                severity = 'warning';\n                                message = \"Has usado \".concat(Math.round(percentage), \"% de tus tokens mensuales\");\n                                actionRequired = false;\n                                suggestedAction = 'Monitorea tu uso para evitar alcanzar el límite';\n                                _context3.next = 29;\n                                break;\n                            case 28:\n                                return _context3.abrupt(\"return\", null);\n                            case 29:\n                                // Obtener opciones de upgrade\n                                upgradeOptions = this.getUpgradeOptions(profile.subscription_plan);\n                                return _context3.abrupt(\"return\", {\n                                    type: 'tokens',\n                                    severity: severity,\n                                    current: currentTokens,\n                                    limit: profile.monthly_token_limit,\n                                    percentage: Math.round(percentage),\n                                    message: message,\n                                    actionRequired: actionRequired,\n                                    suggestedAction: suggestedAction,\n                                    upgradeOptions: upgradeOptions\n                                });\n                            case 31:\n                            case \"end\":\n                                return _context3.stop();\n                        }\n                    }, _callee3, this);\n                }));\n                function checkTokenLimits(_x2) {\n                    return _checkTokenLimits.apply(this, arguments);\n                }\n                return checkTokenLimits;\n            }()\n        },\n        {\n            key: \"checkPlanLimits\",\n            value: function() {\n                var _checkPlanLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4(profile) {\n                    var expirationDate, now, daysUntilExpiration;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee4$(_context4) {\n                        while(1)switch(_context4.prev = _context4.next){\n                            case 0:\n                                if (!(profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                                    _context4.next = 2;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    type: 'plan',\n                                    severity: 'exceeded',\n                                    current: 0,\n                                    limit: 1,\n                                    percentage: 0,\n                                    message: 'Tu pago está pendiente de verificación',\n                                    actionRequired: true,\n                                    suggestedAction: 'Completa el proceso de pago para activar tu plan',\n                                    upgradeOptions: []\n                                });\n                            case 2:\n                                if (!profile.plan_expires_at) {\n                                    _context4.next = 12;\n                                    break;\n                                }\n                                expirationDate = new Date(profile.plan_expires_at);\n                                now = new Date();\n                                daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));\n                                if (!(daysUntilExpiration <= 0)) {\n                                    _context4.next = 10;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    type: 'plan',\n                                    severity: 'exceeded',\n                                    current: 0,\n                                    limit: 1,\n                                    percentage: 0,\n                                    message: 'Tu plan ha expirado',\n                                    actionRequired: true,\n                                    suggestedAction: 'Renueva tu suscripción para continuar usando las funciones premium',\n                                    upgradeOptions: this.getUpgradeOptions(profile.subscription_plan)\n                                });\n                            case 10:\n                                if (!(daysUntilExpiration <= 7)) {\n                                    _context4.next = 12;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    type: 'plan',\n                                    severity: 'warning',\n                                    current: daysUntilExpiration,\n                                    limit: 30,\n                                    percentage: Math.round((30 - daysUntilExpiration) / 30 * 100),\n                                    message: \"Tu plan expira en \".concat(daysUntilExpiration, \" d\\xEDa\").concat(daysUntilExpiration !== 1 ? 's' : ''),\n                                    actionRequired: false,\n                                    suggestedAction: 'Renueva tu suscripción para evitar la interrupción del servicio',\n                                    upgradeOptions: this.getUpgradeOptions(profile.subscription_plan)\n                                });\n                            case 12:\n                                return _context4.abrupt(\"return\", null);\n                            case 13:\n                            case \"end\":\n                                return _context4.stop();\n                        }\n                    }, _callee4, this);\n                }));\n                function checkPlanLimits(_x3) {\n                    return _checkPlanLimits.apply(this, arguments);\n                }\n                return checkPlanLimits;\n            }()\n        },\n        {\n            key: \"getUpgradeOptions\",\n            value: function getUpgradeOptions(currentPlan) {\n                var upgradeOptions = [];\n                if (currentPlan === 'free') {\n                    var usuarioPlan = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)('usuario');\n                    var proPlan = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)('pro');\n                    if (usuarioPlan) {\n                        var monthlyTokens = usuarioPlan.limits.monthlyTokens || 1000000;\n                        upgradeOptions.push({\n                            plan: 'usuario',\n                            benefits: [\n                                'Chat con preparador IA',\n                                \"\".concat(monthlyTokens.toLocaleString(), \" tokens mensuales\"),\n                                'Tests y flashcards ilimitados'\n                            ],\n                            newLimit: monthlyTokens\n                        });\n                    }\n                    if (proPlan) {\n                        var _monthlyTokens = proPlan.limits.monthlyTokens || 1000000;\n                        upgradeOptions.push({\n                            plan: 'pro',\n                            benefits: [\n                                'Todas las funciones del plan Usuario',\n                                'Planificación de estudios con IA',\n                                'Resúmenes A1 y A2',\n                                \"\".concat(_monthlyTokens.toLocaleString(), \" tokens mensuales\")\n                            ],\n                            newLimit: _monthlyTokens\n                        });\n                    }\n                } else if (currentPlan === 'usuario') {\n                    var _proPlan = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)('pro');\n                    if (_proPlan) {\n                        upgradeOptions.push({\n                            plan: 'pro',\n                            benefits: [\n                                'Planificación de estudios con IA',\n                                'Resúmenes A1 y A2',\n                                'Funciones avanzadas',\n                                \"\".concat(_proPlan.limits.monthlyTokens.toLocaleString(), \" tokens mensuales\")\n                            ],\n                            newLimit: _proPlan.limits.monthlyTokens\n                        });\n                    }\n                }\n                return upgradeOptions;\n            }\n        },\n        {\n            key: \"createLimitNotification\",\n            value: function() {\n                var _createLimitNotification = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee5(userId, limitStatus) {\n                    var notification;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee5$(_context5) {\n                        while(1)switch(_context5.prev = _context5.next){\n                            case 0:\n                                notification = {\n                                    userId: userId,\n                                    type: \"limit_\".concat(limitStatus.type),\n                                    severity: limitStatus.severity === 'exceeded' ? 'error' : limitStatus.severity === 'limit_reached' ? 'warning' : 'info',\n                                    title: this.getNotificationTitle(limitStatus),\n                                    message: limitStatus.message,\n                                    metadata: {\n                                        limitType: limitStatus.type,\n                                        current: limitStatus.current,\n                                        limit: limitStatus.limit,\n                                        percentage: limitStatus.percentage\n                                    }\n                                }; // Agregar acción si es necesaria\n                                if (limitStatus.actionRequired && limitStatus.upgradeOptions && limitStatus.upgradeOptions.length > 0) {\n                                    notification.actionUrl = '/payment';\n                                    notification.actionText = 'Actualizar Plan';\n                                }\n                                // Log de la notificación\n                                _context5.next = 4;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, \"limit_notification_\".concat(limitStatus.type), false, 'system', 0, \"Limit notification: \".concat(limitStatus.severity));\n                            case 4:\n                                return _context5.abrupt(\"return\", notification);\n                            case 5:\n                            case \"end\":\n                                return _context5.stop();\n                        }\n                    }, _callee5, this);\n                }));\n                function createLimitNotification(_x4, _x5) {\n                    return _createLimitNotification.apply(this, arguments);\n                }\n                return createLimitNotification;\n            }()\n        },\n        {\n            key: \"getNotificationTitle\",\n            value: function getNotificationTitle(limitStatus) {\n                switch(limitStatus.type){\n                    case 'tokens':\n                        if (limitStatus.severity === 'exceeded') {\n                            return 'Límite de tokens excedido';\n                        } else if (limitStatus.severity === 'limit_reached') {\n                            return 'Límite de tokens casi alcanzado';\n                        } else {\n                            return 'Uso elevado de tokens';\n                        }\n                    case 'plan':\n                        if (limitStatus.severity === 'exceeded') {\n                            return 'Plan expirado o pago pendiente';\n                        } else {\n                            return 'Plan próximo a expirar';\n                        }\n                    default:\n                        return 'Límite alcanzado';\n                }\n            }\n        },\n        {\n            key: \"isActionBlocked\",\n            value: function() {\n                var _isActionBlocked = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee6(userId, action) {\n                    var tokensRequired, limits, tokenLimit, planLimit, _args6 = arguments;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee6$(_context6) {\n                        while(1)switch(_context6.prev = _context6.next){\n                            case 0:\n                                tokensRequired = _args6.length > 2 && _args6[2] !== undefined ? _args6[2] : 0;\n                                _context6.prev = 1;\n                                _context6.next = 4;\n                                return this.checkUserLimits(userId);\n                            case 4:\n                                limits = _context6.sent;\n                                if (!(tokensRequired > 0)) {\n                                    _context6.next = 11;\n                                    break;\n                                }\n                                tokenLimit = limits.find(function(l) {\n                                    return l.type === 'tokens';\n                                });\n                                if (!(tokenLimit && tokenLimit.severity === 'exceeded')) {\n                                    _context6.next = 9;\n                                    break;\n                                }\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Límite mensual de tokens excedido',\n                                    limitStatus: tokenLimit\n                                });\n                            case 9:\n                                if (!(tokenLimit && tokenLimit.current + tokensRequired > tokenLimit.limit)) {\n                                    _context6.next = 11;\n                                    break;\n                                }\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: \"Esta acci\\xF3n requiere \".concat(tokensRequired, \" tokens pero solo tienes \").concat(tokenLimit.limit - tokenLimit.current, \" disponibles\"),\n                                    limitStatus: tokenLimit\n                                });\n                            case 11:\n                                // Verificar límites de plan\n                                planLimit = limits.find(function(l) {\n                                    return l.type === 'plan' && l.severity === 'exceeded';\n                                });\n                                if (!planLimit) {\n                                    _context6.next = 14;\n                                    break;\n                                }\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: planLimit.message,\n                                    limitStatus: planLimit\n                                });\n                            case 14:\n                                return _context6.abrupt(\"return\", {\n                                    blocked: false\n                                });\n                            case 17:\n                                _context6.prev = 17;\n                                _context6.t0 = _context6[\"catch\"](1);\n                                console.error('Error checking if action is blocked:', _context6.t0);\n                                return _context6.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Error verificando límites'\n                                });\n                            case 21:\n                            case \"end\":\n                                return _context6.stop();\n                        }\n                    }, _callee6, this, [\n                        [\n                            1,\n                            17\n                        ]\n                    ]);\n                }));\n                function isActionBlocked(_x6, _x7) {\n                    return _isActionBlocked.apply(this, arguments);\n                }\n                return isActionBlocked;\n            }()\n        },\n        {\n            key: \"isClientActionBlocked\",\n            value: function() {\n                var _isClientActionBlocked = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee7(action) {\n                    var tokensRequired, limits, tokenLimit, planLimit, _args7 = arguments;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee7$(_context7) {\n                        while(1)switch(_context7.prev = _context7.next){\n                            case 0:\n                                tokensRequired = _args7.length > 1 && _args7[1] !== undefined ? _args7[1] : 0;\n                                _context7.prev = 1;\n                                _context7.next = 4;\n                                return this.checkClientUserLimits();\n                            case 4:\n                                limits = _context7.sent;\n                                if (!(tokensRequired > 0)) {\n                                    _context7.next = 11;\n                                    break;\n                                }\n                                tokenLimit = limits.find(function(l) {\n                                    return l.type === 'tokens';\n                                });\n                                if (!(tokenLimit && tokenLimit.severity === 'exceeded')) {\n                                    _context7.next = 9;\n                                    break;\n                                }\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Límite mensual de tokens excedido',\n                                    limitStatus: tokenLimit\n                                });\n                            case 9:\n                                if (!(tokenLimit && tokenLimit.current + tokensRequired > tokenLimit.limit)) {\n                                    _context7.next = 11;\n                                    break;\n                                }\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: \"Esta acci\\xF3n requiere \".concat(tokensRequired, \" tokens pero solo tienes \").concat(tokenLimit.limit - tokenLimit.current, \" disponibles\"),\n                                    limitStatus: tokenLimit\n                                });\n                            case 11:\n                                // Verificar límites de plan\n                                planLimit = limits.find(function(l) {\n                                    return l.type === 'plan' && l.severity === 'exceeded';\n                                });\n                                if (!planLimit) {\n                                    _context7.next = 14;\n                                    break;\n                                }\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: planLimit.message,\n                                    limitStatus: planLimit\n                                });\n                            case 14:\n                                return _context7.abrupt(\"return\", {\n                                    blocked: false\n                                });\n                            case 17:\n                                _context7.prev = 17;\n                                _context7.t0 = _context7[\"catch\"](1);\n                                console.error('Error checking if client action is blocked:', _context7.t0);\n                                return _context7.abrupt(\"return\", {\n                                    blocked: true,\n                                    reason: 'Error verificando límites'\n                                });\n                            case 21:\n                            case \"end\":\n                                return _context7.stop();\n                        }\n                    }, _callee7, this, [\n                        [\n                            1,\n                            17\n                        ]\n                    ]);\n                }));\n                function isClientActionBlocked(_x8) {\n                    return _isClientActionBlocked.apply(this, arguments);\n                }\n                return isClientActionBlocked;\n            }()\n        },\n        {\n            key: \"recordUsage\",\n            value: function() {\n                var _recordUsage = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee8(userId, action, tokensUsed) {\n                    var _yield$import2, SupabaseAdminService, profile, currentMonth, currentTokens;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee8$(_context8) {\n                        while(1)switch(_context8.prev = _context8.next){\n                            case 0:\n                                _context8.prev = 0;\n                                if (!(tokensUsed > 0)) {\n                                    _context8.next = 15;\n                                    break;\n                                }\n                                _context8.next = 4;\n                                return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_admin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(app-pages-browser)/./src/lib/supabase/admin.ts\"));\n                            case 4:\n                                _yield$import2 = _context8.sent;\n                                SupabaseAdminService = _yield$import2.SupabaseAdminService;\n                                _context8.next = 8;\n                                return SupabaseAdminService.getUserProfile(userId);\n                            case 8:\n                                profile = _context8.sent;\n                                if (!profile) {\n                                    _context8.next = 15;\n                                    break;\n                                }\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                                _context8.next = 14;\n                                return SupabaseAdminService.upsertUserProfile(_objectSpread(_objectSpread({}, profile), {}, {\n                                    current_month_tokens: currentTokens + tokensUsed,\n                                    current_month: currentMonth,\n                                    updated_at: new Date().toISOString()\n                                }));\n                            case 14:\n                                console.log(\"\\u2705 Tokens actualizados: +\".concat(tokensUsed, \" para usuario \").concat(userId));\n                            case 15:\n                                _context8.next = 17;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, action, true, 'system', tokensUsed, \"Action completed successfully\");\n                            case 17:\n                                _context8.next = 22;\n                                break;\n                            case 19:\n                                _context8.prev = 19;\n                                _context8.t0 = _context8[\"catch\"](0);\n                                console.error('Error recording usage:', _context8.t0);\n                            case 22:\n                            case \"end\":\n                                return _context8.stop();\n                        }\n                    }, _callee8, null, [\n                        [\n                            0,\n                            19\n                        ]\n                    ]);\n                }));\n                function recordUsage(_x9, _x10, _x11) {\n                    return _recordUsage.apply(this, arguments);\n                }\n                return recordUsage;\n            }()\n        },\n        {\n            key: \"recordClientUsage\",\n            value: function() {\n                var _recordClientUsage = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee9(action, tokensUsed) {\n                    var supabase, _yield$supabase$auth$2, user, authError, _yield$supabase$from$2, profile, profileError, currentMonth, currentTokens, _yield$supabase$from$3, updateError;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee9$(_context9) {\n                        while(1)switch(_context9.prev = _context9.next){\n                            case 0:\n                                _context9.prev = 0;\n                                supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n                                _context9.next = 4;\n                                return supabase.auth.getUser();\n                            case 4:\n                                _yield$supabase$auth$2 = _context9.sent;\n                                user = _yield$supabase$auth$2.data.user;\n                                authError = _yield$supabase$auth$2.error;\n                                if (!(authError || !user)) {\n                                    _context9.next = 10;\n                                    break;\n                                }\n                                console.warn('Cannot record usage: user not authenticated');\n                                return _context9.abrupt(\"return\");\n                            case 10:\n                                if (!(tokensUsed > 0)) {\n                                    _context9.next = 27;\n                                    break;\n                                }\n                                _context9.next = 13;\n                                return supabase.from('user_profiles').select('subscription_plan, monthly_token_limit, current_month_tokens, current_month').eq('user_id', user.id).single();\n                            case 13:\n                                _yield$supabase$from$2 = _context9.sent;\n                                profile = _yield$supabase$from$2.data;\n                                profileError = _yield$supabase$from$2.error;\n                                if (!profileError) {\n                                    _context9.next = 19;\n                                    break;\n                                }\n                                console.error('Error fetching profile for usage recording:', profileError);\n                                return _context9.abrupt(\"return\");\n                            case 19:\n                                if (!profile) {\n                                    _context9.next = 27;\n                                    break;\n                                }\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0; // Actualizar tokens usando cliente normal\n                                _context9.next = 24;\n                                return supabase.from('user_profiles').update({\n                                    current_month_tokens: currentTokens + tokensUsed,\n                                    current_month: currentMonth,\n                                    updated_at: new Date().toISOString()\n                                }).eq('user_id', user.id);\n                            case 24:\n                                _yield$supabase$from$3 = _context9.sent;\n                                updateError = _yield$supabase$from$3.error;\n                                if (updateError) {\n                                    console.error('Error updating token usage:', updateError);\n                                } else {\n                                    console.log(\"\\u2705 Tokens actualizados: +\".concat(tokensUsed, \" para usuario \").concat(user.id));\n                                }\n                            case 27:\n                                _context9.next = 29;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(user.id, action, true, 'system', tokensUsed, \"Action completed successfully\");\n                            case 29:\n                                _context9.next = 34;\n                                break;\n                            case 31:\n                                _context9.prev = 31;\n                                _context9.t0 = _context9[\"catch\"](0);\n                                console.error('Error recording client usage:', _context9.t0);\n                            case 34:\n                            case \"end\":\n                                return _context9.stop();\n                        }\n                    }, _callee9, null, [\n                        [\n                            0,\n                            31\n                        ]\n                    ]);\n                }));\n                function recordClientUsage(_x12, _x13) {\n                    return _recordClientUsage.apply(this, arguments);\n                }\n                return recordClientUsage;\n            }()\n        }\n    ]);\n    return LimitHandler;\n}();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/limitHandler.ts\n"));

/***/ })

});