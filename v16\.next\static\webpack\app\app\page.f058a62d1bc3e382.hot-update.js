"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/lib/services/permissionService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/permissionService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionService: () => (/* binding */ PermissionService)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n/* harmony import */ var _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/webhookLogger */ \"(app-pages-browser)/./src/lib/utils/webhookLogger.ts\");\n\n\n\n\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n    if (!it) {\n        if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n            if (it) o = it;\n            var i = 0;\n            var F = function F() {};\n            return {\n                s: F,\n                n: function n() {\n                    if (i >= o.length) return {\n                        done: true\n                    };\n                    return {\n                        done: false,\n                        value: o[i++]\n                    };\n                },\n                e: function e(_e) {\n                    throw _e;\n                },\n                f: F\n            };\n        }\n        throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true, didErr = false, err;\n    return {\n        s: function s() {\n            it = it.call(o);\n        },\n        n: function n() {\n            var step = it.next();\n            normalCompletion = step.done;\n            return step;\n        },\n        e: function e(_e2) {\n            didErr = true;\n            err = _e2;\n        },\n        f: function f() {\n            try {\n                if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n            } finally{\n                if (didErr) throw err;\n            }\n        }\n    };\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\n\n// src/lib/services/permissionService.ts\n// Sistema centralizado de verificación de permisos\n\n\n\nvar PermissionService = /*#__PURE__*/ function() {\n    function PermissionService() {\n        (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, PermissionService);\n    }\n    (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(PermissionService, null, [\n        {\n            key: \"checkPermission\",\n            value: /**\n     * Verificar permisos completos para una acción\n     */ function() {\n                var _checkPermission = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(userId, permission) {\n                    var _yield$import, SupabaseAdminService, profile, planConfig, requiredPlans, tokenCheck, tokenInfo;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {\n                        while(1)switch(_context.prev = _context.next){\n                            case 0:\n                                _context.prev = 0;\n                                _context.next = 3;\n                                return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_admin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(app-pages-browser)/./src/lib/supabase/admin.ts\"));\n                            case 3:\n                                _yield$import = _context.sent;\n                                SupabaseAdminService = _yield$import.SupabaseAdminService;\n                                _context.next = 7;\n                                return SupabaseAdminService.getUserProfile(userId);\n                            case 7:\n                                profile = _context.sent;\n                                if (profile) {\n                                    _context.next = 10;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Perfil de usuario no encontrado'\n                                });\n                            case 10:\n                                if (!(permission.requiresPayment !== false && profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                                    _context.next = 12;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Pago no verificado',\n                                    userPlan: profile.subscription_plan,\n                                    upgradeRequired: false\n                                });\n                            case 12:\n                                if (!(permission.minimumPlan && permission.minimumPlan.length > 0)) {\n                                    _context.next = 15;\n                                    break;\n                                }\n                                if (permission.minimumPlan.includes(profile.subscription_plan)) {\n                                    _context.next = 15;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"Esta funci\\xF3n requiere plan \".concat(permission.minimumPlan.join(' o ')),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: permission.minimumPlan,\n                                    upgradeRequired: true,\n                                    suggestedPlan: this.getSuggestedPlan(profile.subscription_plan, permission.minimumPlan)\n                                });\n                            case 15:\n                                if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.hasFeatureAccess)(profile.subscription_plan, permission.feature)) {\n                                    _context.next = 19;\n                                    break;\n                                }\n                                planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)(profile.subscription_plan);\n                                requiredPlans = this.getPlansWithFeature(permission.feature);\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"La funci\\xF3n \".concat(permission.feature, \" no est\\xE1 disponible en \").concat((planConfig === null || planConfig === void 0 ? void 0 : planConfig.name) || profile.subscription_plan),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: requiredPlans,\n                                    upgradeRequired: true,\n                                    suggestedPlan: requiredPlans[0]\n                                });\n                            case 19:\n                                if (!(permission.tokensRequired && permission.tokensRequired > 0)) {\n                                    _context.next = 25;\n                                    break;\n                                }\n                                _context.next = 22;\n                                return this.checkTokenLimits(profile, permission.tokensRequired);\n                            case 22:\n                                tokenCheck = _context.sent;\n                                if (tokenCheck.allowed) {\n                                    _context.next = 25;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: tokenCheck.reason,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenCheck.tokenInfo,\n                                    upgradeRequired: tokenCheck.upgradeRequired,\n                                    suggestedPlan: tokenCheck.suggestedPlan\n                                });\n                            case 25:\n                                // Si llegamos aquí, el permiso está concedido\n                                tokenInfo = this.getTokenInfo(profile); // Log del acceso concedido\n                                _context.next = 28;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, permission.feature, true, profile.subscription_plan, permission.tokensRequired || 0);\n                            case 28:\n                                return _context.abrupt(\"return\", {\n                                    granted: true,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenInfo\n                                });\n                            case 31:\n                                _context.prev = 31;\n                                _context.t0 = _context[\"catch\"](0);\n                                console.error('Error checking permission:', _context.t0);\n                                // Log del error\n                                _context.next = 36;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, permission.feature, false, 'error', 0, 'Internal permission check error');\n                            case 36:\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Error interno de verificación de permisos'\n                                });\n                            case 37:\n                            case \"end\":\n                                return _context.stop();\n                        }\n                    }, _callee, this, [\n                        [\n                            0,\n                            31\n                        ]\n                    ]);\n                }));\n                function checkPermission(_x, _x2) {\n                    return _checkPermission.apply(this, arguments);\n                }\n                return checkPermission;\n            }()\n        },\n        {\n            key: \"checkMultiplePermissions\",\n            value: function() {\n                var _checkMultiplePermissions = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2(userId, permissions) {\n                    var results, _iterator, _step, permission;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {\n                        while(1)switch(_context2.prev = _context2.next){\n                            case 0:\n                                results = {};\n                                _iterator = _createForOfIteratorHelper(permissions);\n                                _context2.prev = 2;\n                                _iterator.s();\n                            case 4:\n                                if ((_step = _iterator.n()).done) {\n                                    _context2.next = 11;\n                                    break;\n                                }\n                                permission = _step.value;\n                                _context2.next = 8;\n                                return this.checkPermission(userId, permission);\n                            case 8:\n                                results[permission.feature] = _context2.sent;\n                            case 9:\n                                _context2.next = 4;\n                                break;\n                            case 11:\n                                _context2.next = 16;\n                                break;\n                            case 13:\n                                _context2.prev = 13;\n                                _context2.t0 = _context2[\"catch\"](2);\n                                _iterator.e(_context2.t0);\n                            case 16:\n                                _context2.prev = 16;\n                                _iterator.f();\n                                return _context2.finish(16);\n                            case 19:\n                                return _context2.abrupt(\"return\", results);\n                            case 20:\n                            case \"end\":\n                                return _context2.stop();\n                        }\n                    }, _callee2, this, [\n                        [\n                            2,\n                            13,\n                            16,\n                            19\n                        ]\n                    ]);\n                }));\n                function checkMultiplePermissions(_x3, _x4) {\n                    return _checkMultiplePermissions.apply(this, arguments);\n                }\n                return checkMultiplePermissions;\n            }()\n        },\n        {\n            key: \"checkTokenLimits\",\n            value: function() {\n                var _checkTokenLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(profile, tokensRequired) {\n                    var currentMonth, currentTokens, tokenInfo, suggestedPlan;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee3$(_context3) {\n                        while(1)switch(_context3.prev = _context3.next){\n                            case 0:\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01'; // Reset si es nuevo mes\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                                tokenInfo = {\n                                    current: currentTokens || 0,\n                                    limit: profile.monthly_token_limit || 0,\n                                    remaining: Math.max(0, (profile.monthly_token_limit || 0) - (currentTokens || 0)),\n                                    percentage: (profile.monthly_token_limit || 0) > 0 ? Math.round((currentTokens || 0) / profile.monthly_token_limit * 100) : 0\n                                }; // Verificar si tiene tokens suficientes\n                                if (!(currentTokens + tokensRequired > profile.monthly_token_limit)) {\n                                    _context3.next = 6;\n                                    break;\n                                }\n                                suggestedPlan = profile.subscription_plan === 'free' ? 'usuario' : 'pro';\n                                return _context3.abrupt(\"return\", {\n                                    allowed: false,\n                                    reason: \"L\\xEDmite mensual de tokens alcanzado. Necesitas \".concat(tokensRequired, \" tokens pero solo tienes \").concat(tokenInfo.remaining, \" disponibles.\"),\n                                    tokenInfo: tokenInfo,\n                                    upgradeRequired: true,\n                                    suggestedPlan: suggestedPlan\n                                });\n                            case 6:\n                                return _context3.abrupt(\"return\", {\n                                    allowed: true,\n                                    tokenInfo: tokenInfo\n                                });\n                            case 7:\n                            case \"end\":\n                                return _context3.stop();\n                        }\n                    }, _callee3);\n                }));\n                function checkTokenLimits(_x5, _x6) {\n                    return _checkTokenLimits.apply(this, arguments);\n                }\n                return checkTokenLimits;\n            }()\n        },\n        {\n            key: \"getTokenInfo\",\n            value: function getTokenInfo(profile) {\n                var currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                var currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                return {\n                    current: currentTokens || 0,\n                    limit: profile.monthly_token_limit || 0,\n                    remaining: Math.max(0, (profile.monthly_token_limit || 0) - (currentTokens || 0)),\n                    percentage: (profile.monthly_token_limit || 0) > 0 ? Math.round((currentTokens || 0) / profile.monthly_token_limit * 100) : 0\n                };\n            }\n        },\n        {\n            key: \"getSuggestedPlan\",\n            value: function getSuggestedPlan(currentPlan, requiredPlans) {\n                // Si el usuario tiene plan gratuito, sugerir el plan más bajo requerido\n                if (currentPlan === 'free') {\n                    return requiredPlans[0];\n                }\n                // Si el usuario tiene plan usuario y se requiere pro, sugerir pro\n                if (currentPlan === 'usuario' && requiredPlans.includes('pro')) {\n                    return 'pro';\n                }\n                // Por defecto, sugerir el plan más alto\n                return requiredPlans[requiredPlans.length - 1];\n            }\n        },\n        {\n            key: \"getPlansWithFeature\",\n            value: function getPlansWithFeature(feature) {\n                var plans = [\n                    'free',\n                    'usuario',\n                    'pro'\n                ];\n                var plansWithFeature = [];\n                for(var _i = 0, _plans = plans; _i < _plans.length; _i++){\n                    var plan = _plans[_i];\n                    if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.hasFeatureAccess)(plan, feature)) {\n                        plansWithFeature.push(plan);\n                    }\n                }\n                return plansWithFeature;\n            }\n        },\n        {\n            key: \"checkClientPermission\",\n            value: function() {\n                var _checkClientPermission = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4(permission) {\n                    var supabase, _yield$supabase$auth$, user, authError, _yield$supabase$from$, profile, profileError, planConfig, requiredPlans, tokenCheck, tokenInfo;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee4$(_context4) {\n                        while(1)switch(_context4.prev = _context4.next){\n                            case 0:\n                                _context4.prev = 0;\n                                supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n                                _context4.next = 4;\n                                return supabase.auth.getUser();\n                            case 4:\n                                _yield$supabase$auth$ = _context4.sent;\n                                user = _yield$supabase$auth$.data.user;\n                                authError = _yield$supabase$auth$.error;\n                                if (!(authError || !user)) {\n                                    _context4.next = 9;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Usuario no autenticado'\n                                });\n                            case 9:\n                                _context4.next = 11;\n                                return supabase.from('user_profiles').select('subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month').eq('user_id', user.id).single();\n                            case 11:\n                                _yield$supabase$from$ = _context4.sent;\n                                profile = _yield$supabase$from$.data;\n                                profileError = _yield$supabase$from$.error;\n                                if (!(profileError && profileError.code !== 'PGRST116')) {\n                                    _context4.next = 17;\n                                    break;\n                                }\n                                console.error(\"Error fetching profile for permission check:\", profileError);\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Error al obtener perfil de usuario'\n                                });\n                            case 17:\n                                if (profile) {\n                                    _context4.next = 19;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Perfil de usuario no encontrado'\n                                });\n                            case 19:\n                                if (!(permission.requiresPayment !== false && profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                                    _context4.next = 21;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Pago no verificado',\n                                    userPlan: profile.subscription_plan,\n                                    upgradeRequired: false\n                                });\n                            case 21:\n                                if (!(permission.minimumPlan && permission.minimumPlan.length > 0)) {\n                                    _context4.next = 24;\n                                    break;\n                                }\n                                if (permission.minimumPlan.includes(profile.subscription_plan)) {\n                                    _context4.next = 24;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"Esta funci\\xF3n requiere plan \".concat(permission.minimumPlan.join(' o ')),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: permission.minimumPlan,\n                                    upgradeRequired: true,\n                                    suggestedPlan: this.getSuggestedPlan(profile.subscription_plan, permission.minimumPlan)\n                                });\n                            case 24:\n                                if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.hasFeatureAccess)(profile.subscription_plan, permission.feature)) {\n                                    _context4.next = 28;\n                                    break;\n                                }\n                                planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)(profile.subscription_plan);\n                                requiredPlans = this.getPlansWithFeature(permission.feature);\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"La funci\\xF3n \".concat(permission.feature, \" no est\\xE1 disponible en \").concat((planConfig === null || planConfig === void 0 ? void 0 : planConfig.name) || profile.subscription_plan),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: requiredPlans,\n                                    upgradeRequired: true,\n                                    suggestedPlan: requiredPlans[0]\n                                });\n                            case 28:\n                                if (!(permission.tokensRequired && permission.tokensRequired > 0)) {\n                                    _context4.next = 34;\n                                    break;\n                                }\n                                _context4.next = 31;\n                                return this.checkTokenLimits(profile, permission.tokensRequired);\n                            case 31:\n                                tokenCheck = _context4.sent;\n                                if (tokenCheck.allowed) {\n                                    _context4.next = 34;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: tokenCheck.reason,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenCheck.tokenInfo,\n                                    upgradeRequired: tokenCheck.upgradeRequired,\n                                    suggestedPlan: tokenCheck.suggestedPlan\n                                });\n                            case 34:\n                                // Si llegamos aquí, el permiso está concedido\n                                tokenInfo = this.getTokenInfo(profile);\n                                return _context4.abrupt(\"return\", {\n                                    granted: true,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenInfo\n                                });\n                            case 38:\n                                _context4.prev = 38;\n                                _context4.t0 = _context4[\"catch\"](0);\n                                console.error('Error checking client permission:', _context4.t0);\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Error de verificación'\n                                });\n                            case 42:\n                            case \"end\":\n                                return _context4.stop();\n                        }\n                    }, _callee4, this, [\n                        [\n                            0,\n                            38\n                        ]\n                    ]);\n                }));\n                function checkClientPermission(_x7) {\n                    return _checkClientPermission.apply(this, arguments);\n                }\n                return checkClientPermission;\n            }()\n        },\n        {\n            key: \"createFeaturePermission\",\n            value: function createFeaturePermission(feature) {\n                var tokensRequired = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n                var featureRequirements = {\n                    'test_generation': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'flashcard_generation': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'mind_map_generation': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'ai_tutor_chat': {\n                        minimumPlan: [\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'study_planning': {\n                        minimumPlan: [\n                            'pro'\n                        ]\n                    },\n                    'summary_a1_a2': {\n                        minimumPlan: [\n                            'pro'\n                        ]\n                    },\n                    'document_upload': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    }\n                };\n                var requirements = featureRequirements[feature] || {};\n                return _objectSpread({\n                    feature: feature,\n                    tokensRequired: tokensRequired,\n                    requiresPayment: true\n                }, requirements);\n            }\n        }\n    ]);\n    return PermissionService;\n}();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/permissionService.ts\n"));

/***/ })

});