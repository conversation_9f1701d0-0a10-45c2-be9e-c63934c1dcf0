"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/app/app/page.tsx":
/*!******************************!*\
  !*** ./src/app/app/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppPage)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiCalendar,FiCheck,FiDownload,FiFileText,FiLogOut,FiPrinter,FiRefreshCw,FiUpload,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../features/summaries/components/SummaryGenerator */ \"(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../features/summaries/components/SummaryList */ \"(app-pages-browser)/./src/features/summaries/components/SummaryList.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ../../features/shared/components/SidebarMenu */ \"(app-pages-browser)/./src/features/shared/components/SidebarMenu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n/* harmony import */ var _components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/UpgradePlanMessage */ \"(app-pages-browser)/./src/components/ui/UpgradePlanMessage.tsx\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_SessionInfo__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/components/ui/SessionInfo */ \"(app-pages-browser)/./src/components/ui/SessionInfo.tsx\");\n/* harmony import */ var _components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/components/ui/TokenStatsModal */ \"(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction AppPage() {\n    _s();\n    _s1();\n    var _user$email, _this = this;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]), documentosSeleccionados = _useState[0], setDocumentosSeleccionados = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), mostrarUploader = _useState2[0], setMostrarUploader = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)('dashboard'), activeTab = _useState3[0], setActiveTab = _useState3[1];\n    var _useState4 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), showUploadSuccess = _useState4[0], setShowUploadSuccess = _useState4[1];\n    var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), isRefreshingDocuments = _useState5[0], setIsRefreshingDocuments = _useState5[1];\n    var _useState6 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), planEstudios = _useState6[0], setPlanEstudios = _useState6[1];\n    var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), temarioId = _useState7[0], setTemarioId = _useState7[1];\n    var _useState8 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), tienePlanificacion = _useState8[0], setTienePlanificacion = _useState8[1];\n    var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), showTokenStats = _useState9[0], setShowTokenStats = _useState9[1];\n    var _useState10 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), refreshSummaries = _useState10[0], setRefreshSummaries = _useState10[1];\n    var _useState11 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), hasStudyPlanningAccess = _useState11[0], setHasStudyPlanningAccess = _useState11[1];\n    var _useState12 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), hasAiTutorAccess = _useState12[0], setHasAiTutorAccess = _useState12[1];\n    var _useState13 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), hasSummaryAccess = _useState13[0], setHasSummaryAccess = _useState13[1];\n    var _useState14 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), shouldRefreshTokenStats = _useState14[0], setShouldRefreshTokenStats = _useState14[1];\n    var _useAuth = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__.useAuth)(), cerrarSesion = _useAuth.cerrarSesion, user = _useAuth.user, isLoading = _useAuth.isLoading;\n    var router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    var documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    // Hooks para el sistema de tareas en segundo plano\n    var _useBackgroundGenerat = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_22__.useBackgroundGeneration)(), generatePlanEstudios = _useBackgroundGenerat.generatePlanEstudios, isGenerating = _useBackgroundGenerat.isGenerating;\n    // Hook para manejar los resultados del plan de estudios\n    var _usePlanEstudiosResul = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_23__.usePlanEstudiosResults)({\n        onResult: function onResult(result) {\n            setPlanEstudios(result);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.success('¡Plan de estudios generado exitosamente!');\n        },\n        onError: function onError(error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.error(\"Error al generar plan: \".concat(error));\n        }\n    }), latestResult = _usePlanEstudiosResul.latestResult, isPlanLoading = _usePlanEstudiosResul.isLoading;\n    // Verificar autenticación y redirigir si no está autenticado\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AppPage.useEffect\": function() {\n            if (!isLoading && !user) {\n                router.push('/login');\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        user,\n        isLoading,\n        router\n    ]);\n    // Cargar datos del temario y verificar planificación\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AppPage.useEffect\": function() {\n            var cargarDatosTemario = /*#__PURE__*/ ({\n                \"AppPage.useEffect.cargarDatosTemario\": function() {\n                    var _ref = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {\n                        var temario, tienePlan, planExistente;\n                        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                            while(1)switch(_context.prev = _context.next){\n                                case 0:\n                                    if (user) {\n                                        _context.next = 2;\n                                        break;\n                                    }\n                                    return _context.abrupt(\"return\");\n                                case 2:\n                                    _context.prev = 2;\n                                    _context.next = 5;\n                                    return (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_26__.obtenerTemarioUsuario)();\n                                case 5:\n                                    temario = _context.sent;\n                                    if (!temario) {\n                                        _context.next = 18;\n                                        break;\n                                    }\n                                    setTemarioId(temario.id);\n                                    _context.next = 10;\n                                    return (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_27__.tienePlanificacionConfigurada)(temario.id);\n                                case 10:\n                                    tienePlan = _context.sent;\n                                    setTienePlanificacion(tienePlan);\n                                    // Verificar si ya existe un plan de estudios guardado\n                                    _context.next = 14;\n                                    return (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_28__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                                case 14:\n                                    planExistente = _context.sent;\n                                    if (planExistente && planExistente.plan_data) {\n                                        setPlanEstudios(planExistente.plan_data);\n                                    } else {\n                                        setPlanEstudios(null); // Limpiar plan si no existe\n                                    }\n                                    _context.next = 21;\n                                    break;\n                                case 18:\n                                    // No hay temario, limpiar todo el estado\n                                    setTemarioId(null);\n                                    setTienePlanificacion(false);\n                                    setPlanEstudios(null);\n                                case 21:\n                                    _context.next = 29;\n                                    break;\n                                case 23:\n                                    _context.prev = 23;\n                                    _context.t0 = _context[\"catch\"](2);\n                                    console.error('Error al cargar datos del temario:', _context.t0);\n                                    // En caso de error, limpiar el estado\n                                    setTemarioId(null);\n                                    setTienePlanificacion(false);\n                                    setPlanEstudios(null);\n                                case 29:\n                                case \"end\":\n                                    return _context.stop();\n                            }\n                        }, _callee, null, [\n                            [\n                                2,\n                                23\n                            ]\n                        ]);\n                    }));\n                    return function cargarDatosTemario() {\n                        return _ref.apply(this, arguments);\n                    };\n                }\n            })[\"AppPage.useEffect.cargarDatosTemario\"]();\n            cargarDatosTemario();\n        }\n    }[\"AppPage.useEffect\"], [\n        user\n    ]);\n    // Actualizar el plan cuando se reciba un resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AppPage.useEffect\": function() {\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        latestResult\n    ]);\n    // Verificar acceso a características restringidas\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"AppPage.useEffect\": function() {\n            var checkAccess = /*#__PURE__*/ ({\n                \"AppPage.useEffect.checkAccess\": function() {\n                    var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2() {\n                        var _yield$Promise$all, _yield$Promise$all2, studyPlanningAccess, aiTutorAccess, summaryAccess;\n                        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee2$(_context2) {\n                            while(1)switch(_context2.prev = _context2.next){\n                                case 0:\n                                    if (!user) {\n                                        _context2.next = 11;\n                                        break;\n                                    }\n                                    _context2.next = 3;\n                                    return Promise.all([\n                                        (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_24__.checkUserFeatureAccess)('study_planning'),\n                                        (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_24__.checkUserFeatureAccess)('ai_tutor_chat'),\n                                        (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_24__.checkUserFeatureAccess)('summary_a1_a2')\n                                    ]);\n                                case 3:\n                                    _yield$Promise$all = _context2.sent;\n                                    _yield$Promise$all2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_yield$Promise$all, 3);\n                                    studyPlanningAccess = _yield$Promise$all2[0];\n                                    aiTutorAccess = _yield$Promise$all2[1];\n                                    summaryAccess = _yield$Promise$all2[2];\n                                    setHasStudyPlanningAccess(studyPlanningAccess);\n                                    setHasAiTutorAccess(aiTutorAccess);\n                                    setHasSummaryAccess(summaryAccess);\n                                case 11:\n                                case \"end\":\n                                    return _context2.stop();\n                            }\n                        }, _callee2);\n                    }));\n                    return function checkAccess() {\n                        return _ref2.apply(this, arguments);\n                    };\n                }\n            })[\"AppPage.useEffect.checkAccess\"]();\n            checkAccess();\n        }\n    }[\"AppPage.useEffect\"], [\n        user\n    ]);\n    // Si está cargando o no hay usuario, mostrar pantalla de carga\n    if (isLoading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 7\n        }, this);\n    }\n    var handleUploadSuccess = /*#__PURE__*/ function() {\n        var _ref3 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee3() {\n            var _documentSelectorRef$;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee3$(_context3) {\n                while(1)switch(_context3.prev = _context3.next){\n                    case 0:\n                        setShowUploadSuccess(true);\n                        setIsRefreshingDocuments(true);\n                        // Recargar la lista de documentos automáticamente\n                        _context3.prev = 2;\n                        _context3.next = 5;\n                        return (_documentSelectorRef$ = documentSelectorRef.current) === null || _documentSelectorRef$ === void 0 ? void 0 : _documentSelectorRef$.recargarDocumentos();\n                    case 5:\n                        _context3.next = 10;\n                        break;\n                    case 7:\n                        _context3.prev = 7;\n                        _context3.t0 = _context3[\"catch\"](2);\n                        console.error('Error al recargar documentos:', _context3.t0);\n                    case 10:\n                        _context3.prev = 10;\n                        setIsRefreshingDocuments(false);\n                        return _context3.finish(10);\n                    case 13:\n                        // Ocultar el mensaje después de 5 segundos\n                        setTimeout(function() {\n                            return setShowUploadSuccess(false);\n                        }, 5000);\n                    case 14:\n                    case \"end\":\n                        return _context3.stop();\n                }\n            }, _callee3, null, [\n                [\n                    2,\n                    7,\n                    10,\n                    13\n                ]\n            ]);\n        }));\n        return function handleUploadSuccess() {\n            return _ref3.apply(this, arguments);\n        };\n    }();\n    var handleDocumentDeleted = /*#__PURE__*/ function() {\n        var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee4() {\n            var _documentSelectorRef$2;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee4$(_context4) {\n                while(1)switch(_context4.prev = _context4.next){\n                    case 0:\n                        _context4.prev = 0;\n                        _context4.next = 3;\n                        return (_documentSelectorRef$2 = documentSelectorRef.current) === null || _documentSelectorRef$2 === void 0 ? void 0 : _documentSelectorRef$2.recargarDocumentos();\n                    case 3:\n                        _context4.next = 8;\n                        break;\n                    case 5:\n                        _context4.prev = 5;\n                        _context4.t0 = _context4[\"catch\"](0);\n                        console.error('Error al recargar documentos después de eliminar:', _context4.t0);\n                    case 8:\n                    case \"end\":\n                        return _context4.stop();\n                }\n            }, _callee4, null, [\n                [\n                    0,\n                    5\n                ]\n            ]);\n        }));\n        return function handleDocumentDeleted() {\n            return _ref4.apply(this, arguments);\n        };\n    }();\n    var handleLogout = /*#__PURE__*/ function() {\n        var _ref5 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee5() {\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee5$(_context5) {\n                while(1)switch(_context5.prev = _context5.next){\n                    case 0:\n                        _context5.next = 2;\n                        return cerrarSesion();\n                    case 2:\n                    case \"end\":\n                        return _context5.stop();\n                }\n            }, _callee5);\n        }));\n        return function handleLogout() {\n            return _ref5.apply(this, arguments);\n        };\n    }();\n    var handleGenerarPlanEstudios = /*#__PURE__*/ function() {\n        var _ref6 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee6() {\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee6$(_context6) {\n                while(1)switch(_context6.prev = _context6.next){\n                    case 0:\n                        if (temarioId) {\n                            _context6.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.error('No se encontró un temario configurado');\n                        return _context6.abrupt(\"return\");\n                    case 3:\n                        if (tienePlanificacion) {\n                            _context6.next = 6;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                        return _context6.abrupt(\"return\");\n                    case 6:\n                        _context6.prev = 6;\n                        _context6.next = 9;\n                        return generatePlanEstudios({\n                            temarioId: temarioId,\n                            onComplete: function onComplete(result) {\n                                setPlanEstudios(result);\n                            },\n                            onError: function onError(error) {\n                                if (error.includes('planificación configurada')) {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                                } else {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                                }\n                            }\n                        });\n                    case 9:\n                        _context6.next = 14;\n                        break;\n                    case 11:\n                        _context6.prev = 11;\n                        _context6.t0 = _context6[\"catch\"](6);\n                        console.error('Error al iniciar generación del plan:', _context6.t0);\n                    case 14:\n                    case \"end\":\n                        return _context6.stop();\n                }\n            }, _callee6, null, [\n                [\n                    6,\n                    11\n                ]\n            ]);\n        }));\n        return function handleGenerarPlanEstudios() {\n            return _ref6.apply(this, arguments);\n        };\n    }();\n    var handleDescargarPlan = function handleDescargarPlan() {\n        if (!planEstudios) return;\n        // Convertir el plan estructurado a texto\n        var planTexto = convertirPlanATexto(planEstudios);\n        var blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        var url = URL.createObjectURL(blob);\n        var a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.success('Plan descargado exitosamente');\n    };\n    var convertirPlanATexto = function convertirPlanATexto(plan) {\n        var texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xFAmero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xF3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xF3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach(function(semana) {\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach(function(dia) {\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach(function(tarea) {\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xF3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    var handleImprimirPlan = function handleImprimirPlan() {\n        if (!planEstudios) return;\n        var planTexto = convertirPlanATexto(planEstudios);\n        var printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\\\"content\\\"></div>\\n            <script>\\n              // Convertir markdown a HTML b\\xE1sico para impresi\\xF3n\\n              const markdown = \".concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    // Función para convertir TabType a string para el Dashboard\n    var handleNavigateToTab = function handleNavigateToTab(tab) {\n        setActiveTab(tab);\n    };\n    // Handler para cuando se genera un resumen\n    var handleSummaryGenerated = function handleSummaryGenerated(summaryId) {\n        setRefreshSummaries(function(prev) {\n            return prev + 1;\n        });\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_30__.toast.success('Resumen generado exitosamente');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                        children: \"OposiAI\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Tu asistente inteligente para oposiciones\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_components_ui_SessionInfo__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        className: \"mt-1\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Hola, \",\n                                                    (_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.split('@')[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                                onClick: function onClick() {\n                                                    return router.push('/profile');\n                                                },\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Ver perfil\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiUser, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 341,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 332,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                        onClick: function onClick() {\n                                            return setShowTokenStats(true);\n                                        },\n                                        className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        title: \"Ver estad\\xEDsticas de uso de IA\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiBarChart, {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                        onClick: function onClick() {\n                                            return setMostrarUploader(!mostrarUploader);\n                                        },\n                                        className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiUpload, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xF3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 359,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xA1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 388,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 387,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 380,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiFileText, {\n                                        className: \"w-5 h-5 text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 403,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Documentos Seleccionados\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 mb-3\",\n                                children: \"Selecciona los documentos que quieres usar para generar contenido con IA.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                ref: documentSelectorRef,\n                                onSelectionChange: setDocumentosSeleccionados\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 409,\n                                columnNumber: 11\n                            }, this),\n                            documentosSeleccionados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                className: \"mt-3 p-3 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"strong\", {\n                                                children: documentosSeleccionados.length\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" documento\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \" seleccionado\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 flex flex-wrap gap-1\",\n                                        children: documentosSeleccionados.map(function(doc) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    doc.numero_tema && \"Tema \".concat(doc.numero_tema, \": \"),\n                                                    doc.titulo\n                                                ]\n                                            }, doc.id, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 420,\n                                                columnNumber: 19\n                                            }, _this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 414,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                activeTab: activeTab,\n                                onTabChange: setActiveTab\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    onNavigateToTab: handleNavigateToTab\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 447,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                children: !hasStudyPlanningAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    feature: \"study_planning\",\n                                                    benefits: [\n                                                        \"Planes de estudio personalizados con IA\",\n                                                        \"Cronogramas adaptativos a tu ritmo\",\n                                                        \"Seguimiento automático de progreso\",\n                                                        \"Recomendaciones inteligentes de repaso\"\n                                                    ],\n                                                    className: \"min-h-[600px]\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 452,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                                    children: \"Mi Plan de Estudios\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                                                                onClick: handleGenerarPlanEstudios,\n                                                                                disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                                className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiRefreshCw, {\n                                                                                        className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 475,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Regenerar Plan\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                                                                onClick: handleDescargarPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiDownload, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 482,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Descargar\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 478,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                                                                onClick: handleImprimirPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiPrinter, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: _jsxFileName,\n                                                                                        lineNumber: 489,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Imprimir\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: _jsxFileName,\n                                                                                lineNumber: 485,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 467,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 465,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                    children: \"Generando tu plan personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"La IA est\\xE1 analizando tu temario y configuraci\\xF3n...\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 499,\n                                                            columnNumber: 29\n                                                        }, this) : planEstudios && temarioId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                            plan: planEstudios,\n                                                            temarioId: temarioId\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 505,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiCalendar, {\n                                                                        className: \"w-10 h-10 text-teal-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: _jsxFileName,\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Genera tu Plan de Estudios Personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                    children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xF3n de planificaci\\xF3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: !tienePlanificacion,\n                                                                    className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPrinter_FiRefreshCw_FiUpload_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_34__.FiCalendar, {\n                                                                            className: \"w-5 h-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: _jsxFileName,\n                                                                            lineNumber: 525,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"Generar Plan de Estudios\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-4\",\n                                                                    children: \"Necesitas completar la configuraci\\xF3n de planificaci\\xF3n en \\\"Mi Temario\\\"\"\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 510,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 450,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && (!hasAiTutorAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                feature: \"ai_tutor_chat\",\n                                                benefits: [\n                                                    \"Chat ilimitado con IA especializada\",\n                                                    \"Respuestas personalizadas a tus documentos\",\n                                                    \"Historial completo de conversaciones\",\n                                                    \"Explicaciones detalladas y ejemplos\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 542,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 553,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 558,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 562,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 566,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 569,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 571,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'resumenes' && (!hasSummaryAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                feature: \"summary_a1_a2\",\n                                                benefits: [\n                                                    \"Resúmenes inteligentes con IA\",\n                                                    \"Formato A1 y A2 optimizado\",\n                                                    \"Edición automática de contenido\",\n                                                    \"Exportación a PDF de alta calidad\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 575,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        documentosSeleccionados: documentosSeleccionados,\n                                                        onSummaryGenerated: handleSummaryGenerated\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 587,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"hr\", {\n                                                        className: \"border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 591,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        refreshTrigger: refreshSummaries\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 592,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 586,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 598,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 433,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xA9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xE9rminos\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 621,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 616,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 610,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 609,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 608,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_33__.jsxDEV)(_components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                isOpen: showTokenStats,\n                onClose: function onClose() {\n                    setShowTokenStats(false);\n                    setShouldRefreshTokenStats(false); // Reset flag al cerrar\n                },\n                shouldRefreshOnOpen: shouldRefreshTokenStats\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 640,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPage, \"RfiE/XarF7ezjF/Xk6ni+GCw3PE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__.useAuth,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_22__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_22__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_22__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_23__.usePlanEstudiosResults,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_23__.usePlanEstudiosResults,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_23__.usePlanEstudiosResults\n    ];\n});\n_c1 = AppPage;\n_s1(AppPage, \"3DMxOcMi91EnOFSgGN9L6g/7EpE=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_21__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_22__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_23__.usePlanEstudiosResults\n    ];\n});\n_c = AppPage;\nvar _c;\n$RefreshReg$(_c, \"AppPage\");\nvar _c1;\n$RefreshReg$(_c1, \"AppPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app/page.tsx\n"));

/***/ })

});