"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx":
/*!****************************************************************!*\
  !*** ./src/features/summaries/components/SummaryGenerator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryGenerator)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/resumenesService */ \"(app-pages-browser)/./src/lib/supabase/resumenesService.ts\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\summaries\\\\components\\\\SummaryGenerator.tsx\", _s1 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\n\nvar summarySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    instrucciones: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres')\n});\nfunction SummaryGenerator(_ref) {\n    _s();\n    _s1();\n    var documentosSeleccionados = _ref.documentosSeleccionados, onSummaryGenerated = _ref.onSummaryGenerated;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), resumenGenerado = _useState[0], setResumenGenerado = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), tiempoEstimado = _useState2[0], setTiempoEstimado = _useState2[1];\n    var _useBackgroundGenerat = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration)(), generateResumen = _useBackgroundGenerat.generateResumen, isGenerating = _useBackgroundGenerat.isGenerating, getActiveTask = _useBackgroundGenerat.getActiveTask;\n    var _useForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(summarySchema),\n        defaultValues: {\n            instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'\n        }\n    }), register = _useForm.register, handleSubmit = _useForm.handleSubmit, errors = _useForm.formState.errors, reset = _useForm.reset;\n    // Funciones de validación\n    var validarDocumentoParaResumen = function validarDocumentoParaResumen(documento) {\n        if (!documento) {\n            return {\n                valido: false,\n                error: 'No se ha proporcionado ningún documento'\n            };\n        }\n        if (!documento.titulo || documento.titulo.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener un título'\n            };\n        }\n        if (!documento.contenido || documento.contenido.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener contenido'\n            };\n        }\n        if (documento.contenido.trim().length < 50) {\n            return {\n                valido: false,\n                error: 'El contenido del documento es demasiado corto para generar un resumen útil'\n            };\n        }\n        return {\n            valido: true\n        };\n    };\n    var estimarTiempoGeneracion = function estimarTiempoGeneracion(documento) {\n        if (!documento || !documento.contenido) {\n            return 30; // 30 segundos por defecto\n        }\n        var palabras = documento.contenido.split(/\\s+/).length;\n        // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos\n        var tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));\n        return tiempoEstimado;\n    };\n    // Validaciones\n    var puedeGenerar = documentosSeleccionados.length === 1;\n    var documento = documentosSeleccionados[0];\n    var validacionDocumento = documento ? validarDocumentoParaResumen(documento) : {\n        valido: false,\n        error: 'No hay documento seleccionado'\n    };\n    // Estado de la tarea en segundo plano\n    var activeTask = getActiveTask('resumen');\n    var isCurrentlyGenerating = isGenerating('resumen');\n    // Observar estado de la tarea para mostrar resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SummaryGenerator.useEffect\": function() {\n            if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'completed' && activeTask.result) {\n                setResumenGenerado(activeTask.result);\n                setTiempoEstimado(0);\n            } else if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'error') {\n                setTiempoEstimado(0);\n            }\n        }\n    }[\"SummaryGenerator.useEffect\"], [\n        activeTask\n    ]);\n    var onSubmit = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2(data) {\n            var existe, errorMessage;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee2$(_context2) {\n                while(1)switch(_context2.prev = _context2.next){\n                    case 0:\n                        if (!(!puedeGenerar || !documento || !validacionDocumento.valido)) {\n                            _context2.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');\n                        return _context2.abrupt(\"return\");\n                    case 3:\n                        _context2.prev = 3;\n                        console.log('🚀 Iniciando generación de resumen...');\n                        setTiempoEstimado(estimarTiempoGeneracion(documento));\n                        // Verificar si ya existe un resumen para este documento\n                        console.log('🔍 Verificando si ya existe resumen...');\n                        _context2.next = 9;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.existeResumenParaDocumento)(documento.id);\n                    case 9:\n                        existe = _context2.sent;\n                        if (!existe) {\n                            _context2.next = 14;\n                            break;\n                        }\n                        console.log('⚠️ Ya existe un resumen para este documento');\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');\n                        return _context2.abrupt(\"return\");\n                    case 14:\n                        console.log('✅ No existe resumen previo, continuando...');\n                        // Usar el sistema de tareas en segundo plano\n                        _context2.next = 17;\n                        return generateResumen({\n                            documento: documento,\n                            instrucciones: data.instrucciones,\n                            onComplete: function() {\n                                var _onComplete = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee(resumenContent) {\n                                    var resumenId;\n                                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                                        while(1)switch(_context.prev = _context.next){\n                                            case 0:\n                                                console.log('✅ Resumen generado, guardando en Supabase...');\n                                                // Guardar en Supabase\n                                                _context.next = 3;\n                                                return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.guardarResumen)(documento.id, \"Resumen: \".concat(documento.titulo), resumenContent, data.instrucciones);\n                                            case 3:\n                                                resumenId = _context.sent;\n                                                if (resumenId) {\n                                                    console.log('✅ Resumen guardado exitosamente con ID:', resumenId);\n                                                    onSummaryGenerated === null || onSummaryGenerated === void 0 || onSummaryGenerated(resumenId);\n                                                    reset();\n                                                } else {\n                                                    console.error('❌ Error al guardar el resumen - no se recibió ID');\n                                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al guardar el resumen en la base de datos');\n                                                }\n                                            case 5:\n                                            case \"end\":\n                                                return _context.stop();\n                                        }\n                                    }, _callee);\n                                }));\n                                function onComplete(_x2) {\n                                    return _onComplete.apply(this, arguments);\n                                }\n                                return onComplete;\n                            }(),\n                            onError: function onError(error) {\n                                console.error('❌ Error en generación de resumen:', error);\n                                setTiempoEstimado(0);\n                            }\n                        });\n                    case 17:\n                        _context2.next = 25;\n                        break;\n                    case 19:\n                        _context2.prev = 19;\n                        _context2.t0 = _context2[\"catch\"](3);\n                        console.error('Error al iniciar generación de resumen:', _context2.t0);\n                        errorMessage = _context2.t0 instanceof Error ? _context2.t0.message : 'Error al generar el resumen';\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(errorMessage);\n                        setTiempoEstimado(0);\n                    case 25:\n                    case \"end\":\n                        return _context2.stop();\n                }\n            }, _callee2, null, [\n                [\n                    3,\n                    19\n                ]\n            ]);\n        }));\n        return function onSubmit(_x) {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCC4 Generaci\\xF3n de Res\\xFAmenes\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    !puedeGenerar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Selecci\\xF3n incorrecta\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: documentosSeleccionados.length === 0 ? 'Debes seleccionar exactamente un documento para generar un resumen.' : \"Tienes \".concat(documentosSeleccionados.length, \" documentos seleccionados. Solo se permite generar un resumen por tema.\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this) : !validacionDocumento.valido ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Documento no v\\xE1lido\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: validacionDocumento.error\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u2705 Documento seleccionado:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                        children: documento.titulo\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    documento.numero_tema && \" (Tema \".concat(documento.numero_tema, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-blue-600\",\n                                children: [\n                                    \"Contenido: ~\",\n                                    documento.contenido.split(/\\s+/).length,\n                                    \" palabras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            puedeGenerar && validacionDocumento.valido && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"label\", {\n                                htmlFor: \"instrucciones\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Instrucciones para el resumen:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"textarea\", _objectSpread(_objectSpread({\n                                id: \"instrucciones\"\n                            }, register('instrucciones')), {}, {\n                                disabled: isCurrentlyGenerating,\n                                rows: 4,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical\",\n                                placeholder: \"Describe c\\xF3mo quieres que sea el resumen...\"\n                            }), void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            errors.instrucciones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.instrucciones.message\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isCurrentlyGenerating,\n                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        children: isCurrentlyGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generando resumen...\",\n                                tiempoEstimado > 0 && \" (~\".concat(tiempoEstimado, \"s)\")\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generar Resumen\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                    children: \"\\u23F3 Generando resumen...\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 238,\n                                    columnNumber: 56\n                                }, this),\n                                \"La IA est\\xE1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar \",\n                                tiempoEstimado > 0 ? \"aproximadamente \".concat(tiempoEstimado, \" segundos\") : 'unos momentos',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 9\n            }, this),\n            resumenGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-3\",\n                        children: \"\\uD83D\\uDCCB Resumen Generado\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: resumenGenerado.replace(/\\n/g, '<br />')\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-2\",\n                        children: \"\\u2705 Resumen guardado exitosamente. Puedes acceder a \\xE9l desde la secci\\xF3n de res\\xFAmenes.\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(SummaryGenerator, \"/vRDUXc96KupzW4DGJlc//3RXKo=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c1 = SummaryGenerator;\n_s1(SummaryGenerator, \"PSDV3WtsHhTMSACB9kYGm0nxOZM=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SummaryGenerator;\nvar _c;\n$RefreshReg$(_c, \"SummaryGenerator\");\nvar _c1;\n$RefreshReg$(_c1, \"SummaryGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\n"));

/***/ })

});