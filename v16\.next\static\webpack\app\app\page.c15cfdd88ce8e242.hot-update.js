"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TokenStatsModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenStatsModal)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/usePlanLimits */ \"(app-pages-browser)/./src/hooks/usePlanLimits.ts\");\n/* harmony import */ var _TokenProgressBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TokenProgressBar */ \"(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenStatsModal.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TokenStatsModal(_ref) {\n    _s();\n    _s1();\n    var _this = this;\n    var isOpen = _ref.isOpen, onClose = _ref.onClose, _ref$shouldRefreshOnO = _ref.shouldRefreshOnOpen, shouldRefreshOnOpen = _ref$shouldRefreshOnO === void 0 ? false : _ref$shouldRefreshOnO;\n    var planLimits = (0,_hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_2__.usePlanLimits)();\n    if (!isOpen) return null;\n    var formatTokens = function formatTokens(tokens) {\n        var validTokens = tokens || 0;\n        return validTokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiBarChart, {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Estad\\xEDsticas de Uso de IA\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiX, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: planLimits.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 46,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Verificando plan...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 47,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, this) : planLimits.userPlan === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(FiLock, {\n                                className: \"mx-auto h-16 w-16 text-gray-400 mb-6\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Estad\\xEDsticas de Tokens no disponibles\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"strong\", {\n                                            children: \"Plan Gratuito:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Las estad\\xEDsticas avanzadas y la compra de tokens est\\xE1n disponibles solo para usuarios con planes de pago.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 56,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 55,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Actualiza tu plan para acceder a:\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-700 space-y-2 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiBarChart, {\n                                                        className: \"w-4 h-4 text-blue-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 66,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Estad\\xEDsticas detalladas de uso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 65,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(FiActivity, {\n                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 70,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"An\\xE1lisis por actividad y modelo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 69,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(FiArrowUp, {\n                                                        className: \"w-4 h-4 text-purple-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 74,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seguimiento de progreso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(FiArrowUp, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 83,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ver Planes Disponibles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 79,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 78,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, this) : loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Cargando estad\\xEDsticas...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this) : stats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {\n                        children: [\n                            planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(_TokenProgressBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    used: planLimits.tokenUsage.current || 0,\n                                    limit: planLimits.tokenUsage.limit || 0,\n                                    percentage: planLimits.tokenUsage.percentage || 0,\n                                    remaining: planLimits.tokenUsage.remaining || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 99,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 98,\n                                columnNumber: 17\n                            }, this),\n                            planLimits.tokenUsage && (planLimits.tokenUsage.percentage || 0) >= 80 && planLimits.userPlan && planLimits.userPlan !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(TokenPurchaseButton, {\n                                    userPlan: planLimits.userPlan,\n                                    currentTokens: planLimits.tokenUsage.current || 0,\n                                    tokenLimit: planLimits.tokenUsage.limit || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 111,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 110,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(FiActivity, {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-blue-900\",\n                                                        children: \"Total Sesiones\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 124,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900 mt-1\",\n                                                children: stats.totalSessions\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiBarChart, {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-900\",\n                                                        children: \"Tokens Consumidos (Hist\\xF3rico)\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900 mt-1\",\n                                                children: formatTokens(stats.totalTokens)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 131,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Actividad\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Actividad\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 150,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 151,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 148,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byActivity).map(function(_ref2) {\n                                                        var _ref3 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref2, 2), activity = _ref3[0], data = _ref3[1];\n                                                        var activityData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                    children: activity\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 159,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: activityData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(activityData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, activity, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 158,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Modelo\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Modelo\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 176,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byModel).map(function(_ref4) {\n                                                        var _ref5 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref4, 2), model = _ref5[0], data = _ref5[1];\n                                                        var modelData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900 font-mono\",\n                                                                    children: model\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: modelData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(modelData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, model, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 186,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"strong\", {\n                                            children: \"Nota:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Los datos de uso de tokens se almacenan en Supabase. Los tokens de entrada y salida se registran autom\\xE1ticamente para cada actividad de IA.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 200,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 199,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No hay datos de uso disponibles.\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 208,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 207,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_5__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenStatsModal, \"3T7lKQz+yklv2M+OFaxFVArNDzI=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_2__.usePlanLimits\n    ];\n});\n_c1 = TokenStatsModal;\n_s1(TokenStatsModal, \"3T7lKQz+yklv2M+OFaxFVArNDzI=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_2__.usePlanLimits\n    ];\n});\n_c = TokenStatsModal;\nvar _c;\n$RefreshReg$(_c, \"TokenStatsModal\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenStatsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\n"));

/***/ })

});