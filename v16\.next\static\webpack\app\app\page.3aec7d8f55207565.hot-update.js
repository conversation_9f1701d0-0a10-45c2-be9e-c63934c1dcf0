"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/hooks/usePlanLimits.ts":
/*!************************************!*\
  !*** ./src/hooks/usePlanLimits.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useActionBlock: () => (/* binding */ useActionBlock),\n/* harmony export */   useFeatureAccess: () => (/* binding */ useFeatureAccess),\n/* harmony export */   useFeatureValidation: () => (/* binding */ useFeatureValidation),\n/* harmony export */   usePlanLimits: () => (/* binding */ usePlanLimits),\n/* harmony export */   useUpgradeInfo: () => (/* binding */ useUpgradeInfo),\n/* harmony export */   useUsageRecorder: () => (/* binding */ useUsageRecorder)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_services_permissionService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/permissionService */ \"(app-pages-browser)/./src/lib/services/permissionService.ts\");\n/* harmony import */ var _lib_services_limitHandler__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/limitHandler */ \"(app-pages-browser)/./src/lib/services/limitHandler.ts\");\n// src/hooks/usePlanLimits.ts\n// Hook React para validación de límites de plan\n/* __next_internal_client_entry_do_not_use__ usePlanLimits,useFeatureAccess,useActionBlock,useUsageRecorder,useUpgradeInfo,useFeatureValidation auto */ \n\nvar _s = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$();\n\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n/**\n * Hook principal para gestión de límites de plan\n */ function usePlanLimits() {\n    _s();\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        loading: true,\n        userPlan: null,\n        tokenUsage: null,\n        limits: [],\n        paymentVerified: false,\n        error: null\n    }), state = _useState[0], setState = _useState[1];\n    var loadLimits = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {\n        var supabase, _yield$supabase$auth$, user, authError, _yield$supabase$from$, profile, profileError, currentMonth, currentTokens, monthlyLimit, tokenUsage, limits;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n            while(1)switch(_context.prev = _context.next){\n                case 0:\n                    _context.prev = 0;\n                    setState({\n                        \"usePlanLimits.useCallback[loadLimits]._callee._callee$\": function(prev) {\n                            return _objectSpread(_objectSpread({}, prev), {}, {\n                                loading: true,\n                                error: null\n                            });\n                        }\n                    }[\"usePlanLimits.useCallback[loadLimits]._callee._callee$\"]);\n                    supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.createClient)();\n                    _context.next = 5;\n                    return supabase.auth.getUser();\n                case 5:\n                    _yield$supabase$auth$ = _context.sent;\n                    user = _yield$supabase$auth$.data.user;\n                    authError = _yield$supabase$auth$.error;\n                    if (!(authError || !user)) {\n                        _context.next = 11;\n                        break;\n                    }\n                    setState({\n                        \"usePlanLimits.useCallback[loadLimits]._callee._callee$\": function(prev) {\n                            return _objectSpread(_objectSpread({}, prev), {}, {\n                                loading: false,\n                                error: 'Usuario no autenticado'\n                            });\n                        }\n                    }[\"usePlanLimits.useCallback[loadLimits]._callee._callee$\"]);\n                    return _context.abrupt(\"return\");\n                case 11:\n                    _context.next = 13;\n                    return supabase.from('user_profiles').select('*').eq('user_id', user.id).single();\n                case 13:\n                    _yield$supabase$from$ = _context.sent;\n                    profile = _yield$supabase$from$.data;\n                    profileError = _yield$supabase$from$.error;\n                    if (!(profileError || !profile)) {\n                        _context.next = 19;\n                        break;\n                    }\n                    setState({\n                        \"usePlanLimits.useCallback[loadLimits]._callee._callee$\": function(prev) {\n                            return _objectSpread(_objectSpread({}, prev), {}, {\n                                loading: false,\n                                error: 'Perfil no encontrado'\n                            });\n                        }\n                    }[\"usePlanLimits.useCallback[loadLimits]._callee._callee$\"]);\n                    return _context.abrupt(\"return\");\n                case 19:\n                    // Calcular uso de tokens con validaciones defensivas\n                    currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                    currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens || 0 // Validar null/undefined\n                     : 0;\n                    monthlyLimit = profile.monthly_token_limit || 0; // Validar null/undefined\n                    tokenUsage = {\n                        current: currentTokens,\n                        limit: monthlyLimit,\n                        percentage: monthlyLimit > 0 ? Math.round(currentTokens / monthlyLimit * 100) : 0,\n                        // Evitar división por 0\n                        remaining: Math.max(0, monthlyLimit - currentTokens) // Evitar negativos\n                    }; // Verificar límites usando versión cliente\n                    _context.next = 25;\n                    return _lib_services_limitHandler__WEBPACK_IMPORTED_MODULE_6__.LimitHandler.checkClientUserLimits();\n                case 25:\n                    limits = _context.sent;\n                    setState({\n                        loading: false,\n                        userPlan: profile.subscription_plan,\n                        tokenUsage: tokenUsage,\n                        limits: limits,\n                        paymentVerified: profile.payment_verified || profile.subscription_plan === 'free',\n                        error: null\n                    });\n                    _context.next = 33;\n                    break;\n                case 29:\n                    _context.prev = 29;\n                    _context.t0 = _context[\"catch\"](0);\n                    console.error('Error loading plan limits:', _context.t0);\n                    setState({\n                        \"usePlanLimits.useCallback[loadLimits]._callee._callee$\": function(prev) {\n                            return _objectSpread(_objectSpread({}, prev), {}, {\n                                loading: false,\n                                error: 'Error cargando límites'\n                            });\n                        }\n                    }[\"usePlanLimits.useCallback[loadLimits]._callee._callee$\"]);\n                case 33:\n                case \"end\":\n                    return _context.stop();\n            }\n        }, _callee, null, [\n            [\n                0,\n                29\n            ]\n        ]);\n    })), []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"usePlanLimits.useEffect\": function() {\n            loadLimits();\n        }\n    }[\"usePlanLimits.useEffect\"], [\n        loadLimits\n    ]);\n    var refresh = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"usePlanLimits.useCallback[refresh]\": function() {\n            loadLimits();\n        }\n    }[\"usePlanLimits.useCallback[refresh]\"], [\n        loadLimits\n    ]);\n    return _objectSpread(_objectSpread({}, state), {}, {\n        refresh: refresh\n    });\n}\n/**\n * Hook para verificar acceso a una característica específica\n */ _s(usePlanLimits, \"LGqgpHSeN/pOMUCZE/AwdYvyQbQ=\");\nfunction useFeatureAccess(feature) {\n    _s2();\n    var tokensRequired = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var autoCheck = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        allowed: false,\n        loading: true\n    }), result = _useState2[0], setResult = _useState2[1];\n    var checkAccess = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2() {\n        var permission, permissionResult;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee2$(_context2) {\n            while(1)switch(_context2.prev = _context2.next){\n                case 0:\n                    _context2.prev = 0;\n                    setResult({\n                        \"useFeatureAccess.useCallback[checkAccess]._callee2._callee2$\": function(prev) {\n                            return _objectSpread(_objectSpread({}, prev), {}, {\n                                loading: true\n                            });\n                        }\n                    }[\"useFeatureAccess.useCallback[checkAccess]._callee2._callee2$\"]);\n                    permission = _lib_services_permissionService__WEBPACK_IMPORTED_MODULE_5__.PermissionService.createFeaturePermission(feature, tokensRequired);\n                    _context2.next = 5;\n                    return _lib_services_permissionService__WEBPACK_IMPORTED_MODULE_5__.PermissionService.checkClientPermission(permission);\n                case 5:\n                    permissionResult = _context2.sent;\n                    setResult({\n                        allowed: permissionResult.granted,\n                        loading: false,\n                        reason: permissionResult.reason,\n                        upgradeRequired: permissionResult.upgradeRequired,\n                        suggestedPlan: permissionResult.suggestedPlan,\n                        tokenInfo: permissionResult.tokenInfo\n                    });\n                    _context2.next = 13;\n                    break;\n                case 9:\n                    _context2.prev = 9;\n                    _context2.t0 = _context2[\"catch\"](0);\n                    console.error('Error checking feature access:', _context2.t0);\n                    setResult({\n                        allowed: false,\n                        loading: false,\n                        reason: 'Error verificando acceso'\n                    });\n                case 13:\n                case \"end\":\n                    return _context2.stop();\n            }\n        }, _callee2, null, [\n            [\n                0,\n                9\n            ]\n        ]);\n    })), [\n        feature,\n        tokensRequired\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useFeatureAccess.useEffect\": function() {\n            if (autoCheck) {\n                checkAccess();\n            }\n        }\n    }[\"useFeatureAccess.useEffect\"], [\n        autoCheck,\n        checkAccess\n    ]);\n    return _objectSpread(_objectSpread({}, result), {}, {\n        checkAccess: checkAccess\n    });\n}\n/**\n * Hook para verificar si una acción está bloqueada\n */ _s2(useFeatureAccess, \"GjGSedcH2CPooW+muuceXwI5ci0=\");\nfunction useActionBlock(action) {\n    _s3();\n    var tokensRequired = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)({\n        isBlocked: false,\n        loading: true\n    }), blocked = _useState3[0], setBlocked = _useState3[1];\n    var checkBlock = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee3() {\n        var supabase, _yield$supabase$auth$2, user, error, blockResult;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee3$(_context3) {\n            while(1)switch(_context3.prev = _context3.next){\n                case 0:\n                    _context3.prev = 0;\n                    setBlocked({\n                        \"useActionBlock.useCallback[checkBlock]._callee3._callee3$\": function(prev) {\n                            return _objectSpread(_objectSpread({}, prev), {}, {\n                                loading: true\n                            });\n                        }\n                    }[\"useActionBlock.useCallback[checkBlock]._callee3._callee3$\"]);\n                    supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_4__.createClient)();\n                    _context3.next = 5;\n                    return supabase.auth.getUser();\n                case 5:\n                    _yield$supabase$auth$2 = _context3.sent;\n                    user = _yield$supabase$auth$2.data.user;\n                    error = _yield$supabase$auth$2.error;\n                    if (!(error || !user)) {\n                        _context3.next = 11;\n                        break;\n                    }\n                    setBlocked({\n                        isBlocked: true,\n                        loading: false,\n                        reason: 'Usuario no autenticado'\n                    });\n                    return _context3.abrupt(\"return\");\n                case 11:\n                    _context3.next = 13;\n                    return _lib_services_limitHandler__WEBPACK_IMPORTED_MODULE_6__.LimitHandler.isClientActionBlocked(action, tokensRequired);\n                case 13:\n                    blockResult = _context3.sent;\n                    setBlocked({\n                        isBlocked: blockResult.blocked,\n                        loading: false,\n                        reason: blockResult.reason,\n                        limitStatus: blockResult.limitStatus\n                    });\n                    _context3.next = 21;\n                    break;\n                case 17:\n                    _context3.prev = 17;\n                    _context3.t0 = _context3[\"catch\"](0);\n                    console.error('Error checking action block:', _context3.t0);\n                    setBlocked({\n                        isBlocked: true,\n                        loading: false,\n                        reason: 'Error verificando bloqueo'\n                    });\n                case 21:\n                case \"end\":\n                    return _context3.stop();\n            }\n        }, _callee3, null, [\n            [\n                0,\n                17\n            ]\n        ]);\n    })), [\n        action,\n        tokensRequired\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"useActionBlock.useEffect\": function() {\n            checkBlock();\n        }\n    }[\"useActionBlock.useEffect\"], [\n        checkBlock\n    ]);\n    return _objectSpread(_objectSpread({}, blocked), {}, {\n        refresh: checkBlock\n    });\n}\n/**\n * Hook para registrar uso después de una acción\n */ _s3(useActionBlock, \"71Q3DMqMl1CHLqp47pKcxsNmiQE=\");\nfunction useUsageRecorder() {\n    _s4();\n    var recordUsage = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ ({\n        \"useUsageRecorder.useCallback[recordUsage]\": function() {\n            var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee4(action, tokensUsed) {\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee4$(_context4) {\n                    while(1)switch(_context4.prev = _context4.next){\n                        case 0:\n                            _context4.prev = 0;\n                            _context4.next = 3;\n                            return _lib_services_limitHandler__WEBPACK_IMPORTED_MODULE_6__.LimitHandler.recordClientUsage(action, tokensUsed);\n                        case 3:\n                            _context4.next = 8;\n                            break;\n                        case 5:\n                            _context4.prev = 5;\n                            _context4.t0 = _context4[\"catch\"](0);\n                            console.error('Error recording usage:', _context4.t0);\n                        case 8:\n                        case \"end\":\n                            return _context4.stop();\n                    }\n                }, _callee4, null, [\n                    [\n                        0,\n                        5\n                    ]\n                ]);\n            }));\n            return ({\n                \"useUsageRecorder.useCallback[recordUsage]\": function(_x, _x2) {\n                    return _ref4.apply(this, arguments);\n                }\n            })[\"useUsageRecorder.useCallback[recordUsage]\"];\n        }\n    })[\"useUsageRecorder.useCallback[recordUsage]\"](), []);\n    return {\n        recordUsage: recordUsage\n    };\n}\n/**\n * Hook para obtener información de upgrade\n */ _s4(useUsageRecorder, \"LZMc6KzeeHgVZkv1EkoMlUUDtgw=\");\nfunction useUpgradeInfo() {\n    _s5();\n    var _usePlanLimits = usePlanLimits(), userPlan = _usePlanLimits.userPlan, limits = _usePlanLimits.limits;\n    var getUpgradeRecommendation = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useUpgradeInfo.useCallback[getUpgradeRecommendation]\": function() {\n            if (!userPlan) return null;\n            // Buscar límites que requieren acción\n            var criticalLimit = limits.find({\n                \"useUpgradeInfo.useCallback[getUpgradeRecommendation].criticalLimit\": function(l) {\n                    return l.severity === 'exceeded' || l.severity === 'limit_reached';\n                }\n            }[\"useUpgradeInfo.useCallback[getUpgradeRecommendation].criticalLimit\"]);\n            if (criticalLimit && criticalLimit.upgradeOptions && criticalLimit.upgradeOptions.length > 0) {\n                return {\n                    reason: criticalLimit.message,\n                    suggestedPlan: criticalLimit.upgradeOptions[0].plan,\n                    benefits: criticalLimit.upgradeOptions[0].benefits,\n                    newLimit: criticalLimit.upgradeOptions[0].newLimit,\n                    urgency: criticalLimit.severity\n                };\n            }\n            return null;\n        }\n    }[\"useUpgradeInfo.useCallback[getUpgradeRecommendation]\"], [\n        userPlan,\n        limits\n    ]);\n    var shouldShowUpgradePrompt = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)({\n        \"useUpgradeInfo.useCallback[shouldShowUpgradePrompt]\": function() {\n            return limits.some({\n                \"useUpgradeInfo.useCallback[shouldShowUpgradePrompt]\": function(l) {\n                    return l.actionRequired;\n                }\n            }[\"useUpgradeInfo.useCallback[shouldShowUpgradePrompt]\"]);\n        }\n    }[\"useUpgradeInfo.useCallback[shouldShowUpgradePrompt]\"], [\n        limits\n    ]);\n    return {\n        getUpgradeRecommendation: getUpgradeRecommendation,\n        shouldShowUpgradePrompt: shouldShowUpgradePrompt,\n        upgradeUrl: '/upgrade-plan'\n    };\n}\n/**\n * Hook combinado para validación completa de características\n */ _s5(useUpgradeInfo, \"gr0zrkl9XkdtpC7sHRKgmkuULJ8=\", false, function() {\n    return [\n        usePlanLimits\n    ];\n});\nfunction useFeatureValidation(feature) {\n    _s6();\n    var tokensRequired = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    var planLimits = usePlanLimits();\n    var featureAccess = useFeatureAccess(feature, tokensRequired);\n    var actionBlock = useActionBlock(feature, tokensRequired);\n    var _useUsageRecorder = useUsageRecorder(), recordUsage = _useUsageRecorder.recordUsage;\n    var upgradeInfo = useUpgradeInfo();\n    var canUseFeature = !actionBlock.isBlocked && featureAccess.allowed;\n    var loading = planLimits.loading || featureAccess.loading || actionBlock.loading;\n    var executeWithValidation = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(/*#__PURE__*/ ({\n        \"useFeatureValidation.useCallback[executeWithValidation]\": function() {\n            var _ref5 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee5(action, actualTokensUsed) {\n                var result;\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee5$(_context5) {\n                    while(1)switch(_context5.prev = _context5.next){\n                        case 0:\n                            if (canUseFeature) {\n                                _context5.next = 2;\n                                break;\n                            }\n                            throw new Error(actionBlock.reason || featureAccess.reason || 'Acceso denegado');\n                        case 2:\n                            _context5.prev = 2;\n                            _context5.next = 5;\n                            return action();\n                        case 5:\n                            result = _context5.sent;\n                            if (!(actualTokensUsed && actualTokensUsed > 0)) {\n                                _context5.next = 10;\n                                break;\n                            }\n                            _context5.next = 9;\n                            return recordUsage(feature, actualTokensUsed);\n                        case 9:\n                            planLimits.refresh(); // Refrescar límites después del uso\n                        case 10:\n                            return _context5.abrupt(\"return\", result);\n                        case 13:\n                            _context5.prev = 13;\n                            _context5.t0 = _context5[\"catch\"](2);\n                            console.error('Error executing validated action:', _context5.t0);\n                            throw _context5.t0;\n                        case 17:\n                        case \"end\":\n                            return _context5.stop();\n                    }\n                }, _callee5, null, [\n                    [\n                        2,\n                        13\n                    ]\n                ]);\n            }));\n            return ({\n                \"useFeatureValidation.useCallback[executeWithValidation]\": function(_x3, _x4) {\n                    return _ref5.apply(this, arguments);\n                }\n            })[\"useFeatureValidation.useCallback[executeWithValidation]\"];\n        }\n    })[\"useFeatureValidation.useCallback[executeWithValidation]\"](), [\n        canUseFeature,\n        actionBlock.reason,\n        featureAccess.reason,\n        recordUsage,\n        feature,\n        planLimits\n    ]);\n    return {\n        canUseFeature: canUseFeature,\n        loading: loading,\n        reason: actionBlock.reason || featureAccess.reason,\n        planLimits: planLimits,\n        featureAccess: featureAccess,\n        actionBlock: actionBlock,\n        upgradeInfo: upgradeInfo,\n        executeWithValidation: executeWithValidation,\n        refresh: function refresh() {\n            planLimits.refresh();\n            featureAccess.checkAccess();\n            actionBlock.refresh();\n        }\n    };\n}\n_s6(useFeatureValidation, \"OxbaBP08Ww6yUfE1ST//ztjqP4U=\", false, function() {\n    return [\n        usePlanLimits,\n        useFeatureAccess,\n        useActionBlock,\n        useUsageRecorder,\n        useUpgradeInfo\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/usePlanLimits.ts\n"));

/***/ })

});