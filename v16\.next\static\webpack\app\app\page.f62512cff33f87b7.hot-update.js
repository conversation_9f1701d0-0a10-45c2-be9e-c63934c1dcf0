"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canPerformActivity: () => (/* binding */ canPerformActivity),\n/* harmony export */   checkTokenLimit: () => (/* binding */ checkTokenLimit),\n/* harmony export */   getTokenPurchaseHistory: () => (/* binding */ getTokenPurchaseHistory),\n/* harmony export */   getTokenUsageProgress: () => (/* binding */ getTokenUsageProgress),\n/* harmony export */   getUserPlanInfo: () => (/* binding */ getUserPlanInfo),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserTokenStats: () => (/* binding */ getUserTokenStats),\n/* harmony export */   saveTokenUsage: () => (/* binding */ saveTokenUsage)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n\n\n\n\n/**\n * Guarda el uso de tokens en Supabase con validación de plan\n */ function saveTokenUsage(_x) {\n    return _saveTokenUsage.apply(this, arguments);\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ function _saveTokenUsage() {\n    _saveTokenUsage = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(data) {\n        var supabase, _yield$supabase$auth$, user, userError, accessValidation, usageRecord, _yield$supabase$from$, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n            while(1)switch(_context.prev = _context.next){\n                case 0:\n                    _context.prev = 0;\n                    console.log('🔄 saveTokenUsage (cliente) iniciado con data:', data);\n                    // Este servicio solo funciona en el cliente\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    console.log('✅ Cliente Supabase creado');\n                    _context.next = 6;\n                    return supabase.auth.getUser();\n                case 6:\n                    _yield$supabase$auth$ = _context.sent;\n                    user = _yield$supabase$auth$.data.user;\n                    userError = _yield$supabase$auth$.error;\n                    console.log('👤 Usuario obtenido:', user ? \"ID: \".concat(user.id, \", Email: \").concat(user.email) : 'No autenticado');\n                    if (!(userError || !user)) {\n                        _context.next = 13;\n                        break;\n                    }\n                    console.warn('❌ No hay usuario autenticado para guardar tokens:', userError === null || userError === void 0 ? void 0 : userError.message);\n                    return _context.abrupt(\"return\");\n                case 13:\n                    _context.next = 15;\n                    return validateActivityAccess(user.id, data.activity, data.usage.totalTokens);\n                case 15:\n                    accessValidation = _context.sent;\n                    if (accessValidation.allowed) {\n                        _context.next = 19;\n                        break;\n                    }\n                    console.warn('❌ Acceso denegado para actividad:', accessValidation.reason);\n                    throw new Error(accessValidation.reason);\n                case 19:\n                    usageRecord = {\n                        user_id: user.id,\n                        activity_type: data.activity,\n                        model_name: data.model,\n                        prompt_tokens: data.usage.promptTokens,\n                        completion_tokens: data.usage.completionTokens,\n                        total_tokens: data.usage.totalTokens,\n                        estimated_cost: data.usage.estimatedCost || 0,\n                        usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n                    };\n                    console.log('📝 Registro a insertar:', usageRecord);\n                    _context.next = 23;\n                    return supabase.from('user_token_usage').insert([\n                        usageRecord\n                    ]);\n                case 23:\n                    _yield$supabase$from$ = _context.sent;\n                    error = _yield$supabase$from$.error;\n                    if (!error) {\n                        _context.next = 28;\n                        break;\n                    }\n                    console.error('❌ Error al guardar uso de tokens:', error);\n                    return _context.abrupt(\"return\");\n                case 28:\n                    console.log('✅ Registro insertado exitosamente en user_token_usage');\n                    // Actualizar contador mensual del usuario\n                    _context.next = 31;\n                    return updateMonthlyTokenCount(user.id, data.usage.totalTokens);\n                case 31:\n                    _context.next = 36;\n                    break;\n                case 33:\n                    _context.prev = 33;\n                    _context.t0 = _context[\"catch\"](0);\n                    console.error('Error en saveTokenUsage:', _context.t0);\n                case 36:\n                case \"end\":\n                    return _context.stop();\n            }\n        }, _callee, null, [\n            [\n                0,\n                33\n            ]\n        ]);\n    }));\n    return _saveTokenUsage.apply(this, arguments);\n}\nfunction updateMonthlyTokenCount(_x2, _x3) {\n    return _updateMonthlyTokenCount.apply(this, arguments);\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario actual\n */ function _updateMonthlyTokenCount() {\n    _updateMonthlyTokenCount = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(userId, tokens) {\n        var supabase, currentMonth, _yield$supabase$from$2, profile, profileError, _yield$supabase$from$3, insertError, newTokenCount, _yield$supabase$from$4, updateError;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee2$(_context2) {\n            while(1)switch(_context2.prev = _context2.next){\n                case 0:\n                    _context2.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    currentMonth = new Date().toISOString().slice(0, 7) + '-01'; // Obtener o crear perfil del usuario\n                    _context2.next = 5;\n                    return supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n                case 5:\n                    _yield$supabase$from$2 = _context2.sent;\n                    profile = _yield$supabase$from$2.data;\n                    profileError = _yield$supabase$from$2.error;\n                    if (!(profileError && profileError.code !== 'PGRST116')) {\n                        _context2.next = 11;\n                        break;\n                    }\n                    console.error('Error al obtener perfil:', profileError);\n                    return _context2.abrupt(\"return\");\n                case 11:\n                    if (profile) {\n                        _context2.next = 19;\n                        break;\n                    }\n                    _context2.next = 14;\n                    return supabase.from('user_profiles').insert([\n                        {\n                            user_id: userId,\n                            subscription_plan: 'free',\n                            monthly_token_limit: 50000,\n                            current_month_tokens: tokens,\n                            current_month: currentMonth\n                        }\n                    ]);\n                case 14:\n                    _yield$supabase$from$3 = _context2.sent;\n                    insertError = _yield$supabase$from$3.error;\n                    if (insertError) {\n                        console.error('Error al crear perfil:', insertError);\n                    }\n                    _context2.next = 25;\n                    break;\n                case 19:\n                    // Actualizar perfil existente\n                    newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n                    _context2.next = 22;\n                    return supabase.from('user_profiles').update({\n                        current_month_tokens: newTokenCount,\n                        current_month: currentMonth,\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', userId);\n                case 22:\n                    _yield$supabase$from$4 = _context2.sent;\n                    updateError = _yield$supabase$from$4.error;\n                    if (updateError) {\n                        console.error('Error al actualizar perfil:', updateError);\n                    }\n                case 25:\n                    _context2.next = 30;\n                    break;\n                case 27:\n                    _context2.prev = 27;\n                    _context2.t0 = _context2[\"catch\"](0);\n                    console.error('Error en updateMonthlyTokenCount:', _context2.t0);\n                case 30:\n                case \"end\":\n                    return _context2.stop();\n            }\n        }, _callee2, null, [\n            [\n                0,\n                27\n            ]\n        ]);\n    }));\n    return _updateMonthlyTokenCount.apply(this, arguments);\n}\nfunction getUserTokenStats() {\n    return _getUserTokenStats.apply(this, arguments);\n}\n/**\n * Calcula estadísticas a partir de los registros\n */ function _getUserTokenStats() {\n    _getUserTokenStats = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee3() {\n        var supabase, _yield$supabase$auth$2, user, userError, _yield$supabase$from$5, records, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee3$(_context3) {\n            while(1)switch(_context3.prev = _context3.next){\n                case 0:\n                    _context3.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context3.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$2 = _context3.sent;\n                    user = _yield$supabase$auth$2.data.user;\n                    userError = _yield$supabase$auth$2.error;\n                    if (!(userError || !user)) {\n                        _context3.next = 9;\n                        break;\n                    }\n                    return _context3.abrupt(\"return\", getEmptyStats());\n                case 9:\n                    _context3.next = 11;\n                    return supabase.from('user_token_usage').select('*').eq('user_id', user.id).order('created_at', {\n                        ascending: false\n                    });\n                case 11:\n                    _yield$supabase$from$5 = _context3.sent;\n                    records = _yield$supabase$from$5.data;\n                    error = _yield$supabase$from$5.error;\n                    if (!error) {\n                        _context3.next = 17;\n                        break;\n                    }\n                    console.error('Error al obtener estadísticas:', error);\n                    return _context3.abrupt(\"return\", getEmptyStats());\n                case 17:\n                    return _context3.abrupt(\"return\", calculateStats(records || []));\n                case 20:\n                    _context3.prev = 20;\n                    _context3.t0 = _context3[\"catch\"](0);\n                    console.error('Error en getUserTokenStats:', _context3.t0);\n                    return _context3.abrupt(\"return\", getEmptyStats());\n                case 24:\n                case \"end\":\n                    return _context3.stop();\n            }\n        }, _callee3, null, [\n            [\n                0,\n                20\n            ]\n        ]);\n    }));\n    return _getUserTokenStats.apply(this, arguments);\n}\nfunction calculateStats(records) {\n    var stats = {\n        totalSessions: records.length,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n    records.forEach(function(record) {\n        var tokens = record.total_tokens;\n        var cost = record.estimated_cost;\n        stats.totalTokens += tokens;\n        stats.totalCost += cost;\n        // Por actividad\n        if (!stats.byActivity[record.activity_type]) {\n            stats.byActivity[record.activity_type] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byActivity[record.activity_type].tokens += tokens;\n        stats.byActivity[record.activity_type].cost += cost;\n        stats.byActivity[record.activity_type].count += 1;\n        // Por modelo\n        if (!stats.byModel[record.model_name]) {\n            stats.byModel[record.model_name] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byModel[record.model_name].tokens += tokens;\n        stats.byModel[record.model_name].cost += cost;\n        stats.byModel[record.model_name].count += 1;\n    });\n    return stats;\n}\n/**\n * Retorna estadísticas vacías\n */ function getEmptyStats() {\n    return {\n        totalSessions: 0,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n}\n/**\n * Obtiene el perfil del usuario actual\n */ function getUserProfile() {\n    return _getUserProfile.apply(this, arguments);\n}\n/**\n * Verifica si el usuario ha alcanzado su límite mensual\n */ function _getUserProfile() {\n    _getUserProfile = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee4() {\n        var supabase, _yield$supabase$auth$3, user, userError, _yield$supabase$from$6, profile, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee4$(_context4) {\n            while(1)switch(_context4.prev = _context4.next){\n                case 0:\n                    _context4.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context4.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$3 = _context4.sent;\n                    user = _yield$supabase$auth$3.data.user;\n                    userError = _yield$supabase$auth$3.error;\n                    if (!(userError || !user)) {\n                        _context4.next = 9;\n                        break;\n                    }\n                    return _context4.abrupt(\"return\", null);\n                case 9:\n                    _context4.next = 11;\n                    return supabase.from('user_profiles').select('*').eq('user_id', user.id).single();\n                case 11:\n                    _yield$supabase$from$6 = _context4.sent;\n                    profile = _yield$supabase$from$6.data;\n                    error = _yield$supabase$from$6.error;\n                    if (!(error && error.code !== 'PGRST116')) {\n                        _context4.next = 17;\n                        break;\n                    }\n                    console.error('Error al obtener perfil:', error);\n                    return _context4.abrupt(\"return\", null);\n                case 17:\n                    return _context4.abrupt(\"return\", profile);\n                case 20:\n                    _context4.prev = 20;\n                    _context4.t0 = _context4[\"catch\"](0);\n                    console.error('Error en getUserProfile:', _context4.t0);\n                    return _context4.abrupt(\"return\", null);\n                case 24:\n                case \"end\":\n                    return _context4.stop();\n            }\n        }, _callee4, null, [\n            [\n                0,\n                20\n            ]\n        ]);\n    }));\n    return _getUserProfile.apply(this, arguments);\n}\nfunction checkTokenLimit() {\n    return _checkTokenLimit.apply(this, arguments);\n}\n/**\n * Valida si un usuario tiene acceso a una actividad específica\n */ function _checkTokenLimit() {\n    _checkTokenLimit = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee5() {\n        var profile, currentTokens, monthlyLimit, percentage, hasReachedLimit;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee5$(_context5) {\n            while(1)switch(_context5.prev = _context5.next){\n                case 0:\n                    _context5.prev = 0;\n                    _context5.next = 3;\n                    return getUserProfile();\n                case 3:\n                    profile = _context5.sent;\n                    if (profile) {\n                        _context5.next = 6;\n                        break;\n                    }\n                    return _context5.abrupt(\"return\", {\n                        hasReachedLimit: false,\n                        currentTokens: 0,\n                        limit: 50000,\n                        percentage: 0\n                    });\n                case 6:\n                    currentTokens = profile.current_month_tokens || 0;\n                    monthlyLimit = profile.monthly_token_limit || 0;\n                    percentage = monthlyLimit > 0 ? currentTokens / monthlyLimit * 100 : 0;\n                    hasReachedLimit = currentTokens >= monthlyLimit;\n                    return _context5.abrupt(\"return\", {\n                        hasReachedLimit: hasReachedLimit,\n                        currentTokens: currentTokens,\n                        limit: monthlyLimit,\n                        percentage: percentage\n                    });\n                case 13:\n                    _context5.prev = 13;\n                    _context5.t0 = _context5[\"catch\"](0);\n                    console.error('Error en checkTokenLimit:', _context5.t0);\n                    return _context5.abrupt(\"return\", {\n                        hasReachedLimit: false,\n                        currentTokens: 0,\n                        limit: 50000,\n                        percentage: 0\n                    });\n                case 17:\n                case \"end\":\n                    return _context5.stop();\n            }\n        }, _callee5, null, [\n            [\n                0,\n                13\n            ]\n        ]);\n    }));\n    return _checkTokenLimit.apply(this, arguments);\n}\nfunction validateActivityAccess(_x4, _x5, _x6) {\n    return _validateActivityAccess.apply(this, arguments);\n}\n/**\n * Obtiene información detallada del plan del usuario\n */ function _validateActivityAccess() {\n    _validateActivityAccess = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee6(userId, activity, tokensToUse) {\n        var supabase, _yield$supabase$from$7, profile, error, activityToFeature, featureName, currentMonth, currentTokens;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee6$(_context6) {\n            while(1)switch(_context6.prev = _context6.next){\n                case 0:\n                    _context6.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)(); // Obtener perfil del usuario\n                    _context6.next = 4;\n                    return supabase.from('user_profiles').select('subscription_plan, payment_verified, current_month_tokens, monthly_token_limit, current_month').eq('user_id', userId).single();\n                case 4:\n                    _yield$supabase$from$7 = _context6.sent;\n                    profile = _yield$supabase$from$7.data;\n                    error = _yield$supabase$from$7.error;\n                    if (!(error || !profile)) {\n                        _context6.next = 9;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Perfil de usuario no encontrado'\n                    });\n                case 9:\n                    // Mapear actividades a características\n                    activityToFeature = {\n                        'test_generation': 'test_generation',\n                        'flashcard_generation': 'flashcard_generation',\n                        'mind_map_generation': 'mind_map_generation',\n                        'ai_chat': 'ai_tutor_chat',\n                        'study_planning': 'study_planning',\n                        'summary_generation': 'summary_a1_a2',\n                        'document_analysis': 'document_upload'\n                    };\n                    featureName = activityToFeature[activity] || activity; // Verificar acceso a la característica según el plan\n                    if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_3__.hasFeatureAccess)(profile.subscription_plan, featureName)) {\n                        _context6.next = 13;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: \"La actividad \".concat(activity, \" no est\\xE1 disponible en el plan \").concat(profile.subscription_plan)\n                    });\n                case 13:\n                    if (!(profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                        _context6.next = 15;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Pago no verificado. Complete el proceso de pago para usar esta función.'\n                    });\n                case 15:\n                    // Verificar límites de tokens\n                    currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                    currentTokens = profile.current_month_tokens; // Reset si es nuevo mes\n                    if (profile.current_month !== currentMonth) {\n                        currentTokens = 0;\n                    }\n                    if (!(currentTokens + tokensToUse > profile.monthly_token_limit)) {\n                        _context6.next = 20;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: \"L\\xEDmite mensual de tokens alcanzado. Usado: \".concat(currentTokens, \"/\").concat(profile.monthly_token_limit)\n                    });\n                case 20:\n                    return _context6.abrupt(\"return\", {\n                        allowed: true\n                    });\n                case 23:\n                    _context6.prev = 23;\n                    _context6.t0 = _context6[\"catch\"](0);\n                    console.error('Error validating activity access:', _context6.t0);\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Error interno de validación'\n                    });\n                case 27:\n                case \"end\":\n                    return _context6.stop();\n            }\n        }, _callee6, null, [\n            [\n                0,\n                23\n            ]\n        ]);\n    }));\n    return _validateActivityAccess.apply(this, arguments);\n}\nfunction getUserPlanInfo() {\n    return _getUserPlanInfo.apply(this, arguments);\n}\n/**\n * Verifica si el usuario puede realizar una actividad específica antes de ejecutarla\n */ function _getUserPlanInfo() {\n    _getUserPlanInfo = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee7() {\n        var profile, planConfig, currentTokens, monthlyLimit, percentage;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee7$(_context7) {\n            while(1)switch(_context7.prev = _context7.next){\n                case 0:\n                    _context7.prev = 0;\n                    _context7.next = 3;\n                    return getUserProfile();\n                case 3:\n                    profile = _context7.sent;\n                    if (profile) {\n                        _context7.next = 6;\n                        break;\n                    }\n                    return _context7.abrupt(\"return\", null);\n                case 6:\n                    planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_3__.getPlanConfiguration)(profile.subscription_plan);\n                    if (planConfig) {\n                        _context7.next = 9;\n                        break;\n                    }\n                    return _context7.abrupt(\"return\", null);\n                case 9:\n                    currentTokens = profile.current_month_tokens || 0;\n                    monthlyLimit = profile.monthly_token_limit || 0;\n                    percentage = monthlyLimit > 0 ? currentTokens / monthlyLimit * 100 : 0;\n                    return _context7.abrupt(\"return\", {\n                        plan: profile.subscription_plan,\n                        planName: planConfig.name,\n                        features: planConfig.features,\n                        tokenUsage: {\n                            current: currentTokens,\n                            limit: monthlyLimit,\n                            percentage: Math.round(percentage),\n                            remaining: Math.max(0, monthlyLimit - currentTokens)\n                        },\n                        paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n                    });\n                case 15:\n                    _context7.prev = 15;\n                    _context7.t0 = _context7[\"catch\"](0);\n                    console.error('Error getting user plan info:', _context7.t0);\n                    return _context7.abrupt(\"return\", null);\n                case 19:\n                case \"end\":\n                    return _context7.stop();\n            }\n        }, _callee7, null, [\n            [\n                0,\n                15\n            ]\n        ]);\n    }));\n    return _getUserPlanInfo.apply(this, arguments);\n}\nfunction canPerformActivity(_x7) {\n    return _canPerformActivity.apply(this, arguments);\n}\n/**\n * Obtiene datos de progreso de tokens para estadísticas avanzadas\n */ function _canPerformActivity() {\n    _canPerformActivity = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee8(activity) {\n        var estimatedTokens, supabase, _yield$supabase$auth$4, user, userError, validation, planInfo, _args8 = arguments;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee8$(_context8) {\n            while(1)switch(_context8.prev = _context8.next){\n                case 0:\n                    estimatedTokens = _args8.length > 1 && _args8[1] !== undefined ? _args8[1] : 0;\n                    _context8.prev = 1;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context8.next = 5;\n                    return supabase.auth.getUser();\n                case 5:\n                    _yield$supabase$auth$4 = _context8.sent;\n                    user = _yield$supabase$auth$4.data.user;\n                    userError = _yield$supabase$auth$4.error;\n                    if (!(userError || !user)) {\n                        _context8.next = 10;\n                        break;\n                    }\n                    return _context8.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Usuario no autenticado'\n                    });\n                case 10:\n                    _context8.next = 12;\n                    return validateActivityAccess(user.id, activity, estimatedTokens);\n                case 12:\n                    validation = _context8.sent;\n                    if (validation.allowed) {\n                        _context8.next = 18;\n                        break;\n                    }\n                    _context8.next = 16;\n                    return getUserPlanInfo();\n                case 16:\n                    planInfo = _context8.sent;\n                    return _context8.abrupt(\"return\", {\n                        allowed: false,\n                        reason: validation.reason,\n                        planInfo: planInfo\n                    });\n                case 18:\n                    return _context8.abrupt(\"return\", {\n                        allowed: true\n                    });\n                case 21:\n                    _context8.prev = 21;\n                    _context8.t0 = _context8[\"catch\"](1);\n                    console.error('Error checking activity permission:', _context8.t0);\n                    return _context8.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Error interno de validación'\n                    });\n                case 25:\n                case \"end\":\n                    return _context8.stop();\n            }\n        }, _callee8, null, [\n            [\n                1,\n                21\n            ]\n        ]);\n    }));\n    return _canPerformActivity.apply(this, arguments);\n}\nfunction getTokenUsageProgress() {\n    return _getTokenUsageProgress.apply(this, arguments);\n}\n/**\n * Obtiene historial de compras de tokens del usuario\n */ function _getTokenUsageProgress() {\n    _getTokenUsageProgress = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee9() {\n        var supabase, _yield$supabase$auth$5, user, userError, profile, percentage, thirtyDaysAgo, _yield$supabase$from$8, dailyUsage, historyError, dailyHistory, dailyMap, i, date, dateStr;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee9$(_context9) {\n            while(1)switch(_context9.prev = _context9.next){\n                case 0:\n                    _context9.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context9.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$5 = _context9.sent;\n                    user = _yield$supabase$auth$5.data.user;\n                    userError = _yield$supabase$auth$5.error;\n                    if (!(userError || !user)) {\n                        _context9.next = 9;\n                        break;\n                    }\n                    return _context9.abrupt(\"return\", null);\n                case 9:\n                    _context9.next = 11;\n                    return getUserProfile();\n                case 11:\n                    profile = _context9.sent;\n                    if (profile) {\n                        _context9.next = 14;\n                        break;\n                    }\n                    return _context9.abrupt(\"return\", null);\n                case 14:\n                    // Calcular porcentaje de uso\n                    percentage = profile.current_month_tokens / profile.monthly_token_limit * 100; // Obtener historial diario de los últimos 30 días\n                    thirtyDaysAgo = new Date();\n                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n                    _context9.next = 19;\n                    return supabase.from('user_token_usage').select('created_at, total_tokens').eq('user_id', user.id).gte('created_at', thirtyDaysAgo.toISOString()).order('created_at', {\n                        ascending: true\n                    });\n                case 19:\n                    _yield$supabase$from$8 = _context9.sent;\n                    dailyUsage = _yield$supabase$from$8.data;\n                    historyError = _yield$supabase$from$8.error;\n                    if (historyError) {\n                        console.error('Error al obtener historial diario:', historyError);\n                    }\n                    // Agrupar por día\n                    dailyHistory = [];\n                    dailyMap = new Map();\n                    if (dailyUsage) {\n                        dailyUsage.forEach(function(record) {\n                            var date = new Date(record.created_at).toISOString().split('T')[0];\n                            var currentTokens = dailyMap.get(date) || 0;\n                            dailyMap.set(date, currentTokens + record.total_tokens);\n                        });\n                        // Convertir a array ordenado\n                        for(i = 29; i >= 0; i--){\n                            date = new Date();\n                            date.setDate(date.getDate() - i);\n                            dateStr = date.toISOString().split('T')[0];\n                            dailyHistory.push({\n                                date: dateStr,\n                                tokens: dailyMap.get(dateStr) || 0\n                            });\n                        }\n                    }\n                    return _context9.abrupt(\"return\", {\n                        percentage: Math.round(percentage),\n                        limit: profile.monthly_token_limit,\n                        used: profile.current_month_tokens,\n                        remaining: profile.monthly_token_limit - profile.current_month_tokens,\n                        dailyHistory: dailyHistory\n                    });\n                case 29:\n                    _context9.prev = 29;\n                    _context9.t0 = _context9[\"catch\"](0);\n                    console.error('Error en getTokenUsageProgress:', _context9.t0);\n                    return _context9.abrupt(\"return\", null);\n                case 33:\n                case \"end\":\n                    return _context9.stop();\n            }\n        }, _callee9, null, [\n            [\n                0,\n                29\n            ]\n        ]);\n    }));\n    return _getTokenUsageProgress.apply(this, arguments);\n}\nfunction getTokenPurchaseHistory() {\n    return _getTokenPurchaseHistory.apply(this, arguments);\n}\nfunction _getTokenPurchaseHistory() {\n    _getTokenPurchaseHistory = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee10() {\n        var supabase, _yield$supabase$auth$6, user, userError, _yield$supabase$from$9, purchases, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee10$(_context10) {\n            while(1)switch(_context10.prev = _context10.next){\n                case 0:\n                    _context10.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context10.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$6 = _context10.sent;\n                    user = _yield$supabase$auth$6.data.user;\n                    userError = _yield$supabase$auth$6.error;\n                    if (!(userError || !user)) {\n                        _context10.next = 9;\n                        break;\n                    }\n                    return _context10.abrupt(\"return\", null);\n                case 9:\n                    _context10.next = 11;\n                    return supabase.from('token_purchases').select('id, amount, price, created_at, status').eq('user_id', user.id).order('created_at', {\n                        ascending: false\n                    });\n                case 11:\n                    _yield$supabase$from$9 = _context10.sent;\n                    purchases = _yield$supabase$from$9.data;\n                    error = _yield$supabase$from$9.error;\n                    if (!error) {\n                        _context10.next = 17;\n                        break;\n                    }\n                    console.error('Error al obtener historial de compras:', error);\n                    return _context10.abrupt(\"return\", null);\n                case 17:\n                    return _context10.abrupt(\"return\", purchases || []);\n                case 20:\n                    _context10.prev = 20;\n                    _context10.t0 = _context10[\"catch\"](0);\n                    console.error('Error en getTokenPurchaseHistory:', _context10.t0);\n                    return _context10.abrupt(\"return\", null);\n                case 24:\n                case \"end\":\n                    return _context10.stop();\n            }\n        }, _callee10, null, [\n            [\n                0,\n                20\n            ]\n        ]);\n    }));\n    return _getTokenPurchaseHistory.apply(this, arguments);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UvdG9rZW5Vc2FnZVNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXVDO0FBRXdDO0FBbUMvRTs7Q0FFQSxHQUNBLFNBQXNCRyxjQUFjQSxDQUFBQyxFQUFBO0lBQUEsT0FBQUMsZUFBQSxDQUFBQyxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQXVEcEM7O0NBRUEsR0FGQSxTQUFBRixnQkFBQTtJQUFBQSxlQUFBLEdBQUFHLDRLQUFBLGVBQUFDLDhJQUFBLENBdkRPLFNBQUFFLFFBQThCQyxJQUF1QjtRQUFBLElBQUFDLFFBQUEsRUFBQUMscUJBQUEsRUFBQUMsSUFBQSxFQUFBQyxTQUFBLEVBQUFDLGdCQUFBLEVBQUFDLFdBQUEsRUFBQUMscUJBQUEsRUFBQUMsS0FBQTtRQUFBLE9BQUFYLDhJQUFBLFVBQUFhLFNBQUFDLFFBQUE7WUFBQSxlQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO2dCQUFBO29CQUFBRixRQUFBLENBQUFDLElBQUE7b0JBRXhERSxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnREFBZ0QsRUFBRWYsSUFBSSxDQUFDO29CQUVuRTtvQkFDTUMsUUFBUSxHQUFHYixxREFBWSxDQUFDLENBQUM7b0JBQy9CMEIsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkJBQTJCLENBQUM7b0JBQUNKLFFBQUEsQ0FBQUUsSUFBQTtvQkFBQSxPQUVVWixRQUFRLENBQUNlLElBQUksQ0FBQ0MsT0FBTyxDQUFDLENBQUM7Z0JBQUE7b0JBQUFmLHFCQUFBLEdBQUFTLFFBQUEsQ0FBQU8sSUFBQTtvQkFBMURmLElBQUksR0FBQUQscUJBQUEsQ0FBWkYsSUFBSSxDQUFJRyxJQUFJO29CQUFXQyxTQUFTLEdBQUFGLHFCQUFBLENBQWhCTSxLQUFLO29CQUM3Qk0sT0FBTyxDQUFDQyxHQUFHLENBQUMsc0JBQXNCLEVBQUVaLElBQUksVUFBQWdCLE1BQUEsQ0FBVWhCLElBQUksQ0FBQ2lCLEVBQUUsZUFBQUQsTUFBQSxDQUFZaEIsSUFBSSxDQUFDa0IsS0FBSyxJQUFLLGdCQUFnQixDQUFDO29CQUFDLE1BRWxHakIsU0FBUyxJQUFJLENBQUNELElBQUFBLENBQUk7d0JBQUFRLFFBQUEsQ0FBQUUsSUFBQTt3QkFBQTtvQkFBQTtvQkFDcEJDLE9BQU8sQ0FBQ1EsSUFBSSxDQUFDLG1EQUFtRCxFQUFFbEIsU0FBUyxhQUFUQSxTQUFTLHVCQUFUQSxTQUFTLENBQUVtQixPQUFPLENBQUM7b0JBQUMsT0FBQVosUUFBQSxDQUFBYSxNQUFBO2dCQUFBO29CQUFBYixRQUFBLENBQUFFLElBQUE7b0JBQUEsT0FLekRZLHNCQUFzQixDQUFDdEIsSUFBSSxDQUFDaUIsRUFBRSxFQUFFcEIsSUFBSSxDQUFDMEIsUUFBUSxFQUFFMUIsSUFBSSxDQUFDMkIsS0FBSyxDQUFDQyxXQUFXLENBQUM7Z0JBQUE7b0JBQS9GdkIsZ0JBQWdCLEdBQUFNLFFBQUEsQ0FBQU8sSUFBQTtvQkFBQSxJQUNqQmIsZ0JBQWdCLENBQUN3QixPQUFPO3dCQUFBbEIsUUFBQSxDQUFBRSxJQUFBO3dCQUFBO29CQUFBO29CQUMzQkMsT0FBTyxDQUFDUSxJQUFJLENBQUMsbUNBQW1DLEVBQUVqQixnQkFBZ0IsQ0FBQ3lCLE1BQU0sQ0FBQztvQkFBQyxNQUNyRSxJQUFJQyxLQUFLLENBQUMxQixnQkFBZ0IsQ0FBQ3lCLE1BQU0sQ0FBQztnQkFBQTtvQkFHcEN4QixXQUFXLEdBQUc7d0JBQ2xCMEIsT0FBTyxFQUFFN0IsSUFBSSxDQUFDaUIsRUFBRTt3QkFDaEJhLGFBQWEsRUFBRWpDLElBQUksQ0FBQzBCLFFBQVE7d0JBQzVCUSxVQUFVLEVBQUVsQyxJQUFJLENBQUNtQyxLQUFLO3dCQUN0QkMsYUFBYSxFQUFFcEMsSUFBSSxDQUFDMkIsS0FBSyxDQUFDVSxZQUFZO3dCQUN0Q0MsaUJBQWlCLEVBQUV0QyxJQUFJLENBQUMyQixLQUFLLENBQUNZLGdCQUFnQjt3QkFDOUNDLFlBQVksRUFBRXhDLElBQUksQ0FBQzJCLEtBQUssQ0FBQ0MsV0FBVzt3QkFDcENhLGNBQWMsRUFBRXpDLElBQUksQ0FBQzJCLEtBQUssQ0FBQ2UsYUFBYSxJQUFJLENBQUM7d0JBQzdDQyxXQUFXLEVBQUUsSUFBSUMsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsQ0FBQ0MsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxLQUFLLENBQUM7b0JBQzVELENBQUM7b0JBRURoQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyx5QkFBeUIsRUFBRVQsV0FBVyxDQUFDO29CQUFDSyxRQUFBLENBQUFFLElBQUE7b0JBQUEsT0FFNUJaLFFBQVEsQ0FDN0I4QyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FDeEJDLE1BQU0sQ0FBQzt3QkFBQzFDLFdBQVc7cUJBQUMsQ0FBQztnQkFBQTtvQkFBQUMscUJBQUEsR0FBQUksUUFBQSxDQUFBTyxJQUFBO29CQUZoQlYsS0FBSyxHQUFBRCxxQkFBQSxDQUFMQyxLQUFLO29CQUFBLEtBSVRBLEtBQUs7d0JBQUFHLFFBQUEsQ0FBQUUsSUFBQTt3QkFBQTtvQkFBQTtvQkFDUEMsT0FBTyxDQUFDTixLQUFLLENBQUMsbUNBQW1DLEVBQUVBLEtBQUssQ0FBQztvQkFBQyxPQUFBRyxRQUFBLENBQUFhLE1BQUE7Z0JBQUE7b0JBSTVEVixPQUFPLENBQUNDLEdBQUcsQ0FBQyx1REFBdUQsQ0FBQztvQkFFcEU7b0JBQUFKLFFBQUEsQ0FBQUUsSUFBQTtvQkFBQSxPQUNNb0MsdUJBQXVCLENBQUM5QyxJQUFJLENBQUNpQixFQUFFLEVBQUVwQixJQUFJLENBQUMyQixLQUFLLENBQUNDLFdBQVcsQ0FBQztnQkFBQTtvQkFBQWpCLFFBQUEsQ0FBQUUsSUFBQTtvQkFBQTtnQkFBQTtvQkFBQUYsUUFBQSxDQUFBQyxJQUFBO29CQUFBRCxRQUFBLENBQUF1QyxFQUFBLEdBQUF2QyxRQUFBO29CQUc5REcsT0FBTyxDQUFDTixLQUFLLENBQUMsMEJBQTBCLEVBQUFHLFFBQUEsQ0FBQXVDLEVBQU8sQ0FBQztnQkFBQztnQkFBQTtvQkFBQSxPQUFBdkMsUUFBQSxDQUFBd0MsSUFBQTtZQUFBO1FBQUEsR0FBQXBELE9BQUE7WUFBQTtnQkFBQTtnQkFBQTthQUFBO1NBQUE7SUFBQSxDQUVwRDtJQUFBLE9BQUFOLGVBQUEsQ0FBQUMsS0FBQSxPQUFBQyxTQUFBO0FBQUE7QUFBQSxTQUtjc0QsdUJBQXVCQSxDQUFBRyxHQUFBLEVBQUFDLEdBQUE7SUFBQSxPQUFBQyx3QkFBQSxDQUFBNUQsS0FBQSxPQUFBQyxTQUFBO0FBQUE7QUF3RHRDOztDQUVBLEdBRkEsU0FBQTJELHlCQUFBO0lBQUFBLHdCQUFBLEdBQUExRCw0S0FBQSxlQUFBQyw4SUFBQSxDQXhEQSxTQUFBMEQsU0FBdUNDLE1BQWMsRUFBRUMsTUFBYztRQUFBLElBQUF4RCxRQUFBLEVBQUF5RCxZQUFBLEVBQUFDLHNCQUFBLEVBQUFDLE9BQUEsRUFBQUMsWUFBQSxFQUFBQyxzQkFBQSxFQUFBQyxXQUFBLEVBQUFDLGFBQUEsRUFBQUMsc0JBQUEsRUFBQUMsV0FBQTtRQUFBLE9BQUFyRSw4SUFBQSxVQUFBc0UsVUFBQUMsU0FBQTtZQUFBLGVBQUFBLFNBQUEsQ0FBQXhELElBQUEsR0FBQXdELFNBQUEsQ0FBQXZELElBQUE7Z0JBQUE7b0JBQUF1RCxTQUFBLENBQUF4RCxJQUFBO29CQUUzRFgsUUFBUSxHQUFHYixxREFBWSxDQUFDLENBQUM7b0JBQ3pCc0UsWUFBWSxHQUFHLElBQUlkLElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQyxDQUFDLENBQUNDLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUcsS0FBSyxFQUVqRTtvQkFBQXNCLFNBQUEsQ0FBQXZELElBQUE7b0JBQUEsT0FDbURaLFFBQVEsQ0FDeEQ4QyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQ3JCc0IsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUNYQyxFQUFFLENBQUMsU0FBUyxFQUFFZCxNQUFNLENBQUMsQ0FDckJlLE1BQU0sQ0FBQyxDQUFDO2dCQUFBO29CQUFBWixzQkFBQSxHQUFBUyxTQUFBLENBQUFsRCxJQUFBO29CQUpDMEMsT0FBTyxHQUFBRCxzQkFBQSxDQUFiM0QsSUFBSTtvQkFBa0I2RCxZQUFZLEdBQUFGLHNCQUFBLENBQW5CbkQsS0FBSztvQkFBQSxNQU10QnFELFlBQVksSUFBSUEsWUFBWSxDQUFDVyxJQUFJLEtBQUssV0FBVTt3QkFBQUosU0FBQSxDQUFBdkQsSUFBQTt3QkFBQTtvQkFBQTtvQkFDbERDLE9BQU8sQ0FBQ04sS0FBSyxDQUFDLDBCQUEwQixFQUFFcUQsWUFBWSxDQUFDO29CQUFDLE9BQUFPLFNBQUEsQ0FBQTVDLE1BQUE7Z0JBQUE7b0JBQUEsSUFJckRvQyxPQUFPO3dCQUFBUSxTQUFBLENBQUF2RCxJQUFBO3dCQUFBO29CQUFBO29CQUFBdUQsU0FBQSxDQUFBdkQsSUFBQTtvQkFBQSxPQUUyQlosUUFBUSxDQUMxQzhDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FDckJDLE1BQU0sQ0FBQzt3QkFBQzs0QkFDUGhCLE9BQU8sRUFBRXdCLE1BQU07NEJBQ2ZpQixpQkFBaUIsRUFBRSxNQUFNOzRCQUN6QkMsbUJBQW1CLEVBQUUsS0FBSzs0QkFDMUJDLG9CQUFvQixFQUFFbEIsTUFBTTs0QkFDNUJtQixhQUFhLEVBQUVsQjt3QkFDakIsQ0FBQztxQkFBQyxDQUFDO2dCQUFBO29CQUFBSSxzQkFBQSxHQUFBTSxTQUFBLENBQUFsRCxJQUFBO29CQVJVNkMsV0FBVyxHQUFBRCxzQkFBQSxDQUFsQnRELEtBQUs7b0JBVWIsSUFBSXVELFdBQVcsRUFBRTt3QkFDZmpELE9BQU8sQ0FBQ04sS0FBSyxDQUFDLHdCQUF3QixFQUFFdUQsV0FBVyxDQUFDO29CQUN0RDtvQkFBQ0ssU0FBQSxDQUFBdkQsSUFBQTtvQkFBQTtnQkFBQTtvQkFFRDtvQkFDTW1ELGFBQWEsR0FBR0osT0FBTyxDQUFDZ0IsYUFBYSxLQUFLbEIsWUFBWSxHQUN4REUsT0FBTyxDQUFDZSxvQkFBb0IsR0FBR2xCLE1BQU0sR0FDckNBLE1BQU0sRUFBRTtvQkFBQVcsU0FBQSxDQUFBdkQsSUFBQTtvQkFBQSxPQUV5QlosUUFBUSxDQUMxQzhDLElBQUksQ0FBQyxlQUFlLENBQUMsQ0FDckI4QixNQUFNLENBQUM7d0JBQ05GLG9CQUFvQixFQUFFWCxhQUFhO3dCQUNuQ1ksYUFBYSxFQUFFbEIsWUFBWTt3QkFDM0JvQixVQUFVLEVBQUUsSUFBSWxDLElBQUksQ0FBQyxDQUFDLENBQUNDLFdBQVcsQ0FBQztvQkFDckMsQ0FBQyxDQUFDLENBQ0R5QixFQUFFLENBQUMsU0FBUyxFQUFFZCxNQUFNLENBQUM7Z0JBQUE7b0JBQUFTLHNCQUFBLEdBQUFHLFNBQUEsQ0FBQWxELElBQUE7b0JBUFRnRCxXQUFXLEdBQUFELHNCQUFBLENBQWxCekQsS0FBSztvQkFTYixJQUFJMEQsV0FBVyxFQUFFO3dCQUNmcEQsT0FBTyxDQUFDTixLQUFLLENBQUMsNkJBQTZCLEVBQUUwRCxXQUFXLENBQUM7b0JBQzNEO2dCQUFDO29CQUFBRSxTQUFBLENBQUF2RCxJQUFBO29CQUFBO2dCQUFBO29CQUFBdUQsU0FBQSxDQUFBeEQsSUFBQTtvQkFBQXdELFNBQUEsQ0FBQWxCLEVBQUEsR0FBQWtCLFNBQUE7b0JBR0h0RCxPQUFPLENBQUNOLEtBQUssQ0FBQyxtQ0FBbUMsRUFBQTRELFNBQUEsQ0FBQWxCLEVBQU8sQ0FBQztnQkFBQztnQkFBQTtvQkFBQSxPQUFBa0IsU0FBQSxDQUFBakIsSUFBQTtZQUFBO1FBQUEsR0FBQUksUUFBQTtZQUFBO2dCQUFBO2dCQUFBO2FBQUE7U0FBQTtJQUFBLENBRTdEO0lBQUEsT0FBQUQsd0JBQUEsQ0FBQTVELEtBQUEsT0FBQUMsU0FBQTtBQUFBO0FBS0QsU0FBc0JvRixpQkFBaUJBLENBQUE7SUFBQSxPQUFBQyxrQkFBQSxDQUFBdEYsS0FBQSxPQUFBQyxTQUFBO0FBQUE7QUE2QnZDOztDQUVBLEdBRkEsU0FBQXFGLG1CQUFBO0lBQUFBLGtCQUFBLEdBQUFwRiw0S0FBQSxlQUFBQyw4SUFBQSxDQTdCTyxTQUFBb0YsU0FBQTtRQUFBLElBQUFoRixRQUFBLEVBQUFpRixzQkFBQSxFQUFBL0UsSUFBQSxFQUFBQyxTQUFBLEVBQUErRSxzQkFBQSxFQUFBQyxPQUFBLEVBQUE1RSxLQUFBO1FBQUEsT0FBQVgsOElBQUEsVUFBQXdGLFVBQUFDLFNBQUE7WUFBQSxlQUFBQSxTQUFBLENBQUExRSxJQUFBLEdBQUEwRSxTQUFBLENBQUF6RSxJQUFBO2dCQUFBO29CQUFBeUUsU0FBQSxDQUFBMUUsSUFBQTtvQkFFR1gsUUFBUSxHQUFHYixxREFBWSxDQUFDLENBQUM7b0JBQUFrRyxTQUFBLENBQUF6RSxJQUFBO29CQUFBLE9BRW9CWixRQUFRLENBQUNlLElBQUksQ0FBQ0MsT0FBTyxDQUFDLENBQUM7Z0JBQUE7b0JBQUFpRSxzQkFBQSxHQUFBSSxTQUFBLENBQUFwRSxJQUFBO29CQUExRGYsSUFBSSxHQUFBK0Usc0JBQUEsQ0FBWmxGLElBQUksQ0FBSUcsSUFBSTtvQkFBV0MsU0FBUyxHQUFBOEUsc0JBQUEsQ0FBaEIxRSxLQUFLO29CQUFBLE1BRXpCSixTQUFTLElBQUksQ0FBQ0QsSUFBQUEsQ0FBSTt3QkFBQW1GLFNBQUEsQ0FBQXpFLElBQUE7d0JBQUE7b0JBQUE7b0JBQUEsT0FBQXlFLFNBQUEsQ0FBQTlELE1BQUEsV0FDYitELGFBQWEsQ0FBQyxDQUFDO2dCQUFBO29CQUFBRCxTQUFBLENBQUF6RSxJQUFBO29CQUFBLE9BR2VaLFFBQVEsQ0FDNUM4QyxJQUFJLENBQUMsa0JBQWtCLENBQUMsQ0FDeEJzQixNQUFNLENBQUMsR0FBRyxDQUFDLENBQ1hDLEVBQUUsQ0FBQyxTQUFTLEVBQUVuRSxJQUFJLENBQUNpQixFQUFFLENBQUMsQ0FDdEJvRSxLQUFLLENBQUMsWUFBWSxFQUFFO3dCQUFFQyxTQUFTLEVBQUU7b0JBQU0sQ0FBQyxDQUFDO2dCQUFBO29CQUFBTixzQkFBQSxHQUFBRyxTQUFBLENBQUFwRSxJQUFBO29CQUo5QmtFLE9BQU8sR0FBQUQsc0JBQUEsQ0FBYm5GLElBQUk7b0JBQVdRLEtBQUssR0FBQTJFLHNCQUFBLENBQUwzRSxLQUFLO29CQUFBLEtBTXhCQSxLQUFLO3dCQUFBOEUsU0FBQSxDQUFBekUsSUFBQTt3QkFBQTtvQkFBQTtvQkFDUEMsT0FBTyxDQUFDTixLQUFLLENBQUMsZ0NBQWdDLEVBQUVBLEtBQUssQ0FBQztvQkFBQyxPQUFBOEUsU0FBQSxDQUFBOUQsTUFBQSxXQUNoRCtELGFBQWEsQ0FBQyxDQUFDO2dCQUFBO29CQUFBLE9BQUFELFNBQUEsQ0FBQTlELE1BQUEsV0FHakJrRSxjQUFjLENBQUNOLE9BQU8sSUFBSSxFQUFFLENBQUM7Z0JBQUE7b0JBQUFFLFNBQUEsQ0FBQTFFLElBQUE7b0JBQUEwRSxTQUFBLENBQUFwQyxFQUFBLEdBQUFvQyxTQUFBO29CQUdwQ3hFLE9BQU8sQ0FBQ04sS0FBSyxDQUFDLDZCQUE2QixFQUFBOEUsU0FBQSxDQUFBcEMsRUFBTyxDQUFDO29CQUFDLE9BQUFvQyxTQUFBLENBQUE5RCxNQUFBLFdBQzdDK0QsYUFBYSxDQUFDLENBQUM7Z0JBQUE7Z0JBQUE7b0JBQUEsT0FBQUQsU0FBQSxDQUFBbkMsSUFBQTtZQUFBO1FBQUEsR0FBQThCLFFBQUE7WUFBQTtnQkFBQTtnQkFBQTthQUFBO1NBQUE7SUFBQSxDQUV6QjtJQUFBLE9BQUFELGtCQUFBLENBQUF0RixLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQUtELFNBQVMrRixjQUFjQSxDQUFDTixPQUEyQixFQUFtQjtJQUNwRSxJQUFNTyxLQUFzQixHQUFHO1FBQzdCQyxhQUFhLEVBQUVSLE9BQU8sQ0FBQ1MsTUFBTTtRQUM3QmpFLFdBQVcsRUFBRSxDQUFDO1FBQ2RrRSxTQUFTLEVBQUUsQ0FBQztRQUNaQyxVQUFVLEVBQUUsQ0FBQyxDQUFDO1FBQ2RDLE9BQU8sRUFBRSxDQUFDO0lBQ1osQ0FBQztJQUVEWixPQUFPLENBQUNhLE9BQU8sQ0FBQyxTQUFBQyxNQUFNLEVBQUk7UUFDeEIsSUFBTXpDLE1BQU0sR0FBR3lDLE1BQU0sQ0FBQzFELFlBQVk7UUFDbEMsSUFBTTJELElBQUksR0FBR0QsTUFBTSxDQUFDekQsY0FBYztRQUVsQ2tELEtBQUssQ0FBQy9ELFdBQVcsSUFBSTZCLE1BQU07UUFDM0JrQyxLQUFLLENBQUNHLFNBQVMsSUFBSUssSUFBSTtRQUV2QjtRQUNBLElBQUksQ0FBQ1IsS0FBSyxDQUFDSSxVQUFVLENBQUNHLE1BQU0sQ0FBQ2pFLGFBQWEsQ0FBQyxFQUFFO1lBQzNDMEQsS0FBSyxDQUFDSSxVQUFVLENBQUNHLE1BQU0sQ0FBQ2pFLGFBQWEsQ0FBQyxHQUFHO2dCQUFFd0IsTUFBTSxFQUFFLENBQUM7Z0JBQUUwQyxJQUFJLEVBQUUsQ0FBQztnQkFBRUMsS0FBSyxFQUFFO1lBQUUsQ0FBQztRQUMzRTtRQUNBVCxLQUFLLENBQUNJLFVBQVUsQ0FBQ0csTUFBTSxDQUFDakUsYUFBYSxDQUFDLENBQUN3QixNQUFNLElBQUlBLE1BQU07UUFDdkRrQyxLQUFLLENBQUNJLFVBQVUsQ0FBQ0csTUFBTSxDQUFDakUsYUFBYSxDQUFDLENBQUNrRSxJQUFJLElBQUlBLElBQUk7UUFDbkRSLEtBQUssQ0FBQ0ksVUFBVSxDQUFDRyxNQUFNLENBQUNqRSxhQUFhLENBQUMsQ0FBQ21FLEtBQUssSUFBSSxDQUFDO1FBRWpEO1FBQ0EsSUFBSSxDQUFDVCxLQUFLLENBQUNLLE9BQU8sQ0FBQ0UsTUFBTSxDQUFDaEUsVUFBVSxDQUFDLEVBQUU7WUFDckN5RCxLQUFLLENBQUNLLE9BQU8sQ0FBQ0UsTUFBTSxDQUFDaEUsVUFBVSxDQUFDLEdBQUc7Z0JBQUV1QixNQUFNLEVBQUUsQ0FBQztnQkFBRTBDLElBQUksRUFBRSxDQUFDO2dCQUFFQyxLQUFLLEVBQUU7WUFBRSxDQUFDO1FBQ3JFO1FBQ0FULEtBQUssQ0FBQ0ssT0FBTyxDQUFDRSxNQUFNLENBQUNoRSxVQUFVLENBQUMsQ0FBQ3VCLE1BQU0sSUFBSUEsTUFBTTtRQUNqRGtDLEtBQUssQ0FBQ0ssT0FBTyxDQUFDRSxNQUFNLENBQUNoRSxVQUFVLENBQUMsQ0FBQ2lFLElBQUksSUFBSUEsSUFBSTtRQUM3Q1IsS0FBSyxDQUFDSyxPQUFPLENBQUNFLE1BQU0sQ0FBQ2hFLFVBQVUsQ0FBQyxDQUFDa0UsS0FBSyxJQUFJLENBQUM7SUFDN0MsQ0FBQyxDQUFDO0lBRUYsT0FBT1QsS0FBSztBQUNkO0FBRUE7O0NBRUEsR0FDQSxTQUFTSixhQUFhQSxDQUFBLEVBQW9CO0lBQ3hDLE9BQU87UUFDTEssYUFBYSxFQUFFLENBQUM7UUFDaEJoRSxXQUFXLEVBQUUsQ0FBQztRQUNka0UsU0FBUyxFQUFFLENBQUM7UUFDWkMsVUFBVSxFQUFFLENBQUMsQ0FBQztRQUNkQyxPQUFPLEVBQUUsQ0FBQztJQUNaLENBQUM7QUFDSDtBQUVBOztDQUVBLEdBQ0EsU0FBc0JLLGNBQWNBLENBQUE7SUFBQSxPQUFBQyxlQUFBLENBQUE1RyxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQTZCcEM7O0NBRUEsR0FGQSxTQUFBMkcsZ0JBQUE7SUFBQUEsZUFBQSxHQUFBMUcsNEtBQUEsZUFBQUMsOElBQUEsQ0E3Qk8sU0FBQTBHLFNBQUE7UUFBQSxJQUFBdEcsUUFBQSxFQUFBdUcsc0JBQUEsRUFBQXJHLElBQUEsRUFBQUMsU0FBQSxFQUFBcUcsc0JBQUEsRUFBQTdDLE9BQUEsRUFBQXBELEtBQUE7UUFBQSxPQUFBWCw4SUFBQSxVQUFBNkcsVUFBQUMsU0FBQTtZQUFBLGVBQUFBLFNBQUEsQ0FBQS9GLElBQUEsR0FBQStGLFNBQUEsQ0FBQTlGLElBQUE7Z0JBQUE7b0JBQUE4RixTQUFBLENBQUEvRixJQUFBO29CQUVHWCxRQUFRLEdBQUdiLHFEQUFZLENBQUMsQ0FBQztvQkFBQXVILFNBQUEsQ0FBQTlGLElBQUE7b0JBQUEsT0FFb0JaLFFBQVEsQ0FBQ2UsSUFBSSxDQUFDQyxPQUFPLENBQUMsQ0FBQztnQkFBQTtvQkFBQXVGLHNCQUFBLEdBQUFHLFNBQUEsQ0FBQXpGLElBQUE7b0JBQTFEZixJQUFJLEdBQUFxRyxzQkFBQSxDQUFaeEcsSUFBSSxDQUFJRyxJQUFJO29CQUFXQyxTQUFTLEdBQUFvRyxzQkFBQSxDQUFoQmhHLEtBQUs7b0JBQUEsTUFFekJKLFNBQVMsSUFBSSxDQUFDRCxJQUFBQSxDQUFJO3dCQUFBd0csU0FBQSxDQUFBOUYsSUFBQTt3QkFBQTtvQkFBQTtvQkFBQSxPQUFBOEYsU0FBQSxDQUFBbkYsTUFBQSxXQUNiLElBQUk7Z0JBQUE7b0JBQUFtRixTQUFBLENBQUE5RixJQUFBO29CQUFBLE9BRzBCWixRQUFRLENBQzVDOEMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUNyQnNCLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FDWEMsRUFBRSxDQUFDLFNBQVMsRUFBRW5FLElBQUksQ0FBQ2lCLEVBQUUsQ0FBQyxDQUN0Qm1ELE1BQU0sQ0FBQyxDQUFDO2dCQUFBO29CQUFBa0Msc0JBQUEsR0FBQUUsU0FBQSxDQUFBekYsSUFBQTtvQkFKRzBDLE9BQU8sR0FBQTZDLHNCQUFBLENBQWJ6RyxJQUFJO29CQUFXUSxLQUFLLEdBQUFpRyxzQkFBQSxDQUFMakcsS0FBSztvQkFBQSxNQU14QkEsS0FBSyxJQUFJQSxLQUFLLENBQUNnRSxJQUFJLEtBQUssV0FBVTt3QkFBQW1DLFNBQUEsQ0FBQTlGLElBQUE7d0JBQUE7b0JBQUE7b0JBQ3BDQyxPQUFPLENBQUNOLEtBQUssQ0FBQywwQkFBMEIsRUFBRUEsS0FBSyxDQUFDO29CQUFDLE9BQUFtRyxTQUFBLENBQUFuRixNQUFBLFdBQzFDLElBQUk7Z0JBQUE7b0JBQUEsT0FBQW1GLFNBQUEsQ0FBQW5GLE1BQUEsV0FHTm9DLE9BQU87Z0JBQUE7b0JBQUErQyxTQUFBLENBQUEvRixJQUFBO29CQUFBK0YsU0FBQSxDQUFBekQsRUFBQSxHQUFBeUQsU0FBQTtvQkFHZDdGLE9BQU8sQ0FBQ04sS0FBSyxDQUFDLDBCQUEwQixFQUFBbUcsU0FBQSxDQUFBekQsRUFBTyxDQUFDO29CQUFDLE9BQUF5RCxTQUFBLENBQUFuRixNQUFBLFdBQzFDLElBQUk7Z0JBQUE7Z0JBQUE7b0JBQUEsT0FBQW1GLFNBQUEsQ0FBQXhELElBQUE7WUFBQTtRQUFBLEdBQUFvRCxRQUFBO1lBQUE7Z0JBQUE7Z0JBQUE7YUFBQTtTQUFBO0lBQUEsQ0FFZDtJQUFBLE9BQUFELGVBQUEsQ0FBQTVHLEtBQUEsT0FBQUMsU0FBQTtBQUFBO0FBS0QsU0FBc0JpSCxlQUFlQSxDQUFBO0lBQUEsT0FBQUMsZ0JBQUEsQ0FBQW5ILEtBQUEsT0FBQUMsU0FBQTtBQUFBO0FBK0JyQzs7Q0FFQSxHQUZBLFNBQUFrSCxpQkFBQTtJQUFBQSxnQkFBQSxHQUFBakgsNEtBQUEsZUFBQUMsOElBQUEsQ0EvQk8sU0FBQWlILFNBQUE7UUFBQSxJQUFBbEQsT0FBQSxFQUFBbUQsYUFBQSxFQUFBQyxZQUFBLEVBQUFDLFVBQUEsRUFBQUMsZUFBQTtRQUFBLE9BQUFySCw4SUFBQSxVQUFBc0gsVUFBQUMsU0FBQTtZQUFBLGVBQUFBLFNBQUEsQ0FBQXhHLElBQUEsR0FBQXdHLFNBQUEsQ0FBQXZHLElBQUE7Z0JBQUE7b0JBQUF1RyxTQUFBLENBQUF4RyxJQUFBO29CQUFBd0csU0FBQSxDQUFBdkcsSUFBQTtvQkFBQSxPQU9tQndGLGNBQWMsQ0FBQyxDQUFDO2dCQUFBO29CQUFoQ3pDLE9BQU8sR0FBQXdELFNBQUEsQ0FBQWxHLElBQUE7b0JBQUEsSUFFUjBDLE9BQU87d0JBQUF3RCxTQUFBLENBQUF2RyxJQUFBO3dCQUFBO29CQUFBO29CQUFBLE9BQUF1RyxTQUFBLENBQUE1RixNQUFBLFdBQ0g7d0JBQUUwRixlQUFlLEVBQUUsS0FBSzt3QkFBRUgsYUFBYSxFQUFFLENBQUM7d0JBQUVNLEtBQUssRUFBRSxLQUFLO3dCQUFFSixVQUFVLEVBQUU7b0JBQUUsQ0FBQztnQkFBQTtvQkFHNUVGLGFBQWEsR0FBR25ELE9BQU8sQ0FBQ2Usb0JBQW9CLElBQUksQ0FBQztvQkFDakRxQyxZQUFZLEdBQUdwRCxPQUFPLENBQUNjLG1CQUFtQixJQUFJLENBQUM7b0JBQy9DdUMsVUFBVSxHQUFHRCxZQUFZLEdBQUcsQ0FBQyxHQUFJRCxhQUFhLEdBQUdDLFlBQVksR0FBSSxHQUFHLEdBQUcsQ0FBQztvQkFDeEVFLGVBQWUsR0FBR0gsYUFBYSxJQUFJQyxZQUFZO29CQUFBLE9BQUFJLFNBQUEsQ0FBQTVGLE1BQUEsV0FFOUM7d0JBQ0wwRixlQUFlLEVBQWZBLGVBQWU7d0JBQ2ZILGFBQWEsRUFBYkEsYUFBYTt3QkFDYk0sS0FBSyxFQUFFTCxZQUFZO3dCQUNuQkMsVUFBVSxFQUFWQTtvQkFDRixDQUFDO2dCQUFBO29CQUFBRyxTQUFBLENBQUF4RyxJQUFBO29CQUFBd0csU0FBQSxDQUFBbEUsRUFBQSxHQUFBa0UsU0FBQTtvQkFHRHRHLE9BQU8sQ0FBQ04sS0FBSyxDQUFDLDJCQUEyQixFQUFBNEcsU0FBQSxDQUFBbEUsRUFBTyxDQUFDO29CQUFDLE9BQUFrRSxTQUFBLENBQUE1RixNQUFBLFdBQzNDO3dCQUFFMEYsZUFBZSxFQUFFLEtBQUs7d0JBQUVILGFBQWEsRUFBRSxDQUFDO3dCQUFFTSxLQUFLLEVBQUUsS0FBSzt3QkFBRUosVUFBVSxFQUFFO29CQUFFLENBQUM7Z0JBQUE7Z0JBQUE7b0JBQUEsT0FBQUcsU0FBQSxDQUFBakUsSUFBQTtZQUFBO1FBQUEsR0FBQTJELFFBQUE7WUFBQTtnQkFBQTtnQkFBQTthQUFBO1NBQUE7SUFBQSxDQUVuRjtJQUFBLE9BQUFELGdCQUFBLENBQUFuSCxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQUFBLFNBS2M4QixzQkFBc0JBLENBQUE2RixHQUFBLEVBQUFDLEdBQUEsRUFBQUMsR0FBQTtJQUFBLE9BQUFDLHVCQUFBLENBQUEvSCxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQXdFckM7O0NBRUEsR0FGQSxTQUFBOEgsd0JBQUE7SUFBQUEsdUJBQUEsR0FBQTdILDRLQUFBLGVBQUFDLDhJQUFBLENBeEVBLFNBQUE2SCxTQUNFbEUsTUFBYyxFQUNkOUIsUUFBZ0IsRUFDaEJpRyxXQUFtQjtRQUFBLElBQUExSCxRQUFBLEVBQUEySCxzQkFBQSxFQUFBaEUsT0FBQSxFQUFBcEQsS0FBQSxFQUFBcUgsaUJBQUEsRUFBQUMsV0FBQSxFQUFBcEUsWUFBQSxFQUFBcUQsYUFBQTtRQUFBLE9BQUFsSCw4SUFBQSxVQUFBa0ksVUFBQUMsU0FBQTtZQUFBLGVBQUFBLFNBQUEsQ0FBQXBILElBQUEsR0FBQW9ILFNBQUEsQ0FBQW5ILElBQUE7Z0JBQUE7b0JBQUFtSCxTQUFBLENBQUFwSCxJQUFBO29CQUdYWCxRQUFRLEdBQUdiLHFEQUFZLENBQUMsQ0FBQyxFQUUvQjtvQkFBQTRJLFNBQUEsQ0FBQW5ILElBQUE7b0JBQUEsT0FDdUNaLFFBQVEsQ0FDNUM4QyxJQUFJLENBQUMsZUFBZSxDQUFDLENBQ3JCc0IsTUFBTSxDQUFDLCtGQUErRixDQUFDLENBQ3ZHQyxFQUFFLENBQUMsU0FBUyxFQUFFZCxNQUFNLENBQUMsQ0FDckJlLE1BQU0sQ0FBQyxDQUFDO2dCQUFBO29CQUFBcUQsc0JBQUEsR0FBQUksU0FBQSxDQUFBOUcsSUFBQTtvQkFKRzBDLE9BQU8sR0FBQWdFLHNCQUFBLENBQWI1SCxJQUFJO29CQUFXUSxLQUFLLEdBQUFvSCxzQkFBQSxDQUFMcEgsS0FBSztvQkFBQSxNQU14QkEsS0FBSyxJQUFJLENBQUNvRCxPQUFBQSxDQUFPO3dCQUFBb0UsU0FBQSxDQUFBbkgsSUFBQTt3QkFBQTtvQkFBQTtvQkFBQSxPQUFBbUgsU0FBQSxDQUFBeEcsTUFBQSxXQUNaO3dCQUFFSyxPQUFPLEVBQUUsS0FBSzt3QkFBRUMsTUFBTSxFQUFFO29CQUFrQyxDQUFDO2dCQUFBO29CQUd0RTtvQkFDTStGLGlCQUF5QyxHQUFHO3dCQUNoRCxpQkFBaUIsRUFBRSxpQkFBaUI7d0JBQ3BDLHNCQUFzQixFQUFFLHNCQUFzQjt3QkFDOUMscUJBQXFCLEVBQUUscUJBQXFCO3dCQUM1QyxTQUFTLEVBQUUsZUFBZTt3QkFDMUIsZ0JBQWdCLEVBQUUsZ0JBQWdCO3dCQUNsQyxvQkFBb0IsRUFBRSxlQUFlO3dCQUNyQyxtQkFBbUIsRUFBRTtvQkFDdkIsQ0FBQztvQkFFS0MsV0FBVyxHQUFHRCxpQkFBaUIsQ0FBQ25HLFFBQVEsQ0FBQyxJQUFJQSxRQUFRLEVBRTNEO29CQUFBLElBQ0twQyx1RUFBZ0IsQ0FBQ3NFLE9BQU8sQ0FBQ2EsaUJBQWlCLEVBQUVxRCxXQUFXLENBQUM7d0JBQUFFLFNBQUEsQ0FBQW5ILElBQUE7d0JBQUE7b0JBQUE7b0JBQUEsT0FBQW1ILFNBQUEsQ0FBQXhHLE1BQUEsV0FDcEQ7d0JBQ0xLLE9BQU8sRUFBRSxLQUFLO3dCQUNkQyxNQUFNLGtCQUFBWCxNQUFBLENBQWtCTyxRQUFRLHdDQUFBUCxNQUFBLENBQWtDeUMsT0FBTyxDQUFDYSxpQkFBaUI7b0JBQzdGLENBQUM7Z0JBQUE7b0JBQUEsTUFJQ2IsT0FBTyxDQUFDYSxpQkFBaUIsS0FBSyxNQUFNLElBQUksQ0FBQ2IsT0FBTyxDQUFDcUUsZ0JBQUFBLEdBQWdCO3dCQUFBRCxTQUFBLENBQUFuSCxJQUFBO3dCQUFBO29CQUFBO29CQUFBLE9BQUFtSCxTQUFBLENBQUF4RyxNQUFBLFdBQzVEO3dCQUNMSyxPQUFPLEVBQUUsS0FBSzt3QkFDZEMsTUFBTSxFQUFFO29CQUNWLENBQUM7Z0JBQUE7b0JBR0g7b0JBQ000QixZQUFZLEdBQUcsSUFBSWQsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsQ0FBQ0MsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsR0FBRyxLQUFLO29CQUM3RGlFLGFBQWEsR0FBR25ELE9BQU8sQ0FBQ2Usb0JBQW9CLEVBRWhEO29CQUNBLElBQUlmLE9BQU8sQ0FBQ2dCLGFBQWEsS0FBS2xCLFlBQVksRUFBRTt3QkFDMUNxRCxhQUFhLEdBQUcsQ0FBQztvQkFDbkI7b0JBQUMsTUFFR0EsYUFBYSxHQUFHWSxXQUFXLEdBQUcvRCxPQUFPLENBQUNjLG1CQUFBQSxHQUFtQjt3QkFBQXNELFNBQUEsQ0FBQW5ILElBQUE7d0JBQUE7b0JBQUE7b0JBQUEsT0FBQW1ILFNBQUEsQ0FBQXhHLE1BQUEsV0FDcEQ7d0JBQ0xLLE9BQU8sRUFBRSxLQUFLO3dCQUNkQyxNQUFNLG1EQUFBWCxNQUFBLENBQWdENEYsYUFBYSxPQUFBNUYsTUFBQSxDQUFJeUMsT0FBTyxDQUFDYyxtQkFBbUI7b0JBQ3BHLENBQUM7Z0JBQUE7b0JBQUEsT0FBQXNELFNBQUEsQ0FBQXhHLE1BQUEsV0FHSTt3QkFBRUssT0FBTyxFQUFFO29CQUFLLENBQUM7Z0JBQUE7b0JBQUFtRyxTQUFBLENBQUFwSCxJQUFBO29CQUFBb0gsU0FBQSxDQUFBOUUsRUFBQSxHQUFBOEUsU0FBQTtvQkFHeEJsSCxPQUFPLENBQUNOLEtBQUssQ0FBQyxtQ0FBbUMsRUFBQXdILFNBQUEsQ0FBQTlFLEVBQU8sQ0FBQztvQkFBQyxPQUFBOEUsU0FBQSxDQUFBeEcsTUFBQSxXQUNuRDt3QkFBRUssT0FBTyxFQUFFLEtBQUs7d0JBQUVDLE1BQU0sRUFBRTtvQkFBOEIsQ0FBQztnQkFBQTtnQkFBQTtvQkFBQSxPQUFBa0csU0FBQSxDQUFBN0UsSUFBQTtZQUFBO1FBQUEsR0FBQXVFLFFBQUE7WUFBQTtnQkFBQTtnQkFBQTthQUFBO1NBQUE7SUFBQSxDQUVuRTtJQUFBLE9BQUFELHVCQUFBLENBQUEvSCxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQUtELFNBQXNCdUksZUFBZUEsQ0FBQTtJQUFBLE9BQUFDLGdCQUFBLENBQUF6SSxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQWdEckM7O0NBRUEsR0FGQSxTQUFBd0ksaUJBQUE7SUFBQUEsZ0JBQUEsR0FBQXZJLDRLQUFBLGVBQUFDLDhJQUFBLENBaERPLFNBQUF1SSxTQUFBO1FBQUEsSUFBQXhFLE9BQUEsRUFBQXlFLFVBQUEsRUFBQXRCLGFBQUEsRUFBQUMsWUFBQSxFQUFBQyxVQUFBO1FBQUEsT0FBQXBILDhJQUFBLFVBQUF5SSxVQUFBQyxTQUFBO1lBQUEsZUFBQUEsU0FBQSxDQUFBM0gsSUFBQSxHQUFBMkgsU0FBQSxDQUFBMUgsSUFBQTtnQkFBQTtvQkFBQTBILFNBQUEsQ0FBQTNILElBQUE7b0JBQUEySCxTQUFBLENBQUExSCxJQUFBO29CQUFBLE9BYW1Cd0YsY0FBYyxDQUFDLENBQUM7Z0JBQUE7b0JBQWhDekMsT0FBTyxHQUFBMkUsU0FBQSxDQUFBckgsSUFBQTtvQkFBQSxJQUVSMEMsT0FBTzt3QkFBQTJFLFNBQUEsQ0FBQTFILElBQUE7d0JBQUE7b0JBQUE7b0JBQUEsT0FBQTBILFNBQUEsQ0FBQS9HLE1BQUEsV0FDSCxJQUFJO2dCQUFBO29CQUdQNkcsVUFBVSxHQUFHaEosMkVBQW9CLENBQUN1RSxPQUFPLENBQUNhLGlCQUFpQixDQUFDO29CQUFBLElBRTdENEQsVUFBVTt3QkFBQUUsU0FBQSxDQUFBMUgsSUFBQTt3QkFBQTtvQkFBQTtvQkFBQSxPQUFBMEgsU0FBQSxDQUFBL0csTUFBQSxXQUNOLElBQUk7Z0JBQUE7b0JBR1B1RixhQUFhLEdBQUduRCxPQUFPLENBQUNlLG9CQUFvQixJQUFJLENBQUM7b0JBQ2pEcUMsWUFBWSxHQUFHcEQsT0FBTyxDQUFDYyxtQkFBbUIsSUFBSSxDQUFDO29CQUMvQ3VDLFVBQVUsR0FBR0QsWUFBWSxHQUFHLENBQUMsR0FBSUQsYUFBYSxHQUFHQyxZQUFZLEdBQUksR0FBRyxHQUFHLENBQUM7b0JBQUEsT0FBQXVCLFNBQUEsQ0FBQS9HLE1BQUEsV0FFdkU7d0JBQ0xnSCxJQUFJLEVBQUU1RSxPQUFPLENBQUNhLGlCQUFpQjt3QkFDL0JnRSxRQUFRLEVBQUVKLFVBQVUsQ0FBQ0ssSUFBSTt3QkFDekJDLFFBQVEsRUFBRU4sVUFBVSxDQUFDTSxRQUFRO3dCQUM3QkMsVUFBVSxFQUFFOzRCQUNWQyxPQUFPLEVBQUU5QixhQUFhOzRCQUN0Qk0sS0FBSyxFQUFFTCxZQUFZOzRCQUNuQkMsVUFBVSxFQUFFNkIsSUFBSSxDQUFDQyxLQUFLLENBQUM5QixVQUFVLENBQUM7NEJBQ2xDK0IsU0FBUyxFQUFFRixJQUFJLENBQUNHLEdBQUcsQ0FBQyxDQUFDLEVBQUVqQyxZQUFZLEdBQUdELGFBQWE7d0JBQ3JELENBQUM7d0JBQ0RtQyxlQUFlLEVBQUV0RixPQUFPLENBQUNxRSxnQkFBZ0IsSUFBSXJFLE9BQU8sQ0FBQ2EsaUJBQWlCLEtBQUs7b0JBQzdFLENBQUM7Z0JBQUE7b0JBQUE4RCxTQUFBLENBQUEzSCxJQUFBO29CQUFBMkgsU0FBQSxDQUFBckYsRUFBQSxHQUFBcUYsU0FBQTtvQkFHRHpILE9BQU8sQ0FBQ04sS0FBSyxDQUFDLCtCQUErQixFQUFBK0gsU0FBQSxDQUFBckYsRUFBTyxDQUFDO29CQUFDLE9BQUFxRixTQUFBLENBQUEvRyxNQUFBLFdBQy9DLElBQUk7Z0JBQUE7Z0JBQUE7b0JBQUEsT0FBQStHLFNBQUEsQ0FBQXBGLElBQUE7WUFBQTtRQUFBLEdBQUFpRixRQUFBO1lBQUE7Z0JBQUE7Z0JBQUE7YUFBQTtTQUFBO0lBQUEsQ0FFZDtJQUFBLE9BQUFELGdCQUFBLENBQUF6SSxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQUtELFNBQXNCd0osa0JBQWtCQSxDQUFBQyxHQUFBO0lBQUEsT0FBQUMsbUJBQUEsQ0FBQTNKLEtBQUEsT0FBQUMsU0FBQTtBQUFBO0FBb0N4Qzs7Q0FFQSxHQUZBLFNBQUEwSixvQkFBQTtJQUFBQSxtQkFBQSxHQUFBekosNEtBQUEsZUFBQUMsOElBQUEsQ0FwQ08sU0FBQXlKLFNBQ0w1SCxRQUFnQjtRQUFBLElBQUE2SCxlQUFBLEVBQUF0SixRQUFBLEVBQUF1SixzQkFBQSxFQUFBckosSUFBQSxFQUFBQyxTQUFBLEVBQUFxSixVQUFBLEVBQUFDLFFBQUEsRUFBQUMsTUFBQSxHQUFBaEssU0FBQTtRQUFBLE9BQUFFLDhJQUFBLFVBQUErSixVQUFBQyxTQUFBO1lBQUEsZUFBQUEsU0FBQSxDQUFBakosSUFBQSxHQUFBaUosU0FBQSxDQUFBaEosSUFBQTtnQkFBQTtvQkFDaEIwSSxlQUF1QixHQUFBSSxNQUFBLENBQUE5RCxNQUFBLFFBQUE4RCxNQUFBLFFBQUFHLFNBQUEsR0FBQUgsTUFBQSxNQUFHLENBQUM7b0JBQUFFLFNBQUEsQ0FBQWpKLElBQUE7b0JBT25CWCxRQUFRLEdBQUdiLHFEQUFZLENBQUMsQ0FBQztvQkFBQXlLLFNBQUEsQ0FBQWhKLElBQUE7b0JBQUEsT0FFb0JaLFFBQVEsQ0FBQ2UsSUFBSSxDQUFDQyxPQUFPLENBQUMsQ0FBQztnQkFBQTtvQkFBQXVJLHNCQUFBLEdBQUFLLFNBQUEsQ0FBQTNJLElBQUE7b0JBQTFEZixJQUFJLEdBQUFxSixzQkFBQSxDQUFaeEosSUFBSSxDQUFJRyxJQUFJO29CQUFXQyxTQUFTLEdBQUFvSixzQkFBQSxDQUFoQmhKLEtBQUs7b0JBQUEsTUFFekJKLFNBQVMsSUFBSSxDQUFDRCxJQUFBQSxDQUFJO3dCQUFBMEosU0FBQSxDQUFBaEosSUFBQTt3QkFBQTtvQkFBQTtvQkFBQSxPQUFBZ0osU0FBQSxDQUFBckksTUFBQSxXQUNiO3dCQUFFSyxPQUFPLEVBQUUsS0FBSzt3QkFBRUMsTUFBTSxFQUFFO29CQUF5QixDQUFDO2dCQUFBO29CQUFBK0gsU0FBQSxDQUFBaEosSUFBQTtvQkFBQSxPQUdwQ1ksc0JBQXNCLENBQUN0QixJQUFJLENBQUNpQixFQUFFLEVBQUVNLFFBQVEsRUFBRTZILGVBQWUsQ0FBQztnQkFBQTtvQkFBN0VFLFVBQVUsR0FBQUksU0FBQSxDQUFBM0ksSUFBQTtvQkFBQSxJQUVYdUksVUFBVSxDQUFDNUgsT0FBTzt3QkFBQWdJLFNBQUEsQ0FBQWhKLElBQUE7d0JBQUE7b0JBQUE7b0JBQUFnSixTQUFBLENBQUFoSixJQUFBO29CQUFBLE9BQ0VxSCxlQUFlLENBQUMsQ0FBQztnQkFBQTtvQkFBbEN3QixRQUFRLEdBQUFHLFNBQUEsQ0FBQTNJLElBQUE7b0JBQUEsT0FBQTJJLFNBQUEsQ0FBQXJJLE1BQUEsV0FDUDt3QkFDTEssT0FBTyxFQUFFLEtBQUs7d0JBQ2RDLE1BQU0sRUFBRTJILFVBQVUsQ0FBQzNILE1BQU07d0JBQ3pCNEgsUUFBUSxFQUFSQTtvQkFDRixDQUFDO2dCQUFBO29CQUFBLE9BQUFHLFNBQUEsQ0FBQXJJLE1BQUEsV0FHSTt3QkFBRUssT0FBTyxFQUFFO29CQUFLLENBQUM7Z0JBQUE7b0JBQUFnSSxTQUFBLENBQUFqSixJQUFBO29CQUFBaUosU0FBQSxDQUFBM0csRUFBQSxHQUFBMkcsU0FBQTtvQkFHeEIvSSxPQUFPLENBQUNOLEtBQUssQ0FBQyxxQ0FBcUMsRUFBQXFKLFNBQUEsQ0FBQTNHLEVBQU8sQ0FBQztvQkFBQyxPQUFBMkcsU0FBQSxDQUFBckksTUFBQSxXQUNyRDt3QkFBRUssT0FBTyxFQUFFLEtBQUs7d0JBQUVDLE1BQU0sRUFBRTtvQkFBOEIsQ0FBQztnQkFBQTtnQkFBQTtvQkFBQSxPQUFBK0gsU0FBQSxDQUFBMUcsSUFBQTtZQUFBO1FBQUEsR0FBQW1HLFFBQUE7WUFBQTtnQkFBQTtnQkFBQTthQUFBO1NBQUE7SUFBQSxDQUVuRTtJQUFBLE9BQUFELG1CQUFBLENBQUEzSixLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQUtELFNBQXNCb0sscUJBQXFCQSxDQUFBO0lBQUEsT0FBQUMsc0JBQUEsQ0FBQXRLLEtBQUEsT0FBQUMsU0FBQTtBQUFBO0FBZ0YzQzs7Q0FFQSxHQUZBLFNBQUFxSyx1QkFBQTtJQUFBQSxzQkFBQSxHQUFBcEssNEtBQUEsZUFBQUMsOElBQUEsQ0FoRk8sU0FBQW9LLFNBQUE7UUFBQSxJQUFBaEssUUFBQSxFQUFBaUssc0JBQUEsRUFBQS9KLElBQUEsRUFBQUMsU0FBQSxFQUFBd0QsT0FBQSxFQUFBcUQsVUFBQSxFQUFBa0QsYUFBQSxFQUFBQyxzQkFBQSxFQUFBQyxVQUFBLEVBQUFDLFlBQUEsRUFBQUMsWUFBQSxFQUFBQyxRQUFBLEVBQUFDLENBQUEsRUFBQUMsSUFBQSxFQUFBQyxPQUFBO1FBQUEsT0FBQTlLLDhJQUFBLFVBQUErSyxVQUFBQyxTQUFBO1lBQUEsZUFBQUEsU0FBQSxDQUFBakssSUFBQSxHQUFBaUssU0FBQSxDQUFBaEssSUFBQTtnQkFBQTtvQkFBQWdLLFNBQUEsQ0FBQWpLLElBQUE7b0JBV0dYLFFBQVEsR0FBR2IscURBQVksQ0FBQyxDQUFDO29CQUFBeUwsU0FBQSxDQUFBaEssSUFBQTtvQkFBQSxPQUVvQlosUUFBUSxDQUFDZSxJQUFJLENBQUNDLE9BQU8sQ0FBQyxDQUFDO2dCQUFBO29CQUFBaUosc0JBQUEsR0FBQVcsU0FBQSxDQUFBM0osSUFBQTtvQkFBMURmLElBQUksR0FBQStKLHNCQUFBLENBQVpsSyxJQUFJLENBQUlHLElBQUk7b0JBQVdDLFNBQVMsR0FBQThKLHNCQUFBLENBQWhCMUosS0FBSztvQkFBQSxNQUV6QkosU0FBUyxJQUFJLENBQUNELElBQUFBLENBQUk7d0JBQUEwSyxTQUFBLENBQUFoSyxJQUFBO3dCQUFBO29CQUFBO29CQUFBLE9BQUFnSyxTQUFBLENBQUFySixNQUFBLFdBQ2IsSUFBSTtnQkFBQTtvQkFBQXFKLFNBQUEsQ0FBQWhLLElBQUE7b0JBQUEsT0FJU3dGLGNBQWMsQ0FBQyxDQUFDO2dCQUFBO29CQUFoQ3pDLE9BQU8sR0FBQWlILFNBQUEsQ0FBQTNKLElBQUE7b0JBQUEsSUFDUjBDLE9BQU87d0JBQUFpSCxTQUFBLENBQUFoSyxJQUFBO3dCQUFBO29CQUFBO29CQUFBLE9BQUFnSyxTQUFBLENBQUFySixNQUFBLFdBQ0gsSUFBSTtnQkFBQTtvQkFHYjtvQkFDTXlGLFVBQVUsR0FBSXJELE9BQU8sQ0FBQ2Usb0JBQW9CLEdBQUdmLE9BQU8sQ0FBQ2MsbUJBQW1CLEdBQUksR0FBRyxFQUVyRjtvQkFDTXlGLGFBQWEsR0FBRyxJQUFJdkgsSUFBSSxDQUFDLENBQUM7b0JBQ2hDdUgsYUFBYSxDQUFDVyxPQUFPLENBQUNYLGFBQWEsQ0FBQ1ksT0FBTyxDQUFDLENBQUMsR0FBRyxFQUFFLENBQUM7b0JBQUNGLFNBQUEsQ0FBQWhLLElBQUE7b0JBQUEsT0FFSVosUUFBUSxDQUM3RDhDLElBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUN4QnNCLE1BQU0sQ0FBQywwQkFBMEIsQ0FBQyxDQUNsQ0MsRUFBRSxDQUFDLFNBQVMsRUFBRW5FLElBQUksQ0FBQ2lCLEVBQUUsQ0FBQyxDQUN0QjRKLEdBQUcsQ0FBQyxZQUFZLEVBQUViLGFBQWEsQ0FBQ3RILFdBQVcsQ0FBQyxDQUFDLENBQUMsQ0FDOUMyQyxLQUFLLENBQUMsWUFBWSxFQUFFO3dCQUFFQyxTQUFTLEVBQUU7b0JBQUssQ0FBQyxDQUFDO2dCQUFBO29CQUFBMkUsc0JBQUEsR0FBQVMsU0FBQSxDQUFBM0osSUFBQTtvQkFMN0JtSixVQUFVLEdBQUFELHNCQUFBLENBQWhCcEssSUFBSTtvQkFBcUJzSyxZQUFZLEdBQUFGLHNCQUFBLENBQW5CNUosS0FBSztvQkFPL0IsSUFBSThKLFlBQVksRUFBRTt3QkFDaEJ4SixPQUFPLENBQUNOLEtBQUssQ0FBQyxvQ0FBb0MsRUFBRThKLFlBQVksQ0FBQztvQkFDbkU7b0JBRUE7b0JBQ01DLFlBQXFELEdBQUcsRUFBRTtvQkFDMURDLFFBQVEsR0FBRyxJQUFJUyxHQUFHLENBQWlCLENBQUM7b0JBRTFDLElBQUlaLFVBQVUsRUFBRTt3QkFDZEEsVUFBVSxDQUFDcEUsT0FBTyxDQUFDLFNBQUFDLE1BQU0sRUFBSTs0QkFDM0IsSUFBTXdFLElBQUksR0FBRyxJQUFJOUgsSUFBSSxDQUFDc0QsTUFBTSxDQUFDZ0YsVUFBVSxDQUFDLENBQUNySSxXQUFXLENBQUMsQ0FBQyxDQUFDc0ksS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQzs0QkFDcEUsSUFBTXBFLGFBQWEsR0FBR3lELFFBQVEsQ0FBQ1ksR0FBRyxDQUFDVixJQUFJLENBQUMsSUFBSSxDQUFDOzRCQUM3Q0YsUUFBUSxDQUFDYSxHQUFHLENBQUNYLElBQUksRUFBRTNELGFBQWEsR0FBR2IsTUFBTSxDQUFDMUQsWUFBWSxDQUFDO3dCQUN6RCxDQUFDLENBQUM7d0JBRUY7d0JBQ0EsSUFBU2lJLENBQUMsR0FBRyxFQUFFLEVBQUVBLENBQUMsSUFBSSxDQUFDLEVBQUVBLENBQUMsRUFBRSxDQUFFOzRCQUN0QkMsSUFBSSxHQUFHLElBQUk5SCxJQUFJLENBQUMsQ0FBQzs0QkFDdkI4SCxJQUFJLENBQUNJLE9BQU8sQ0FBQ0osSUFBSSxDQUFDSyxPQUFPLENBQUMsQ0FBQyxHQUFHTixDQUFDLENBQUM7NEJBQzFCRSxPQUFPLEdBQUdELElBQUksQ0FBQzdILFdBQVcsQ0FBQyxDQUFDLENBQUNzSSxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDOzRCQUNoRFosWUFBWSxDQUFDZSxJQUFJLENBQUM7Z0NBQ2hCWixJQUFJLEVBQUVDLE9BQU87Z0NBQ2JsSCxNQUFNLEVBQUUrRyxRQUFRLENBQUNZLEdBQUcsQ0FBQ1QsT0FBTyxDQUFDLElBQUk7NEJBQ25DLENBQUMsQ0FBQzt3QkFDSjtvQkFDRjtvQkFBQyxPQUFBRSxTQUFBLENBQUFySixNQUFBLFdBRU07d0JBQ0x5RixVQUFVLEVBQUU2QixJQUFJLENBQUNDLEtBQUssQ0FBQzlCLFVBQVUsQ0FBQzt3QkFDbENJLEtBQUssRUFBRXpELE9BQU8sQ0FBQ2MsbUJBQW1CO3dCQUNsQzZHLElBQUksRUFBRTNILE9BQU8sQ0FBQ2Usb0JBQW9CO3dCQUNsQ3FFLFNBQVMsRUFBRXBGLE9BQU8sQ0FBQ2MsbUJBQW1CLEdBQUdkLE9BQU8sQ0FBQ2Usb0JBQW9CO3dCQUNyRTRGLFlBQVksRUFBWkE7b0JBQ0YsQ0FBQztnQkFBQTtvQkFBQU0sU0FBQSxDQUFBakssSUFBQTtvQkFBQWlLLFNBQUEsQ0FBQTNILEVBQUEsR0FBQTJILFNBQUE7b0JBR0QvSixPQUFPLENBQUNOLEtBQUssQ0FBQyxpQ0FBaUMsRUFBQXFLLFNBQUEsQ0FBQTNILEVBQU8sQ0FBQztvQkFBQyxPQUFBMkgsU0FBQSxDQUFBckosTUFBQSxXQUNqRCxJQUFJO2dCQUFBO2dCQUFBO29CQUFBLE9BQUFxSixTQUFBLENBQUExSCxJQUFBO1lBQUE7UUFBQSxHQUFBOEcsUUFBQTtZQUFBO2dCQUFBO2dCQUFBO2FBQUE7U0FBQTtJQUFBLENBRWQ7SUFBQSxPQUFBRCxzQkFBQSxDQUFBdEssS0FBQSxPQUFBQyxTQUFBO0FBQUE7QUFLRCxTQUFzQjZMLHVCQUF1QkEsQ0FBQTtJQUFBLE9BQUFDLHdCQUFBLENBQUEvTCxLQUFBLE9BQUFDLFNBQUE7QUFBQTtBQWlDNUMsU0FBQThMLHlCQUFBO0lBQUFBLHdCQUFBLEdBQUE3TCw0S0FBQSxlQUFBQyw4SUFBQSxDQWpDTSxTQUFBNkwsVUFBQTtRQUFBLElBQUF6TCxRQUFBLEVBQUEwTCxzQkFBQSxFQUFBeEwsSUFBQSxFQUFBQyxTQUFBLEVBQUF3TCxzQkFBQSxFQUFBQyxTQUFBLEVBQUFyTCxLQUFBO1FBQUEsT0FBQVgsOElBQUEsVUFBQWlNLFdBQUFDLFVBQUE7WUFBQSxlQUFBQSxVQUFBLENBQUFuTCxJQUFBLEdBQUFtTCxVQUFBLENBQUFsTCxJQUFBO2dCQUFBO29CQUFBa0wsVUFBQSxDQUFBbkwsSUFBQTtvQkFRR1gsUUFBUSxHQUFHYixxREFBWSxDQUFDLENBQUM7b0JBQUEyTSxVQUFBLENBQUFsTCxJQUFBO29CQUFBLE9BRW9CWixRQUFRLENBQUNlLElBQUksQ0FBQ0MsT0FBTyxDQUFDLENBQUM7Z0JBQUE7b0JBQUEwSyxzQkFBQSxHQUFBSSxVQUFBLENBQUE3SyxJQUFBO29CQUExRGYsSUFBSSxHQUFBd0wsc0JBQUEsQ0FBWjNMLElBQUksQ0FBSUcsSUFBSTtvQkFBV0MsU0FBUyxHQUFBdUwsc0JBQUEsQ0FBaEJuTCxLQUFLO29CQUFBLE1BRXpCSixTQUFTLElBQUksQ0FBQ0QsSUFBQUEsQ0FBSTt3QkFBQTRMLFVBQUEsQ0FBQWxMLElBQUE7d0JBQUE7b0JBQUE7b0JBQUEsT0FBQWtMLFVBQUEsQ0FBQXZLLE1BQUEsV0FDYixJQUFJO2dCQUFBO29CQUFBdUssVUFBQSxDQUFBbEwsSUFBQTtvQkFBQSxPQUc0QlosUUFBUSxDQUM5QzhDLElBQUksQ0FBQyxpQkFBaUIsQ0FBQyxDQUN2QnNCLE1BQU0sQ0FBQyx1Q0FBdUMsQ0FBQyxDQUMvQ0MsRUFBRSxDQUFDLFNBQVMsRUFBRW5FLElBQUksQ0FBQ2lCLEVBQUUsQ0FBQyxDQUN0Qm9FLEtBQUssQ0FBQyxZQUFZLEVBQUU7d0JBQUVDLFNBQVMsRUFBRTtvQkFBTSxDQUFDLENBQUM7Z0JBQUE7b0JBQUFtRyxzQkFBQSxHQUFBRyxVQUFBLENBQUE3SyxJQUFBO29CQUo5QjJLLFNBQVMsR0FBQUQsc0JBQUEsQ0FBZjVMLElBQUk7b0JBQWFRLEtBQUssR0FBQW9MLHNCQUFBLENBQUxwTCxLQUFLO29CQUFBLEtBTTFCQSxLQUFLO3dCQUFBdUwsVUFBQSxDQUFBbEwsSUFBQTt3QkFBQTtvQkFBQTtvQkFDUEMsT0FBTyxDQUFDTixLQUFLLENBQUMsd0NBQXdDLEVBQUVBLEtBQUssQ0FBQztvQkFBQyxPQUFBdUwsVUFBQSxDQUFBdkssTUFBQSxXQUN4RCxJQUFJO2dCQUFBO29CQUFBLE9BQUF1SyxVQUFBLENBQUF2SyxNQUFBLFdBR05xSyxTQUFTLElBQUksRUFBRTtnQkFBQTtvQkFBQUUsVUFBQSxDQUFBbkwsSUFBQTtvQkFBQW1MLFVBQUEsQ0FBQTdJLEVBQUEsR0FBQTZJLFVBQUE7b0JBR3RCakwsT0FBTyxDQUFDTixLQUFLLENBQUMsbUNBQW1DLEVBQUF1TCxVQUFBLENBQUE3SSxFQUFPLENBQUM7b0JBQUMsT0FBQTZJLFVBQUEsQ0FBQXZLLE1BQUEsV0FDbkQsSUFBSTtnQkFBQTtnQkFBQTtvQkFBQSxPQUFBdUssVUFBQSxDQUFBNUksSUFBQTtZQUFBO1FBQUEsR0FBQXVJLFNBQUE7WUFBQTtnQkFBQTtnQkFBQTthQUFBO1NBQUE7SUFBQSxDQUVkO0lBQUEsT0FBQUQsd0JBQUEsQ0FBQS9MLEtBQUEsT0FBQUMsU0FBQTtBQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdXBhYmFzZVxcdG9rZW5Vc2FnZVNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnLi9jbGllbnQnO1xuaW1wb3J0IHsgVG9rZW5UcmFja2luZ0RhdGEgfSBmcm9tICdAL2xpYi9haS90b2tlblRyYWNrZXInO1xuaW1wb3J0IHsgZ2V0UGxhbkNvbmZpZ3VyYXRpb24sIGhhc0ZlYXR1cmVBY2Nlc3MgfSBmcm9tICdAL2xpYi91dGlscy9wbGFuTGltaXRzJztcblxuZXhwb3J0IGludGVyZmFjZSBUb2tlblVzYWdlUmVjb3JkIHtcbiAgaWQ6IHN0cmluZztcbiAgdXNlcl9pZDogc3RyaW5nO1xuICBhY3Rpdml0eV90eXBlOiBzdHJpbmc7XG4gIG1vZGVsX25hbWU6IHN0cmluZztcbiAgcHJvbXB0X3Rva2VuczogbnVtYmVyO1xuICBjb21wbGV0aW9uX3Rva2VuczogbnVtYmVyO1xuICB0b3RhbF90b2tlbnM6IG51bWJlcjtcbiAgZXN0aW1hdGVkX2Nvc3Q6IG51bWJlcjtcbiAgdXNhZ2VfbW9udGg6IHN0cmluZztcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFRva2VuVXNhZ2VTdGF0cyB7XG4gIHRvdGFsU2Vzc2lvbnM6IG51bWJlcjtcbiAgdG90YWxUb2tlbnM6IG51bWJlcjtcbiAgdG90YWxDb3N0OiBudW1iZXI7XG4gIGJ5QWN0aXZpdHk6IFJlY29yZDxzdHJpbmcsIHsgdG9rZW5zOiBudW1iZXI7IGNvc3Q6IG51bWJlcjsgY291bnQ6IG51bWJlciB9PjtcbiAgYnlNb2RlbDogUmVjb3JkPHN0cmluZywgeyB0b2tlbnM6IG51bWJlcjsgY29zdDogbnVtYmVyOyBjb3VudDogbnVtYmVyIH0+O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVzZXJQcm9maWxlIHtcbiAgaWQ6IHN0cmluZztcbiAgdXNlcl9pZDogc3RyaW5nO1xuICBzdWJzY3JpcHRpb25fcGxhbjogJ2ZyZWUnIHwgJ3VzdWFyaW8nIHwgJ3Bybyc7XG4gIG1vbnRobHlfdG9rZW5fbGltaXQ6IG51bWJlcjtcbiAgY3VycmVudF9tb250aF90b2tlbnM6IG51bWJlcjtcbiAgY3VycmVudF9tb250aDogc3RyaW5nO1xuICBwYXltZW50X3ZlcmlmaWVkOiBib29sZWFuO1xuICBjcmVhdGVkX2F0OiBzdHJpbmc7XG4gIHVwZGF0ZWRfYXQ6IHN0cmluZztcbn1cblxuLyoqXG4gKiBHdWFyZGEgZWwgdXNvIGRlIHRva2VucyBlbiBTdXBhYmFzZSBjb24gdmFsaWRhY2nDs24gZGUgcGxhblxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gc2F2ZVRva2VuVXNhZ2UoZGF0YTogVG9rZW5UcmFja2luZ0RhdGEpOiBQcm9taXNlPHZvaWQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zb2xlLmxvZygn8J+UhCBzYXZlVG9rZW5Vc2FnZSAoY2xpZW50ZSkgaW5pY2lhZG8gY29uIGRhdGE6JywgZGF0YSk7XG5cbiAgICAvLyBFc3RlIHNlcnZpY2lvIHNvbG8gZnVuY2lvbmEgZW4gZWwgY2xpZW50ZVxuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KCk7XG4gICAgY29uc29sZS5sb2coJ+KchSBDbGllbnRlIFN1cGFiYXNlIGNyZWFkbycpO1xuXG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSwgZXJyb3I6IHVzZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG4gICAgY29uc29sZS5sb2coJ/CfkaQgVXN1YXJpbyBvYnRlbmlkbzonLCB1c2VyID8gYElEOiAke3VzZXIuaWR9LCBFbWFpbDogJHt1c2VyLmVtYWlsfWAgOiAnTm8gYXV0ZW50aWNhZG8nKTtcblxuICAgIGlmICh1c2VyRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIGNvbnNvbGUud2Fybign4p2MIE5vIGhheSB1c3VhcmlvIGF1dGVudGljYWRvIHBhcmEgZ3VhcmRhciB0b2tlbnM6JywgdXNlckVycm9yPy5tZXNzYWdlKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBWYWxpZGFyIHF1ZSBlbCB1c3VhcmlvIHRpZW5lIGFjY2VzbyBhIGxhIGFjdGl2aWRhZFxuICAgIGNvbnN0IGFjY2Vzc1ZhbGlkYXRpb24gPSBhd2FpdCB2YWxpZGF0ZUFjdGl2aXR5QWNjZXNzKHVzZXIuaWQsIGRhdGEuYWN0aXZpdHksIGRhdGEudXNhZ2UudG90YWxUb2tlbnMpO1xuICAgIGlmICghYWNjZXNzVmFsaWRhdGlvbi5hbGxvd2VkKSB7XG4gICAgICBjb25zb2xlLndhcm4oJ+KdjCBBY2Nlc28gZGVuZWdhZG8gcGFyYSBhY3RpdmlkYWQ6JywgYWNjZXNzVmFsaWRhdGlvbi5yZWFzb24pO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGFjY2Vzc1ZhbGlkYXRpb24ucmVhc29uKTtcbiAgICB9XG5cbiAgICBjb25zdCB1c2FnZVJlY29yZCA9IHtcbiAgICAgIHVzZXJfaWQ6IHVzZXIuaWQsXG4gICAgICBhY3Rpdml0eV90eXBlOiBkYXRhLmFjdGl2aXR5LFxuICAgICAgbW9kZWxfbmFtZTogZGF0YS5tb2RlbCxcbiAgICAgIHByb21wdF90b2tlbnM6IGRhdGEudXNhZ2UucHJvbXB0VG9rZW5zLFxuICAgICAgY29tcGxldGlvbl90b2tlbnM6IGRhdGEudXNhZ2UuY29tcGxldGlvblRva2VucyxcbiAgICAgIHRvdGFsX3Rva2VuczogZGF0YS51c2FnZS50b3RhbFRva2VucyxcbiAgICAgIGVzdGltYXRlZF9jb3N0OiBkYXRhLnVzYWdlLmVzdGltYXRlZENvc3QgfHwgMCxcbiAgICAgIHVzYWdlX21vbnRoOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgNykgKyAnLTAxJyAvLyBZWVlZLU1NLTAxXG4gICAgfTtcblxuICAgIGNvbnNvbGUubG9nKCfwn5OdIFJlZ2lzdHJvIGEgaW5zZXJ0YXI6JywgdXNhZ2VSZWNvcmQpO1xuXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd1c2VyX3Rva2VuX3VzYWdlJylcbiAgICAgIC5pbnNlcnQoW3VzYWdlUmVjb3JkXSk7XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBhbCBndWFyZGFyIHVzbyBkZSB0b2tlbnM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfinIUgUmVnaXN0cm8gaW5zZXJ0YWRvIGV4aXRvc2FtZW50ZSBlbiB1c2VyX3Rva2VuX3VzYWdlJyk7XG5cbiAgICAvLyBBY3R1YWxpemFyIGNvbnRhZG9yIG1lbnN1YWwgZGVsIHVzdWFyaW9cbiAgICBhd2FpdCB1cGRhdGVNb250aGx5VG9rZW5Db3VudCh1c2VyLmlkLCBkYXRhLnVzYWdlLnRvdGFsVG9rZW5zKTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGVuIHNhdmVUb2tlblVzYWdlOicsIGVycm9yKTtcbiAgfVxufVxuXG4vKipcbiAqIEFjdHVhbGl6YSBlbCBjb250YWRvciBtZW5zdWFsIGRlIHRva2VucyBkZWwgdXN1YXJpb1xuICovXG5hc3luYyBmdW5jdGlvbiB1cGRhdGVNb250aGx5VG9rZW5Db3VudCh1c2VySWQ6IHN0cmluZywgdG9rZW5zOiBudW1iZXIpOiBQcm9taXNlPHZvaWQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCA3KSArICctMDEnO1xuXG4gICAgLy8gT2J0ZW5lciBvIGNyZWFyIHBlcmZpbCBkZWwgdXN1YXJpb1xuICAgIGxldCB7IGRhdGE6IHByb2ZpbGUsIGVycm9yOiBwcm9maWxlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgIC5zaW5nbGUoKTtcblxuICAgIGlmIChwcm9maWxlRXJyb3IgJiYgcHJvZmlsZUVycm9yLmNvZGUgIT09ICdQR1JTVDExNicpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIG9idGVuZXIgcGVyZmlsOicsIHByb2ZpbGVFcnJvcik7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKCFwcm9maWxlKSB7XG4gICAgICAvLyBDcmVhciBwZXJmaWwgbnVldm9cbiAgICAgIGNvbnN0IHsgZXJyb3I6IGluc2VydEVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC5pbnNlcnQoW3tcbiAgICAgICAgICB1c2VyX2lkOiB1c2VySWQsXG4gICAgICAgICAgc3Vic2NyaXB0aW9uX3BsYW46ICdmcmVlJyxcbiAgICAgICAgICBtb250aGx5X3Rva2VuX2xpbWl0OiA1MDAwMCxcbiAgICAgICAgICBjdXJyZW50X21vbnRoX3Rva2VuczogdG9rZW5zLFxuICAgICAgICAgIGN1cnJlbnRfbW9udGg6IGN1cnJlbnRNb250aFxuICAgICAgICB9XSk7XG5cbiAgICAgIGlmIChpbnNlcnRFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBjcmVhciBwZXJmaWw6JywgaW5zZXJ0RXJyb3IpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBBY3R1YWxpemFyIHBlcmZpbCBleGlzdGVudGVcbiAgICAgIGNvbnN0IG5ld1Rva2VuQ291bnQgPSBwcm9maWxlLmN1cnJlbnRfbW9udGggPT09IGN1cnJlbnRNb250aCBcbiAgICAgICAgPyBwcm9maWxlLmN1cnJlbnRfbW9udGhfdG9rZW5zICsgdG9rZW5zIFxuICAgICAgICA6IHRva2VuczsgLy8gUmVzZXQgc2kgZXMgbnVldm8gbWVzXG5cbiAgICAgIGNvbnN0IHsgZXJyb3I6IHVwZGF0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC51cGRhdGUoe1xuICAgICAgICAgIGN1cnJlbnRfbW9udGhfdG9rZW5zOiBuZXdUb2tlbkNvdW50LFxuICAgICAgICAgIGN1cnJlbnRfbW9udGg6IGN1cnJlbnRNb250aCxcbiAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgfSlcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKTtcblxuICAgICAgaWYgKHVwZGF0ZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGFsIGFjdHVhbGl6YXIgcGVyZmlsOicsIHVwZGF0ZUVycm9yKTtcbiAgICAgIH1cbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZW4gdXBkYXRlTW9udGhseVRva2VuQ291bnQ6JywgZXJyb3IpO1xuICB9XG59XG5cbi8qKlxuICogT2J0aWVuZSBlc3RhZMOtc3RpY2FzIGRlIHVzbyBkZSB0b2tlbnMgZGVsIHVzdWFyaW8gYWN0dWFsXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRVc2VyVG9rZW5TdGF0cygpOiBQcm9taXNlPFRva2VuVXNhZ2VTdGF0cz4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KCk7XG4gICAgXG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSwgZXJyb3I6IHVzZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG4gICAgXG4gICAgaWYgKHVzZXJFcnJvciB8fCAhdXNlcikge1xuICAgICAgcmV0dXJuIGdldEVtcHR5U3RhdHMoKTtcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IHJlY29yZHMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfdG9rZW5fdXNhZ2UnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIGVzdGFkw61zdGljYXM6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIGdldEVtcHR5U3RhdHMoKTtcbiAgICB9XG5cbiAgICByZXR1cm4gY2FsY3VsYXRlU3RhdHMocmVjb3JkcyB8fCBbXSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbiBnZXRVc2VyVG9rZW5TdGF0czonLCBlcnJvcik7XG4gICAgcmV0dXJuIGdldEVtcHR5U3RhdHMoKTtcbiAgfVxufVxuXG4vKipcbiAqIENhbGN1bGEgZXN0YWTDrXN0aWNhcyBhIHBhcnRpciBkZSBsb3MgcmVnaXN0cm9zXG4gKi9cbmZ1bmN0aW9uIGNhbGN1bGF0ZVN0YXRzKHJlY29yZHM6IFRva2VuVXNhZ2VSZWNvcmRbXSk6IFRva2VuVXNhZ2VTdGF0cyB7XG4gIGNvbnN0IHN0YXRzOiBUb2tlblVzYWdlU3RhdHMgPSB7XG4gICAgdG90YWxTZXNzaW9uczogcmVjb3Jkcy5sZW5ndGgsXG4gICAgdG90YWxUb2tlbnM6IDAsXG4gICAgdG90YWxDb3N0OiAwLFxuICAgIGJ5QWN0aXZpdHk6IHt9LFxuICAgIGJ5TW9kZWw6IHt9XG4gIH07XG5cbiAgcmVjb3Jkcy5mb3JFYWNoKHJlY29yZCA9PiB7XG4gICAgY29uc3QgdG9rZW5zID0gcmVjb3JkLnRvdGFsX3Rva2VucztcbiAgICBjb25zdCBjb3N0ID0gcmVjb3JkLmVzdGltYXRlZF9jb3N0O1xuXG4gICAgc3RhdHMudG90YWxUb2tlbnMgKz0gdG9rZW5zO1xuICAgIHN0YXRzLnRvdGFsQ29zdCArPSBjb3N0O1xuXG4gICAgLy8gUG9yIGFjdGl2aWRhZFxuICAgIGlmICghc3RhdHMuYnlBY3Rpdml0eVtyZWNvcmQuYWN0aXZpdHlfdHlwZV0pIHtcbiAgICAgIHN0YXRzLmJ5QWN0aXZpdHlbcmVjb3JkLmFjdGl2aXR5X3R5cGVdID0geyB0b2tlbnM6IDAsIGNvc3Q6IDAsIGNvdW50OiAwIH07XG4gICAgfVxuICAgIHN0YXRzLmJ5QWN0aXZpdHlbcmVjb3JkLmFjdGl2aXR5X3R5cGVdLnRva2VucyArPSB0b2tlbnM7XG4gICAgc3RhdHMuYnlBY3Rpdml0eVtyZWNvcmQuYWN0aXZpdHlfdHlwZV0uY29zdCArPSBjb3N0O1xuICAgIHN0YXRzLmJ5QWN0aXZpdHlbcmVjb3JkLmFjdGl2aXR5X3R5cGVdLmNvdW50ICs9IDE7XG5cbiAgICAvLyBQb3IgbW9kZWxvXG4gICAgaWYgKCFzdGF0cy5ieU1vZGVsW3JlY29yZC5tb2RlbF9uYW1lXSkge1xuICAgICAgc3RhdHMuYnlNb2RlbFtyZWNvcmQubW9kZWxfbmFtZV0gPSB7IHRva2VuczogMCwgY29zdDogMCwgY291bnQ6IDAgfTtcbiAgICB9XG4gICAgc3RhdHMuYnlNb2RlbFtyZWNvcmQubW9kZWxfbmFtZV0udG9rZW5zICs9IHRva2VucztcbiAgICBzdGF0cy5ieU1vZGVsW3JlY29yZC5tb2RlbF9uYW1lXS5jb3N0ICs9IGNvc3Q7XG4gICAgc3RhdHMuYnlNb2RlbFtyZWNvcmQubW9kZWxfbmFtZV0uY291bnQgKz0gMTtcbiAgfSk7XG5cbiAgcmV0dXJuIHN0YXRzO1xufVxuXG4vKipcbiAqIFJldG9ybmEgZXN0YWTDrXN0aWNhcyB2YWPDrWFzXG4gKi9cbmZ1bmN0aW9uIGdldEVtcHR5U3RhdHMoKTogVG9rZW5Vc2FnZVN0YXRzIHtcbiAgcmV0dXJuIHtcbiAgICB0b3RhbFNlc3Npb25zOiAwLFxuICAgIHRvdGFsVG9rZW5zOiAwLFxuICAgIHRvdGFsQ29zdDogMCxcbiAgICBieUFjdGl2aXR5OiB7fSxcbiAgICBieU1vZGVsOiB7fVxuICB9O1xufVxuXG4vKipcbiAqIE9idGllbmUgZWwgcGVyZmlsIGRlbCB1c3VhcmlvIGFjdHVhbFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlclByb2ZpbGUoKTogUHJvbWlzZTxVc2VyUHJvZmlsZSB8IG51bGw+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuICAgIFxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiB1c2VyRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuICAgIFxuICAgIGlmICh1c2VyRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YTogcHJvZmlsZSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAuc2VsZWN0KCcqJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuc2luZ2xlKCk7XG5cbiAgICBpZiAoZXJyb3IgJiYgZXJyb3IuY29kZSAhPT0gJ1BHUlNUMTE2Jykge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgb2J0ZW5lciBwZXJmaWw6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgcmV0dXJuIHByb2ZpbGU7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbiBnZXRVc2VyUHJvZmlsZTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuLyoqXG4gKiBWZXJpZmljYSBzaSBlbCB1c3VhcmlvIGhhIGFsY2FuemFkbyBzdSBsw61taXRlIG1lbnN1YWxcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNoZWNrVG9rZW5MaW1pdCgpOiBQcm9taXNlPHsgXG4gIGhhc1JlYWNoZWRMaW1pdDogYm9vbGVhbjsgXG4gIGN1cnJlbnRUb2tlbnM6IG51bWJlcjsgXG4gIGxpbWl0OiBudW1iZXI7IFxuICBwZXJjZW50YWdlOiBudW1iZXIgXG59PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcHJvZmlsZSA9IGF3YWl0IGdldFVzZXJQcm9maWxlKCk7XG4gICAgXG4gICAgaWYgKCFwcm9maWxlKSB7XG4gICAgICByZXR1cm4geyBoYXNSZWFjaGVkTGltaXQ6IGZhbHNlLCBjdXJyZW50VG9rZW5zOiAwLCBsaW1pdDogNTAwMDAsIHBlcmNlbnRhZ2U6IDAgfTtcbiAgICB9XG5cbiAgICBjb25zdCBjdXJyZW50VG9rZW5zID0gcHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucyB8fCAwO1xuICAgIGNvbnN0IG1vbnRobHlMaW1pdCA9IHByb2ZpbGUubW9udGhseV90b2tlbl9saW1pdCB8fCAwO1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBtb250aGx5TGltaXQgPiAwID8gKGN1cnJlbnRUb2tlbnMgLyBtb250aGx5TGltaXQpICogMTAwIDogMDtcbiAgICBjb25zdCBoYXNSZWFjaGVkTGltaXQgPSBjdXJyZW50VG9rZW5zID49IG1vbnRobHlMaW1pdDtcblxuICAgIHJldHVybiB7XG4gICAgICBoYXNSZWFjaGVkTGltaXQsXG4gICAgICBjdXJyZW50VG9rZW5zLFxuICAgICAgbGltaXQ6IG1vbnRobHlMaW1pdCxcbiAgICAgIHBlcmNlbnRhZ2VcbiAgICB9O1xuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgZW4gY2hlY2tUb2tlbkxpbWl0OicsIGVycm9yKTtcbiAgICByZXR1cm4geyBoYXNSZWFjaGVkTGltaXQ6IGZhbHNlLCBjdXJyZW50VG9rZW5zOiAwLCBsaW1pdDogNTAwMDAsIHBlcmNlbnRhZ2U6IDAgfTtcbiAgfVxufVxuXG4vKipcbiAqIFZhbGlkYSBzaSB1biB1c3VhcmlvIHRpZW5lIGFjY2VzbyBhIHVuYSBhY3RpdmlkYWQgZXNwZWPDrWZpY2FcbiAqL1xuYXN5bmMgZnVuY3Rpb24gdmFsaWRhdGVBY3Rpdml0eUFjY2VzcyhcbiAgdXNlcklkOiBzdHJpbmcsXG4gIGFjdGl2aXR5OiBzdHJpbmcsXG4gIHRva2Vuc1RvVXNlOiBudW1iZXJcbik6IFByb21pc2U8eyBhbGxvd2VkOiBib29sZWFuOyByZWFzb24/OiBzdHJpbmcgfT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KCk7XG5cbiAgICAvLyBPYnRlbmVyIHBlcmZpbCBkZWwgdXN1YXJpb1xuICAgIGNvbnN0IHsgZGF0YTogcHJvZmlsZSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAuc2VsZWN0KCdzdWJzY3JpcHRpb25fcGxhbiwgcGF5bWVudF92ZXJpZmllZCwgY3VycmVudF9tb250aF90b2tlbnMsIG1vbnRobHlfdG9rZW5fbGltaXQsIGN1cnJlbnRfbW9udGgnKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlcklkKVxuICAgICAgLnNpbmdsZSgpO1xuXG4gICAgaWYgKGVycm9yIHx8ICFwcm9maWxlKSB7XG4gICAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiAnUGVyZmlsIGRlIHVzdWFyaW8gbm8gZW5jb250cmFkbycgfTtcbiAgICB9XG5cbiAgICAvLyBNYXBlYXIgYWN0aXZpZGFkZXMgYSBjYXJhY3RlcsOtc3RpY2FzXG4gICAgY29uc3QgYWN0aXZpdHlUb0ZlYXR1cmU6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgICAndGVzdF9nZW5lcmF0aW9uJzogJ3Rlc3RfZ2VuZXJhdGlvbicsXG4gICAgICAnZmxhc2hjYXJkX2dlbmVyYXRpb24nOiAnZmxhc2hjYXJkX2dlbmVyYXRpb24nLFxuICAgICAgJ21pbmRfbWFwX2dlbmVyYXRpb24nOiAnbWluZF9tYXBfZ2VuZXJhdGlvbicsXG4gICAgICAnYWlfY2hhdCc6ICdhaV90dXRvcl9jaGF0JyxcbiAgICAgICdzdHVkeV9wbGFubmluZyc6ICdzdHVkeV9wbGFubmluZycsXG4gICAgICAnc3VtbWFyeV9nZW5lcmF0aW9uJzogJ3N1bW1hcnlfYTFfYTInLFxuICAgICAgJ2RvY3VtZW50X2FuYWx5c2lzJzogJ2RvY3VtZW50X3VwbG9hZCdcbiAgICB9O1xuXG4gICAgY29uc3QgZmVhdHVyZU5hbWUgPSBhY3Rpdml0eVRvRmVhdHVyZVthY3Rpdml0eV0gfHwgYWN0aXZpdHk7XG5cbiAgICAvLyBWZXJpZmljYXIgYWNjZXNvIGEgbGEgY2FyYWN0ZXLDrXN0aWNhIHNlZ8O6biBlbCBwbGFuXG4gICAgaWYgKCFoYXNGZWF0dXJlQWNjZXNzKHByb2ZpbGUuc3Vic2NyaXB0aW9uX3BsYW4sIGZlYXR1cmVOYW1lKSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogYExhIGFjdGl2aWRhZCAke2FjdGl2aXR5fSBubyBlc3TDoSBkaXNwb25pYmxlIGVuIGVsIHBsYW4gJHtwcm9maWxlLnN1YnNjcmlwdGlvbl9wbGFufWBcbiAgICAgIH07XG4gICAgfVxuXG4gICAgLy8gVmVyaWZpY2FyIHBhZ28gcGFyYSBwbGFuZXMgZGUgcGFnb1xuICAgIGlmIChwcm9maWxlLnN1YnNjcmlwdGlvbl9wbGFuICE9PSAnZnJlZScgJiYgIXByb2ZpbGUucGF5bWVudF92ZXJpZmllZCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogJ1BhZ28gbm8gdmVyaWZpY2Fkby4gQ29tcGxldGUgZWwgcHJvY2VzbyBkZSBwYWdvIHBhcmEgdXNhciBlc3RhIGZ1bmNpw7NuLidcbiAgICAgIH07XG4gICAgfVxuXG4gICAgLy8gVmVyaWZpY2FyIGzDrW1pdGVzIGRlIHRva2Vuc1xuICAgIGNvbnN0IGN1cnJlbnRNb250aCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCA3KSArICctMDEnO1xuICAgIGxldCBjdXJyZW50VG9rZW5zID0gcHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucztcblxuICAgIC8vIFJlc2V0IHNpIGVzIG51ZXZvIG1lc1xuICAgIGlmIChwcm9maWxlLmN1cnJlbnRfbW9udGggIT09IGN1cnJlbnRNb250aCkge1xuICAgICAgY3VycmVudFRva2VucyA9IDA7XG4gICAgfVxuXG4gICAgaWYgKGN1cnJlbnRUb2tlbnMgKyB0b2tlbnNUb1VzZSA+IHByb2ZpbGUubW9udGhseV90b2tlbl9saW1pdCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogYEzDrW1pdGUgbWVuc3VhbCBkZSB0b2tlbnMgYWxjYW56YWRvLiBVc2FkbzogJHtjdXJyZW50VG9rZW5zfS8ke3Byb2ZpbGUubW9udGhseV90b2tlbl9saW1pdH1gXG4gICAgICB9O1xuICAgIH1cblxuICAgIHJldHVybiB7IGFsbG93ZWQ6IHRydWUgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHZhbGlkYXRpbmcgYWN0aXZpdHkgYWNjZXNzOicsIGVycm9yKTtcbiAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiAnRXJyb3IgaW50ZXJubyBkZSB2YWxpZGFjacOzbicgfTtcbiAgfVxufVxuXG4vKipcbiAqIE9idGllbmUgaW5mb3JtYWNpw7NuIGRldGFsbGFkYSBkZWwgcGxhbiBkZWwgdXN1YXJpb1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VXNlclBsYW5JbmZvKCk6IFByb21pc2U8e1xuICBwbGFuOiBzdHJpbmc7XG4gIHBsYW5OYW1lOiBzdHJpbmc7XG4gIGZlYXR1cmVzOiBzdHJpbmdbXTtcbiAgdG9rZW5Vc2FnZToge1xuICAgIGN1cnJlbnQ6IG51bWJlcjtcbiAgICBsaW1pdDogbnVtYmVyO1xuICAgIHBlcmNlbnRhZ2U6IG51bWJlcjtcbiAgICByZW1haW5pbmc6IG51bWJlcjtcbiAgfTtcbiAgcGF5bWVudFZlcmlmaWVkOiBib29sZWFuO1xufSB8IG51bGw+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBwcm9maWxlID0gYXdhaXQgZ2V0VXNlclByb2ZpbGUoKTtcblxuICAgIGlmICghcHJvZmlsZSkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuXG4gICAgY29uc3QgcGxhbkNvbmZpZyA9IGdldFBsYW5Db25maWd1cmF0aW9uKHByb2ZpbGUuc3Vic2NyaXB0aW9uX3BsYW4pO1xuXG4gICAgaWYgKCFwbGFuQ29uZmlnKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICBjb25zdCBjdXJyZW50VG9rZW5zID0gcHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucyB8fCAwO1xuICAgIGNvbnN0IG1vbnRobHlMaW1pdCA9IHByb2ZpbGUubW9udGhseV90b2tlbl9saW1pdCB8fCAwO1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSBtb250aGx5TGltaXQgPiAwID8gKGN1cnJlbnRUb2tlbnMgLyBtb250aGx5TGltaXQpICogMTAwIDogMDtcblxuICAgIHJldHVybiB7XG4gICAgICBwbGFuOiBwcm9maWxlLnN1YnNjcmlwdGlvbl9wbGFuLFxuICAgICAgcGxhbk5hbWU6IHBsYW5Db25maWcubmFtZSxcbiAgICAgIGZlYXR1cmVzOiBwbGFuQ29uZmlnLmZlYXR1cmVzLFxuICAgICAgdG9rZW5Vc2FnZToge1xuICAgICAgICBjdXJyZW50OiBjdXJyZW50VG9rZW5zLFxuICAgICAgICBsaW1pdDogbW9udGhseUxpbWl0LFxuICAgICAgICBwZXJjZW50YWdlOiBNYXRoLnJvdW5kKHBlcmNlbnRhZ2UpLFxuICAgICAgICByZW1haW5pbmc6IE1hdGgubWF4KDAsIG1vbnRobHlMaW1pdCAtIGN1cnJlbnRUb2tlbnMpXG4gICAgICB9LFxuICAgICAgcGF5bWVudFZlcmlmaWVkOiBwcm9maWxlLnBheW1lbnRfdmVyaWZpZWQgfHwgcHJvZmlsZS5zdWJzY3JpcHRpb25fcGxhbiA9PT0gJ2ZyZWUnXG4gICAgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGdldHRpbmcgdXNlciBwbGFuIGluZm86JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogVmVyaWZpY2Egc2kgZWwgdXN1YXJpbyBwdWVkZSByZWFsaXphciB1bmEgYWN0aXZpZGFkIGVzcGVjw61maWNhIGFudGVzIGRlIGVqZWN1dGFybGFcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNhblBlcmZvcm1BY3Rpdml0eShcbiAgYWN0aXZpdHk6IHN0cmluZyxcbiAgZXN0aW1hdGVkVG9rZW5zOiBudW1iZXIgPSAwXG4pOiBQcm9taXNlPHtcbiAgYWxsb3dlZDogYm9vbGVhbjtcbiAgcmVhc29uPzogc3RyaW5nO1xuICBwbGFuSW5mbz86IGFueTtcbn0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudCgpO1xuXG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSwgZXJyb3I6IHVzZXJFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XG5cbiAgICBpZiAodXNlckVycm9yIHx8ICF1c2VyKSB7XG4gICAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiAnVXN1YXJpbyBubyBhdXRlbnRpY2FkbycgfTtcbiAgICB9XG5cbiAgICBjb25zdCB2YWxpZGF0aW9uID0gYXdhaXQgdmFsaWRhdGVBY3Rpdml0eUFjY2Vzcyh1c2VyLmlkLCBhY3Rpdml0eSwgZXN0aW1hdGVkVG9rZW5zKTtcblxuICAgIGlmICghdmFsaWRhdGlvbi5hbGxvd2VkKSB7XG4gICAgICBjb25zdCBwbGFuSW5mbyA9IGF3YWl0IGdldFVzZXJQbGFuSW5mbygpO1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgIHJlYXNvbjogdmFsaWRhdGlvbi5yZWFzb24sXG4gICAgICAgIHBsYW5JbmZvXG4gICAgICB9O1xuICAgIH1cblxuICAgIHJldHVybiB7IGFsbG93ZWQ6IHRydWUgfTtcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNoZWNraW5nIGFjdGl2aXR5IHBlcm1pc3Npb246JywgZXJyb3IpO1xuICAgIHJldHVybiB7IGFsbG93ZWQ6IGZhbHNlLCByZWFzb246ICdFcnJvciBpbnRlcm5vIGRlIHZhbGlkYWNpw7NuJyB9O1xuICB9XG59XG5cbi8qKlxuICogT2J0aWVuZSBkYXRvcyBkZSBwcm9ncmVzbyBkZSB0b2tlbnMgcGFyYSBlc3RhZMOtc3RpY2FzIGF2YW56YWRhc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0VG9rZW5Vc2FnZVByb2dyZXNzKCk6IFByb21pc2U8e1xuICBwZXJjZW50YWdlOiBudW1iZXI7XG4gIGxpbWl0OiBudW1iZXI7XG4gIHVzZWQ6IG51bWJlcjtcbiAgcmVtYWluaW5nOiBudW1iZXI7XG4gIGRhaWx5SGlzdG9yeTogQXJyYXk8e1xuICAgIGRhdGU6IHN0cmluZztcbiAgICB0b2tlbnM6IG51bWJlcjtcbiAgfT47XG59IHwgbnVsbD4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KCk7XG5cbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcblxuICAgIGlmICh1c2VyRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIC8vIE9idGVuZXIgcGVyZmlsIGRlbCB1c3VhcmlvXG4gICAgY29uc3QgcHJvZmlsZSA9IGF3YWl0IGdldFVzZXJQcm9maWxlKCk7XG4gICAgaWYgKCFwcm9maWxlKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG5cbiAgICAvLyBDYWxjdWxhciBwb3JjZW50YWplIGRlIHVzb1xuICAgIGNvbnN0IHBlcmNlbnRhZ2UgPSAocHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucyAvIHByb2ZpbGUubW9udGhseV90b2tlbl9saW1pdCkgKiAxMDA7XG5cbiAgICAvLyBPYnRlbmVyIGhpc3RvcmlhbCBkaWFyaW8gZGUgbG9zIMO6bHRpbW9zIDMwIGTDrWFzXG4gICAgY29uc3QgdGhpcnR5RGF5c0FnbyA9IG5ldyBEYXRlKCk7XG4gICAgdGhpcnR5RGF5c0Fnby5zZXREYXRlKHRoaXJ0eURheXNBZ28uZ2V0RGF0ZSgpIC0gMzApO1xuXG4gICAgY29uc3QgeyBkYXRhOiBkYWlseVVzYWdlLCBlcnJvcjogaGlzdG9yeUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ3VzZXJfdG9rZW5fdXNhZ2UnKVxuICAgICAgLnNlbGVjdCgnY3JlYXRlZF9hdCwgdG90YWxfdG9rZW5zJylcbiAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXIuaWQpXG4gICAgICAuZ3RlKCdjcmVhdGVkX2F0JywgdGhpcnR5RGF5c0Fnby50b0lTT1N0cmluZygpKVxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IHRydWUgfSk7XG5cbiAgICBpZiAoaGlzdG9yeUVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIGhpc3RvcmlhbCBkaWFyaW86JywgaGlzdG9yeUVycm9yKTtcbiAgICB9XG5cbiAgICAvLyBBZ3J1cGFyIHBvciBkw61hXG4gICAgY29uc3QgZGFpbHlIaXN0b3J5OiBBcnJheTx7IGRhdGU6IHN0cmluZzsgdG9rZW5zOiBudW1iZXIgfT4gPSBbXTtcbiAgICBjb25zdCBkYWlseU1hcCA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XG5cbiAgICBpZiAoZGFpbHlVc2FnZSkge1xuICAgICAgZGFpbHlVc2FnZS5mb3JFYWNoKHJlY29yZCA9PiB7XG4gICAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZShyZWNvcmQuY3JlYXRlZF9hdCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgICBjb25zdCBjdXJyZW50VG9rZW5zID0gZGFpbHlNYXAuZ2V0KGRhdGUpIHx8IDA7XG4gICAgICAgIGRhaWx5TWFwLnNldChkYXRlLCBjdXJyZW50VG9rZW5zICsgcmVjb3JkLnRvdGFsX3Rva2Vucyk7XG4gICAgICB9KTtcblxuICAgICAgLy8gQ29udmVydGlyIGEgYXJyYXkgb3JkZW5hZG9cbiAgICAgIGZvciAobGV0IGkgPSAyOTsgaSA+PSAwOyBpLS0pIHtcbiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKCk7XG4gICAgICAgIGRhdGUuc2V0RGF0ZShkYXRlLmdldERhdGUoKSAtIGkpO1xuICAgICAgICBjb25zdCBkYXRlU3RyID0gZGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG4gICAgICAgIGRhaWx5SGlzdG9yeS5wdXNoKHtcbiAgICAgICAgICBkYXRlOiBkYXRlU3RyLFxuICAgICAgICAgIHRva2VuczogZGFpbHlNYXAuZ2V0KGRhdGVTdHIpIHx8IDBcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHBlcmNlbnRhZ2U6IE1hdGgucm91bmQocGVyY2VudGFnZSksXG4gICAgICBsaW1pdDogcHJvZmlsZS5tb250aGx5X3Rva2VuX2xpbWl0LFxuICAgICAgdXNlZDogcHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucyxcbiAgICAgIHJlbWFpbmluZzogcHJvZmlsZS5tb250aGx5X3Rva2VuX2xpbWl0IC0gcHJvZmlsZS5jdXJyZW50X21vbnRoX3Rva2VucyxcbiAgICAgIGRhaWx5SGlzdG9yeVxuICAgIH07XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbiBnZXRUb2tlblVzYWdlUHJvZ3Jlc3M6JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogT2J0aWVuZSBoaXN0b3JpYWwgZGUgY29tcHJhcyBkZSB0b2tlbnMgZGVsIHVzdWFyaW9cbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFRva2VuUHVyY2hhc2VIaXN0b3J5KCk6IFByb21pc2U8QXJyYXk8e1xuICBpZDogc3RyaW5nO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgcHJpY2U6IG51bWJlcjtcbiAgY3JlYXRlZF9hdDogc3RyaW5nO1xuICBzdGF0dXM6IHN0cmluZztcbn0+IHwgbnVsbD4ge1xuICB0cnkge1xuICAgIGNvbnN0IHN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KCk7XG5cbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9LCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcblxuICAgIGlmICh1c2VyRXJyb3IgfHwgIXVzZXIpIHtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YTogcHVyY2hhc2VzLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCd0b2tlbl9wdXJjaGFzZXMnKVxuICAgICAgLnNlbGVjdCgnaWQsIGFtb3VudCwgcHJpY2UsIGNyZWF0ZWRfYXQsIHN0YXR1cycpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pO1xuXG4gICAgaWYgKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhbCBvYnRlbmVyIGhpc3RvcmlhbCBkZSBjb21wcmFzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIHJldHVybiBwdXJjaGFzZXMgfHwgW107XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbiBnZXRUb2tlblB1cmNoYXNlSGlzdG9yeTonLCBlcnJvcik7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJnZXRQbGFuQ29uZmlndXJhdGlvbiIsImhhc0ZlYXR1cmVBY2Nlc3MiLCJzYXZlVG9rZW5Vc2FnZSIsIl94IiwiX3NhdmVUb2tlblVzYWdlIiwiYXBwbHkiLCJhcmd1bWVudHMiLCJfYXN5bmNUb0dlbmVyYXRvciIsIl9yZWdlbmVyYXRvclJ1bnRpbWUiLCJtYXJrIiwiX2NhbGxlZSIsImRhdGEiLCJzdXBhYmFzZSIsIl95aWVsZCRzdXBhYmFzZSRhdXRoJCIsInVzZXIiLCJ1c2VyRXJyb3IiLCJhY2Nlc3NWYWxpZGF0aW9uIiwidXNhZ2VSZWNvcmQiLCJfeWllbGQkc3VwYWJhc2UkZnJvbSQiLCJlcnJvciIsIndyYXAiLCJfY2FsbGVlJCIsIl9jb250ZXh0IiwicHJldiIsIm5leHQiLCJjb25zb2xlIiwibG9nIiwiYXV0aCIsImdldFVzZXIiLCJzZW50IiwiY29uY2F0IiwiaWQiLCJlbWFpbCIsIndhcm4iLCJtZXNzYWdlIiwiYWJydXB0IiwidmFsaWRhdGVBY3Rpdml0eUFjY2VzcyIsImFjdGl2aXR5IiwidXNhZ2UiLCJ0b3RhbFRva2VucyIsImFsbG93ZWQiLCJyZWFzb24iLCJFcnJvciIsInVzZXJfaWQiLCJhY3Rpdml0eV90eXBlIiwibW9kZWxfbmFtZSIsIm1vZGVsIiwicHJvbXB0X3Rva2VucyIsInByb21wdFRva2VucyIsImNvbXBsZXRpb25fdG9rZW5zIiwiY29tcGxldGlvblRva2VucyIsInRvdGFsX3Rva2VucyIsImVzdGltYXRlZF9jb3N0IiwiZXN0aW1hdGVkQ29zdCIsInVzYWdlX21vbnRoIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic2xpY2UiLCJmcm9tIiwiaW5zZXJ0IiwidXBkYXRlTW9udGhseVRva2VuQ291bnQiLCJ0MCIsInN0b3AiLCJfeDIiLCJfeDMiLCJfdXBkYXRlTW9udGhseVRva2VuQ291bnQiLCJfY2FsbGVlMiIsInVzZXJJZCIsInRva2VucyIsImN1cnJlbnRNb250aCIsIl95aWVsZCRzdXBhYmFzZSRmcm9tJDIiLCJwcm9maWxlIiwicHJvZmlsZUVycm9yIiwiX3lpZWxkJHN1cGFiYXNlJGZyb20kMyIsImluc2VydEVycm9yIiwibmV3VG9rZW5Db3VudCIsIl95aWVsZCRzdXBhYmFzZSRmcm9tJDQiLCJ1cGRhdGVFcnJvciIsIl9jYWxsZWUyJCIsIl9jb250ZXh0MiIsInNlbGVjdCIsImVxIiwic2luZ2xlIiwiY29kZSIsInN1YnNjcmlwdGlvbl9wbGFuIiwibW9udGhseV90b2tlbl9saW1pdCIsImN1cnJlbnRfbW9udGhfdG9rZW5zIiwiY3VycmVudF9tb250aCIsInVwZGF0ZSIsInVwZGF0ZWRfYXQiLCJnZXRVc2VyVG9rZW5TdGF0cyIsIl9nZXRVc2VyVG9rZW5TdGF0cyIsIl9jYWxsZWUzIiwiX3lpZWxkJHN1cGFiYXNlJGF1dGgkMiIsIl95aWVsZCRzdXBhYmFzZSRmcm9tJDUiLCJyZWNvcmRzIiwiX2NhbGxlZTMkIiwiX2NvbnRleHQzIiwiZ2V0RW1wdHlTdGF0cyIsIm9yZGVyIiwiYXNjZW5kaW5nIiwiY2FsY3VsYXRlU3RhdHMiLCJzdGF0cyIsInRvdGFsU2Vzc2lvbnMiLCJsZW5ndGgiLCJ0b3RhbENvc3QiLCJieUFjdGl2aXR5IiwiYnlNb2RlbCIsImZvckVhY2giLCJyZWNvcmQiLCJjb3N0IiwiY291bnQiLCJnZXRVc2VyUHJvZmlsZSIsIl9nZXRVc2VyUHJvZmlsZSIsIl9jYWxsZWU0IiwiX3lpZWxkJHN1cGFiYXNlJGF1dGgkMyIsIl95aWVsZCRzdXBhYmFzZSRmcm9tJDYiLCJfY2FsbGVlNCQiLCJfY29udGV4dDQiLCJjaGVja1Rva2VuTGltaXQiLCJfY2hlY2tUb2tlbkxpbWl0IiwiX2NhbGxlZTUiLCJjdXJyZW50VG9rZW5zIiwibW9udGhseUxpbWl0IiwicGVyY2VudGFnZSIsImhhc1JlYWNoZWRMaW1pdCIsIl9jYWxsZWU1JCIsIl9jb250ZXh0NSIsImxpbWl0IiwiX3g0IiwiX3g1IiwiX3g2IiwiX3ZhbGlkYXRlQWN0aXZpdHlBY2Nlc3MiLCJfY2FsbGVlNiIsInRva2Vuc1RvVXNlIiwiX3lpZWxkJHN1cGFiYXNlJGZyb20kNyIsImFjdGl2aXR5VG9GZWF0dXJlIiwiZmVhdHVyZU5hbWUiLCJfY2FsbGVlNiQiLCJfY29udGV4dDYiLCJwYXltZW50X3ZlcmlmaWVkIiwiZ2V0VXNlclBsYW5JbmZvIiwiX2dldFVzZXJQbGFuSW5mbyIsIl9jYWxsZWU3IiwicGxhbkNvbmZpZyIsIl9jYWxsZWU3JCIsIl9jb250ZXh0NyIsInBsYW4iLCJwbGFuTmFtZSIsIm5hbWUiLCJmZWF0dXJlcyIsInRva2VuVXNhZ2UiLCJjdXJyZW50IiwiTWF0aCIsInJvdW5kIiwicmVtYWluaW5nIiwibWF4IiwicGF5bWVudFZlcmlmaWVkIiwiY2FuUGVyZm9ybUFjdGl2aXR5IiwiX3g3IiwiX2NhblBlcmZvcm1BY3Rpdml0eSIsIl9jYWxsZWU4IiwiZXN0aW1hdGVkVG9rZW5zIiwiX3lpZWxkJHN1cGFiYXNlJGF1dGgkNCIsInZhbGlkYXRpb24iLCJwbGFuSW5mbyIsIl9hcmdzOCIsIl9jYWxsZWU4JCIsIl9jb250ZXh0OCIsInVuZGVmaW5lZCIsImdldFRva2VuVXNhZ2VQcm9ncmVzcyIsIl9nZXRUb2tlblVzYWdlUHJvZ3Jlc3MiLCJfY2FsbGVlOSIsIl95aWVsZCRzdXBhYmFzZSRhdXRoJDUiLCJ0aGlydHlEYXlzQWdvIiwiX3lpZWxkJHN1cGFiYXNlJGZyb20kOCIsImRhaWx5VXNhZ2UiLCJoaXN0b3J5RXJyb3IiLCJkYWlseUhpc3RvcnkiLCJkYWlseU1hcCIsImkiLCJkYXRlIiwiZGF0ZVN0ciIsIl9jYWxsZWU5JCIsIl9jb250ZXh0OSIsInNldERhdGUiLCJnZXREYXRlIiwiZ3RlIiwiTWFwIiwiY3JlYXRlZF9hdCIsInNwbGl0IiwiZ2V0Iiwic2V0IiwicHVzaCIsInVzZWQiLCJnZXRUb2tlblB1cmNoYXNlSGlzdG9yeSIsIl9nZXRUb2tlblB1cmNoYXNlSGlzdG9yeSIsIl9jYWxsZWUxMCIsIl95aWVsZCRzdXBhYmFzZSRhdXRoJDYiLCJfeWllbGQkc3VwYWJhc2UkZnJvbSQ5IiwicHVyY2hhc2VzIiwiX2NhbGxlZTEwJCIsIl9jb250ZXh0MTAiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts\n"));

/***/ })

});