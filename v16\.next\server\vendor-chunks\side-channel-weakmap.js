"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel-weakmap";
exports.ids = ["vendor-chunks/side-channel-weakmap"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel-weakmap/index.js":
/*!****************************************************!*\
  !*** ./node_modules/side-channel-weakmap/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar GetIntrinsic = __webpack_require__(/*! get-intrinsic */ \"(rsc)/./node_modules/get-intrinsic/index.js\");\nvar callBound = __webpack_require__(/*! call-bound */ \"(rsc)/./node_modules/call-bound/index.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar $WeakMap = GetIntrinsic('%WeakMap%', true);\n\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => V} */\nvar $weakMapGet = callBound('WeakMap.prototype.get', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K, value: V) => void} */\nvar $weakMapSet = callBound('WeakMap.prototype.set', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapHas = callBound('WeakMap.prototype.has', true);\n/** @type {<K extends object, V>(thisArg: WeakMap<K, V>, key: K) => boolean} */\nvar $weakMapDelete = callBound('WeakMap.prototype.delete', true);\n\n/** @type {import('.')} */\nmodule.exports = $WeakMap ? /** @type {Exclude<import('.'), false>} */function getSideChannelWeakMap() {\n  /** @typedef {ReturnType<typeof getSideChannelWeakMap>} Channel */\n  /** @typedef {Parameters<Channel['get']>[0]} K */\n  /** @typedef {Parameters<Channel['set']>[1]} V */\n\n  /** @type {WeakMap<K & object, V> | undefined} */var $wm;\n  /** @type {Channel | undefined} */\n  var $m;\n\n  /** @type {Channel} */\n  var channel = {\n    assert: function (key) {\n      if (!channel.has(key)) {\n        throw new $TypeError('Side channel does not contain ' + inspect(key));\n      }\n    },\n    'delete': function (key) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if ($wm) {\n          return $weakMapDelete($wm, key);\n        }\n      } else if (getSideChannelMap) {\n        if ($m) {\n          return $m['delete'](key);\n        }\n      }\n      return false;\n    },\n    get: function (key) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if ($wm) {\n          return $weakMapGet($wm, key);\n        }\n      }\n      return $m && $m.get(key);\n    },\n    has: function (key) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if ($wm) {\n          return $weakMapHas($wm, key);\n        }\n      }\n      return !!$m && $m.has(key);\n    },\n    set: function (key, value) {\n      if ($WeakMap && key && (typeof key === 'object' || typeof key === 'function')) {\n        if (!$wm) {\n          $wm = new $WeakMap();\n        }\n        $weakMapSet($wm, key, value);\n      } else if (getSideChannelMap) {\n        if (!$m) {\n          $m = getSideChannelMap();\n        }\n        // eslint-disable-next-line no-extra-parens\n        /** @type {NonNullable<typeof $m>} */\n        $m.set(key, value);\n      }\n    }\n  };\n\n  // @ts-expect-error TODO: figure out why this is erroring\n  return channel;\n} : getSideChannelMap;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel-weakmap/index.js\n");

/***/ })

};
;