"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TokenStatsModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenStatsModal)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiArrowUp,FiBarChart,FiLock,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase_tokenUsageService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/tokenUsageService */ \"(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts\");\n/* harmony import */ var _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePlanLimits */ \"(app-pages-browser)/./src/hooks/usePlanLimits.ts\");\n/* harmony import */ var _TokenProgressBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TokenProgressBar */ \"(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\");\n/* harmony import */ var _TokenPurchaseButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TokenPurchaseButton */ \"(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenStatsModal.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TokenStatsModal(_ref) {\n    _s();\n    _s1();\n    var _this = this;\n    var isOpen = _ref.isOpen, onClose = _ref.onClose, _ref$shouldRefreshOnO = _ref.shouldRefreshOnOpen, shouldRefreshOnOpen = _ref$shouldRefreshOnO === void 0 ? false : _ref$shouldRefreshOnO;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), stats = _useState[0], setStats = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), loading = _useState2[0], setLoading = _useState2[1];\n    var planLimits = (0,_hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits)();\n    // Refrescar datos cuando se abre el modal si es necesario\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && shouldRefreshOnOpen) {\n                planLimits.refresh();\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        shouldRefreshOnOpen,\n        planLimits\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && planLimits.userPlan && planLimits.userPlan !== 'free') {\n                loadStats();\n            } else if (isOpen && planLimits.userPlan === 'free') {\n                setStats(null);\n                setLoading(false);\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        planLimits.userPlan\n    ]);\n    var loadStats = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {\n            var currentStats;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        setLoading(true);\n                        _context.next = 4;\n                        return (0,_lib_supabase_tokenUsageService__WEBPACK_IMPORTED_MODULE_4__.getUserTokenStats)();\n                    case 4:\n                        currentStats = _context.sent;\n                        setStats(currentStats);\n                        _context.next = 11;\n                        break;\n                    case 8:\n                        _context.prev = 8;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error al cargar estadísticas:', _context.t0);\n                    case 11:\n                        _context.prev = 11;\n                        setLoading(false);\n                        return _context.finish(11);\n                    case 14:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    8,\n                    11,\n                    14\n                ]\n            ]);\n        }));\n        return function loadStats() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    if (!isOpen) return null;\n    var formatTokens = function formatTokens(tokens) {\n        var validTokens = tokens || 0;\n        return validTokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Estad\\xEDsticas de Uso de IA\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiX, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: planLimits.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 78,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Verificando plan...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this) : planLimits.userPlan === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiLock, {\n                                className: \"mx-auto h-16 w-16 text-gray-400 mb-6\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Estad\\xEDsticas de Tokens no disponibles\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                            children: \"Plan Gratuito:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 89,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Las estad\\xEDsticas avanzadas y la compra de tokens est\\xE1n disponibles solo para usuarios con planes de pago.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 88,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 87,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Actualiza tu plan para acceder a:\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-700 space-y-2 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                                        className: \"w-4 h-4 text-blue-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 98,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Estad\\xEDsticas detalladas de uso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity, {\n                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"An\\xE1lisis por actividad y modelo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiArrowUp, {\n                                                        className: \"w-4 h-4 text-purple-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 106,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seguimiento de progreso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiArrowUp, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ver Planes Disponibles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 111,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 82,\n                        columnNumber: 13\n                    }, this) : loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Cargando estad\\xEDsticas...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 124,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 122,\n                        columnNumber: 13\n                    }, this) : stats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                        children: [\n                            planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_TokenProgressBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    used: planLimits.tokenUsage.current || 0,\n                                    limit: planLimits.tokenUsage.limit || 0,\n                                    percentage: planLimits.tokenUsage.percentage || 0,\n                                    remaining: planLimits.tokenUsage.remaining || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 131,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 130,\n                                columnNumber: 17\n                            }, this),\n                            planLimits.tokenUsage && (planLimits.tokenUsage.percentage || 0) >= 80 && planLimits.userPlan && planLimits.userPlan !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_TokenPurchaseButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    userPlan: planLimits.userPlan,\n                                    currentTokens: planLimits.tokenUsage.current || 0,\n                                    tokenLimit: planLimits.tokenUsage.limit || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 143,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 142,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity, {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-blue-900\",\n                                                        children: \"Total Sesiones\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 156,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900 mt-1\",\n                                                children: stats.totalSessions\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 158,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 153,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-900\",\n                                                        children: \"Tokens Consumidos (Hist\\xF3rico)\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900 mt-1\",\n                                                children: formatTokens(stats.totalTokens)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 152,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Actividad\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Actividad\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 182,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 183,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 180,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byActivity).map(function(_ref3) {\n                                                        var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, 2), activity = _ref4[0], data = _ref4[1];\n                                                        var activityData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                    children: activity\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: activityData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(activityData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, activity, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Modelo\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Modelo\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 210,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 211,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 208,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byModel).map(function(_ref5) {\n                                                        var _ref6 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref5, 2), model = _ref6[0], data = _ref6[1];\n                                                        var modelData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900 font-mono\",\n                                                                    children: model\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: modelData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 220,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(modelData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 221,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, model, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                            children: \"Nota:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 233,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Los datos de uso de tokens se almacenan en Supabase. Los tokens de entrada y salida se registran autom\\xE1ticamente para cada actividad de IA.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 232,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No hay datos de uso disponibles.\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 240,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenStatsModal, \"WYDFYwW2tqIbkxDwNiPAn5eB8Ws=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits\n    ];\n});\n_c1 = TokenStatsModal;\n_s1(TokenStatsModal, \"1F00vAQqTrEahcnC0EvNidQjiF0=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits\n    ];\n});\n_c = TokenStatsModal;\nvar _c;\n$RefreshReg$(_c, \"TokenStatsModal\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenStatsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\n"));

/***/ })

});