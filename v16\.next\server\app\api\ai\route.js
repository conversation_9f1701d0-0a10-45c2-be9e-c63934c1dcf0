"use strict";(()=>{var e={};e.id=3700,e.ids=[3700],e.modules={1506:(e,t,r)=>{r.r(t),r.d(t,{generarMapaMental:()=>c});var n=r(29981),a=r(86007),i=r(89459),s=r(55292);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function c(e,t){try{if(!e||0===e.length)throw Error("No se han proporcionado documentos para generar el mapa mental.");let r=(0,n.Jo)(e);if(!r||0===r.trim().length)throw Error("El contenido de los documentos est\xe1 vac\xedo o no es v\xe1lido.");let o=t?.trim()||"Crea un mapa mental que organice los conceptos principales del contenido.",c=a.Q_.replace("{documentos}",r);c=c.replace("{instrucciones}",o);let u=(0,s.Vj)("MAPAS_MENTALES");console.log(`🗺️ Generando mapa mental con modelo: ${u.model} (max_tokens: ${u.max_tokens})`);let d=[{role:"user",content:c}],p=await (0,i.y5)(d,l(l({},u),{},{activityName:"Generaci\xf3n de Mapa Mental"}));if(!p||0===p.trim().length)throw Error("La IA no gener\xf3 ning\xfan contenido para el mapa mental.");let f=p.trim(),h=f.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);return h&&(f=h[0]),f=f.replace(/```html/gi,"").replace(/```/g,"").trim(),console.log("Contenido generado por la IA (primeros 500 caracteres):",f.substring(0,500)),console.log("Longitud total del contenido:",f.length),f.includes("<!DOCTYPE html>")&&f.includes("</html>")?console.log("✅ HTML v\xe1lido detectado"):(console.warn("⚠️ El contenido generado no parece ser HTML v\xe1lido"),console.log("Contenido completo:",f)),f}catch(e){if(console.error("Error al generar mapa mental:",e),e instanceof Error)throw Error(`Error al generar el mapa mental: ${e.message}`);throw Error("Ha ocurrido un error inesperado al generar el mapa mental.")}}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11345:(e,t,r)=>{r.d(t,{createServerSupabaseClient:()=>o});var n=r(83760),a=r(33465);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function o(){let e=await (0,a.UL)();return(0,n.createServerClient)("https://fxnhpxjijinfuxxxplzj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4",{auth:{persistSession:!1,autoRefreshToken:!1,detectSessionInUrl:!1},cookies:{getAll:()=>e.getAll(),setAll(t){try{t.filter(e=>!e.name.includes("auth-token")&&!e.name.includes("refresh-token")).forEach(({name:t,value:r,options:n})=>e.set(t,r,s(s({},n),{},{maxAge:void 0,expires:void 0})))}catch{}}}})}},11997:e=>{e.exports=require("punycode")},22164:(e,t,r)=>{r.r(t),r.d(t,{generarTest:()=>c});var n=r(29981),a=r(86007),i=r(89459),s=r(55292);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function c(e,t=10,r){try{let o=(0,n.Jo)(e);if(!o)throw Error("No se han proporcionado documentos para generar el test.");let c=a.fD.replace("{documentos}",o).replace("{cantidad}",t.toString());c=r?c.replace("{instrucciones}",`Instrucciones adicionales:
- ${r}`):c.replace("{instrucciones}","");let u=(0,s.Vj)("TESTS");console.log(`🧪 Generando test con modelo: ${u.model} (max_tokens: ${u.max_tokens})`);let d=[{role:"user",content:c}],p=await (0,i.y5)(d,l(l({},u),{},{activityName:`Generaci\xf3n de Test (${t||"N/A"} preguntas)`})),f=p.match(/\[\s*\{[\s\S]*\}\s*\]/);if(!f)throw console.log("❌ No se encontr\xf3 JSON en la respuesta. Respuesta recibida:",p.substring(0,500)),Error("No se pudo extraer el formato JSON de la respuesta.");let h=f[0].replace(/"opcion([abcd])"/g,'"opcion_$1"').replace(/"opciona"/g,'"opcion_a"').replace(/"opcionb"/g,'"opcion_b"').replace(/"opcionc"/g,'"opcion_c"').replace(/"opciond"/g,'"opcion_d"'),m=JSON.parse(h);if(console.log(`📊 Preguntas generadas: ${m.length} de ${t} solicitadas`),!Array.isArray(m)||0===m.length)throw Error("El formato de las preguntas generadas no es v\xe1lido.");return m.length!==t&&console.log(`⚠️ Advertencia: Se generaron ${m.length} preguntas en lugar de ${t}`),m.forEach((e,t)=>{if(!e.pregunta||!e.opcion_a||!e.opcion_b||!e.opcion_c||!e.opcion_d||!e.respuesta_correcta)throw Error(`La pregunta ${t+1} no tiene el formato correcto.`);if(!["a","b","c","d"].includes(e.respuesta_correcta))throw Error(`La respuesta correcta de la pregunta ${t+1} no es v\xe1lida.`)}),m}catch(e){throw console.error("Error al generar test:",e),e}}},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29981:(e,t,r)=>{r.d(t,{Jo:()=>n});let n=r(89459).Jo},34631:e=>{e.exports=require("tls")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55292:(e,t,r)=>{function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}r.d(t,{Vj:()=>i});let a={MODELS:{PLAN_ESTUDIOS:"o3-2025-04-16",CONVERSACIONES:"o3-mini-2025-01-31",FLASHCARDS:"o3-2025-04-16",TESTS:"o3-2025-04-16",MAPAS_MENTALES:"o3-mini-2025-01-31",RESUMENES:"o3-mini-2025-01-31"},TASK_CONFIGS:{PLAN_ESTUDIOS:{temperature:.3,max_tokens:1e5},CONVERSACIONES:{temperature:.7,max_tokens:2e4},FLASHCARDS:{temperature:.6,max_tokens:1e5},TESTS:{temperature:.4,max_tokens:1e5},MAPAS_MENTALES:{temperature:.8,max_tokens:1e5},RESUMENES:{temperature:.5,max_tokens:1e5}}};function i(e){return function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({model:a.MODELS[e]},a.TASK_CONFIGS[e])}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},78305:(e,t,r)=>{r.r(t),r.d(t,{generarResumen:()=>c});var n=r(29981),a=r(86007),i=r(89459),s=r(55292);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function c(e,t){try{if(!e||!e.contenido)throw Error("No se ha proporcionado un documento v\xe1lido para generar el resumen.");if(0===e.contenido.trim().length)throw Error("El contenido del documento est\xe1 vac\xedo.");let r=[e],o=(0,n.Jo)(r);if(!o)throw Error("No se pudo preparar el contenido del documento.");let c=t?.trim()||"Crea un resumen completo y estructurado del tema proporcionado.",u=a.RT.replace("{titulo_del_tema}",e.titulo||"Tema sin t\xedtulo");u=(u=u.replace("{documento}",o)).replace("{instrucciones}",c);let d=(0,s.Vj)("RESUMENES");console.log(`📄 Generando resumen con modelo: ${d.model} (max_tokens: ${d.max_tokens})`);let p=[{role:"user",content:u}],f=await (0,i.y5)(p,l(l({},d),{},{activityName:"Generaci\xf3n de Resumen"}));if(!f||0===f.trim().length)throw Error("La IA no gener\xf3 ning\xfan contenido para el resumen.");let h=f.trim();if(h.length<100)throw Error("El resumen generado es demasiado corto. Por favor, int\xe9ntalo de nuevo.");return console.log("✅ Resumen generado exitosamente"),h}catch(e){if(console.error("Error al generar resumen:",e),e instanceof Error)throw Error(`Error al generar el resumen: ${e.message}`);throw Error("Ha ocurrido un error inesperado al generar el resumen.")}}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},84124:(e,t,r)=>{let n;r.r(t),r.d(t,{patchFetch:()=>tx,routeModule:()=>ty,serverHooks:()=>tO,workAsyncStorage:()=>tb,workUnitAsyncStorage:()=>tv});var a,i,s,o,l,c,u={};r.r(u),r.d(u,{POST:()=>tg});var d=r(12693),p=r(79378),f=r(26833),h=r(32644),m=r(11345),g=r(29981),y=r(86007),b=r(89459),v=r(55292);function O(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function w(e,t){try{if(!e||"string"!=typeof e||""===e.trim())return console.warn("Se recibi\xf3 una pregunta vac\xeda o inv\xe1lida"),"Por favor, proporciona una pregunta v\xe1lida.";if(!t||!Array.isArray(t)||0===t.length)return console.warn("No se proporcionaron documentos v\xe1lidos para obtenerRespuestaIA"),"No se han proporcionado documentos para responder a esta pregunta.";let r=(0,g.Jo)(t);if(!r)return console.warn("No se pudo preparar el contenido de los documentos"),"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan informaci\xf3n v\xe1lida.";let n=y.zM.replace("{documentos}",r).replace("{pregunta}",e),a=(0,v.Vj)("CONVERSACIONES");return console.log(`💬 Generando respuesta con modelo: ${a.model} (max_tokens: ${a.max_tokens})`),await (0,b.y5)([{role:"user",content:n}],x(x({},a),{},{activityName:"Conversaci\xf3n/Q&A"}))}catch(e){if(console.error("Error al obtener respuesta de la IA:",e),e instanceof Error)return`Lo siento, ha ocurrido un error al procesar tu pregunta: ${e.message}. Por favor, int\xe9ntalo de nuevo m\xe1s tarde.`;return"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, int\xe9ntalo de nuevo m\xe1s tarde."}}async function _(e,t,n=10){let a=t.map((e,t)=>({titulo:`Documento ${t+1}`,contenido:e}));return await Promise.resolve().then(r.bind(r,93128)).then(t=>t.generarFlashcards(a,n,e))}async function j(e,t){let n=t.map((e,t)=>({titulo:`Documento ${t+1}`,contenido:e}));return await Promise.resolve().then(r.bind(r,1506)).then(t=>t.generarMapaMental(n,e))}async function P(e,t,n=10){let a=t.map((e,t)=>({titulo:`Documento ${t+1}`,contenido:e}));return(await Promise.resolve().then(r.bind(r,22164)).then(t=>t.generarTest(a,n,e))).map(e=>({pregunta:e.pregunta,opciones:{a:e.opcion_a,b:e.opcion_b,c:e.opcion_c,d:e.opcion_d},respuesta_correcta:e.respuesta_correcta}))}async function S(e,t){return await Promise.resolve().then(r.bind(r,78305)).then(r=>r.generarResumen(e,t))}function E(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function A(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?E(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function N(e,t){try{let{createServerSupabaseClient:n}=await Promise.resolve().then(r.bind(r,11345)),a=await n();console.log("\uD83D\uDD0D Consultando planificaci\xf3n con:",{userId:t,temarioId:e});let{data:i,error:s}=await a.from("planificacion_usuario").select("*").eq("user_id",t).eq("temario_id",e).single();if(console.log("\uD83D\uDD0D Resultado consulta planificaci\xf3n:",{data:i?"ENCONTRADA":"NO ENCONTRADA",error:s?.code,errorMessage:s?.message}),s){if("PGRST116"===s.code)return console.log("❌ No hay planificaci\xf3n configurada (PGRST116)"),null;return console.error("❌ Error al obtener planificaci\xf3n:",s),null}return console.log("✅ Planificaci\xf3n encontrada:",i.id),i}catch(e){return console.error("❌ Error al obtener planificaci\xf3n:",e),null}}async function T(e){try{let{createServerSupabaseClient:t}=await Promise.resolve().then(r.bind(r,11345)),n=await t(),{data:a,error:i}=await n.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});if(i)return console.error("Error al obtener temas:",i),[];return a||[]}catch(e){return console.error("Error al obtener temas:",e),[]}}async function I(e,t){try{let n=t;if(!n){let{createServerSupabaseClient:e}=await Promise.resolve().then(r.bind(r,11345)),t=await e(),{data:{user:a},error:i}=await t.auth.getUser();if(!a||i)throw Error("No hay usuario autenticado para generar el plan de estudios");n=a}let{createServerSupabaseClient:a}=await Promise.resolve().then(r.bind(r,11345)),i=await a(),{data:s,error:o}=await i.from("temarios").select("id, titulo").eq("id",e).eq("user_id",n.id).single();if(o||!s)throw Error(`El temario no existe o ha sido eliminado: ${e}`);let l=await N(e,n.id);if(!l)throw Error(`No se encontr\xf3 planificaci\xf3n configurada para el temario: ${s.titulo}`);let c=await T(e),u=c.map(e=>({id:e.id,numero:e.numero,titulo:e.titulo,descripcion:e.descripcion,horasEstimadas:0,esDificil:!1,esMuyImportante:!1,yaDominado:!1,notas:""})),d=function(e,t){let r="",n=new Date,a=n.toISOString().split("T")[0],i=26,s="";if(e.fecha_examen)i=Math.max(1,Math.ceil(Math.ceil((new Date(e.fecha_examen).getTime()-n.getTime())/864e5)/7)),s=e.fecha_examen;else if(e.fecha_examen_aproximada){let t=function(e,t){let r=new Date(t);switch(e){case"1-3_meses":r.setMonth(r.getMonth()+2);break;case"3-6_meses":r.setMonth(r.getMonth()+4);break;case"6-12_meses":r.setMonth(r.getMonth()+9);break;case"mas_12_meses":r.setMonth(r.getMonth()+18);break;case"primavera_2025":r.setFullYear(2025,3,15);break;case"verano_2025":r.setFullYear(2025,6,15);break;case"otono_2025":r.setFullYear(2025,9,15);break;case"invierno_2025":r.setFullYear(2025,11,15);break;case"primavera_2026":r.setFullYear(2026,3,15);break;case"verano_2026":r.setFullYear(2026,6,15);break;default:let n=new Date(e);if(!isNaN(n.getTime()))return n;return console.warn(`Fecha aproximada no reconocida: ${e}`),null}return r}(e.fecha_examen_aproximada,n);t&&(i=Math.max(1,Math.ceil(Math.ceil((t.getTime()-n.getTime())/864e5)/7)),s=t.toISOString().split("T")[0])}return(r+=`**FECHA ACTUAL:** ${a}
**DURACI\xd3N DEL PLAN:** ${i} semanas (desde ${a} hasta ${s||"fecha estimada"})
**CR\xcdTICO:** Debes generar EXACTAMENTE ${i} semanas en el array "semanas". No menos.
**IMPORTANTE:** Todas las fechas del plan deben ser calculadas a partir de la fecha actual (${a}).
- La Semana 1 debe comenzar el ${a}
- Cada semana siguiente debe calcularse sumando 7 d\xedas a la anterior
- La \xfaltima semana (Semana ${i}) debe terminar cerca de la fecha del examen
- Usa el formato YYYY-MM-DD para todas las fechas

`,e.tiempo_por_dia&&Object.keys(e.tiempo_por_dia).length>0)?(r+="**Disponibilidad de Tiempo Diario:**\n",["lunes","martes","miercoles","jueves","viernes","sabado","domingo"].forEach(t=>{let n=e.tiempo_por_dia[t];n&&(r+=`- ${t.charAt(0).toUpperCase()+t.slice(1)}: ${n}h
`)})):e.tiempo_diario_promedio&&(r+=`**Disponibilidad de Tiempo Diario:** Promedio ${e.tiempo_diario_promedio}h/d\xeda
`),e.fecha_examen?r+=`
**Fecha del Examen:** ${e.fecha_examen}
`:e.fecha_examen_aproximada&&(r+=`
**Fecha del Examen (aproximada):** ${e.fecha_examen_aproximada}
`),e.familiaridad_general&&(r+=`
**Familiaridad General con el Temario (1-5):** ${e.familiaridad_general}
`),e.preferencias_horario&&e.preferencias_horario.length>0&&(r+=`
**Preferencias de Horario:** ${e.preferencias_horario.join(", ")}
`),e.frecuencia_repasos&&(r+=`
**Frecuencia de Repasos Deseada:** ${e.frecuencia_repasos}
`),r+="\n**\xcdndice del Temario del Opositor:**\n**IMPORTANTE:** Como experto preparador, debes analizar autom\xe1ticamente cada tema y determinar:\n- Horas de estudio necesarias seg\xfan la complejidad y extensi\xf3n aparente\n- Nivel de dificultad basado en el t\xedtulo y descripci\xf3n\n- Importancia relativa dentro del conjunto del temario\n- Orden de estudio m\xe1s eficiente\n\n",t.forEach(e=>{r+=`- **Tema ${e.numero}: ${e.titulo}**
`,e.descripcion&&(r+=`  - Descripci\xf3n: ${e.descripcion}
`),r+="\n"}),r}(l,u),p=y.y1.replace("{informacionUsuario}",d),f=await (0,b.y5)([{role:"system",content:"Eres un experto preparador de oposiciones. Tu tarea es crear planes de estudio detallados y personalizados en formato JSON v\xe1lido."},{role:"user",content:p}],A(A({},(0,v.Vj)("PLAN_ESTUDIOS")),{},{activityName:"Generaci\xf3n de Plan de Estudios"}));if(!f||0===f.trim().length)throw Error("La IA no gener\xf3 ning\xfan contenido para el plan de estudios");try{console.log("\uD83D\uDD0D Respuesta completa de IA (primeros 1000 caracteres):",f.substring(0,1e3));let t=f.trim();if(t.includes("```json")){let e=t.match(/```json\s*([\s\S]*?)\s*```/);e&&(t=e[1].trim())}else if(t.includes("```")){let e=t.match(/```\s*([\s\S]*?)\s*```/);e&&(t=e[1].trim())}let a=t.indexOf("{"),i=t.lastIndexOf("}");-1!==a&&-1!==i&&i>a&&(t=t.substring(a,i+1)),t=t.replace(/[\u0000-\u001F\u007F-\u009F]/g,"").replace(/\n\s*\n/g,"\n").trim(),console.log("\uD83D\uDD0D JSON limpio (primeros 500 caracteres):",t.substring(0,500)),console.log("\uD83D\uDD0D JSON limpio (\xfaltimos 500 caracteres):",t.substring(Math.max(0,t.length-500))),t=function(e){try{return JSON.parse(e),e}catch(l){console.log("\uD83D\uDD27 Intentando reparar JSON truncado...");let t=e,r=((t=(t=(t=(t=(t=(t=t.replace(/\/\/.*$/gm,"")).replace(/\/\*[\s\S]*?\*\//g,"")).replace(/\s*\/\/\s*\(.*?\)\s*/g,"")).replace(/\s*\/\/\s*\.\.\.\s*/g,"")).replace(/\n\s*\n/g,"\n")).trim()).match(/\{/g)||[]).length,n=(t.match(/\}/g)||[]).length,a=(t.match(/\[/g)||[]).length,i=(t.match(/\]/g)||[]).length;console.log(`🔧 Llaves: ${r} abiertas, ${n} cerradas`),console.log(`🔧 Corchetes: ${a} abiertos, ${i} cerrados`),t.endsWith('"')&&(t=t.slice(0,-1)),t=t.replace(/,\s*$/,"");let s=a-i;for(let e=0;e<s;e++)t+="]";let o=r-n;for(let e=0;e<o;e++)t+="}";console.log("\uD83D\uDD27 JSON reparado (\xfaltimos 200 caracteres):",t.substring(Math.max(0,t.length-200)));try{return JSON.parse(t),console.log("✅ JSON reparado exitosamente"),t}catch(e){throw console.log("❌ No se pudo reparar el JSON, usando fallback"),e}}}(t);let s=JSON.parse(t);console.log("✅ Plan parseado exitosamente. N\xfamero de semanas:",s.semanas?.length||0);let{guardarPlanEstudiosServidor:o}=await r.e(9844).then(r.bind(r,19844)),l=await o(e,s,n,`Plan de Estudios - ${new Date().toLocaleDateString()}`);return l?console.log("✅ Plan de estudios guardado con ID:",l):console.warn("⚠️ No se pudo guardar el plan en la base de datos"),s}catch(o){console.error("❌ Error al parsear JSON del plan:",o),console.error("\uD83D\uDCC4 Respuesta completa de IA:",f);let t="Plan de estudios generado por IA";if(f&&f.length>100){let e=f.split("\n").filter(e=>e.trim().length>0);e.length>0&&(t=e[0].substring(0,200)+"...")}let a={introduccion:`${t}

NOTA: Hubo un problema al procesar la respuesta completa de la IA. Este es un plan b\xe1sico de respaldo.`,resumen:{tiempoTotalEstudio:"Por determinar",numeroTemas:c.length,duracionEstudioNuevo:"Por determinar",duracionRepasoFinal:"Por determinar"},semanas:[{numero:1,fechaInicio:new Date().toISOString().split("T")[0],fechaFin:new Date(Date.now()+5184e5).toISOString().split("T")[0],objetivoPrincipal:"Comenzar el estudio del temario",dias:[{dia:"Lunes",horas:4,tareas:[{titulo:"Revisar plan detallado",descripcion:"Por favor, regenera el plan para obtener una versi\xf3n completa.",tipo:"estudio",duracionEstimada:"4h"}]}]}],estrategiaRepasos:"Regenera el plan para obtener la estrategia de repasos completa",proximosPasos:"Regenera el plan para obtener los pr\xf3ximos pasos detallados"},{guardarPlanEstudiosServidor:i}=await r.e(9844).then(r.bind(r,19844)),s=await i(e,a,n,`Plan de Estudios (Fallback) - ${new Date().toLocaleDateString()}`);return s&&console.log("✅ Plan fallback guardado con ID:",s),a}}catch(e){throw console.error("Error al generar plan de estudios:",e),e}}async function D(e){try{if(!e||0===e.trim().length)throw Error("No se ha proporcionado contenido v\xe1lido para editar.");if(e.trim().length<100)throw Error("El contenido del resumen es demasiado corto para ser editado.");console.log("\uD83D\uDCDD Iniciando edici\xf3n de resumen..."),console.log(`📄 Longitud del contenido original: ${e.length} caracteres`);let t=y.HV.replace("{texto_largo_del_paso_1}",e),r=(0,v.Vj)("RESUMENES");console.log(`📄 Editando resumen con modelo: ${r.model} (max_tokens: ${r.max_tokens})`);let n=await (0,b.y5)([{role:"user",content:t}],{model:r.model,max_tokens:r.max_tokens,temperature:r.temperature,activityName:"Edici\xf3n de Resumen"});if(!n||0===n.trim().length)throw Error("La IA no pudo generar una edici\xf3n v\xe1lida del resumen.");return console.log("✅ Resumen editado exitosamente"),console.log(`📄 Longitud del contenido editado: ${n.length} caracteres`),n.trim()}catch(e){throw console.error("❌ Error al editar resumen:",e),e}}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function C(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r(93128),r(22164),r(1506),r(78305),!function(e){e.assertEqual=e=>{},e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let t={};for(let r of e)t[r]=r;return t},e.getValidEnumValues=t=>{let r=e.objectKeys(t).filter(e=>"number"!=typeof t[t[e]]),n={};for(let e of r)n[e]=t[e];return e.objectValues(n)},e.objectValues=t=>e.objectKeys(t).map(function(e){return t[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let t=[];for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return t},e.find=(e,t)=>{for(let r of e)if(t(r))return r},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(a||(a={})),(i||(i={})).mergeShapes=(e,t)=>C(C({},e),t);let R=a.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),$=e=>{switch(typeof e){case"undefined":return R.undefined;case"string":return R.string;case"number":return Number.isNaN(e)?R.nan:R.number;case"boolean":return R.boolean;case"function":return R.function;case"bigint":return R.bigint;case"symbol":return R.symbol;case"object":if(Array.isArray(e))return R.array;if(null===e)return R.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return R.promise;if("undefined"!=typeof Map&&e instanceof Map)return R.map;if("undefined"!=typeof Set&&e instanceof Set)return R.set;if("undefined"!=typeof Date&&e instanceof Date)return R.date;return R.object;default:return R.unknown}},L=a.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class M extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(e){return e.message},r={_errors:[]},n=e=>{for(let a of e.issues)if("invalid_union"===a.code)a.unionErrors.map(n);else if("invalid_return_type"===a.code)n(a.returnTypeError);else if("invalid_arguments"===a.code)n(a.argumentsError);else if(0===a.path.length)r._errors.push(t(a));else{let e=r,n=0;for(;n<a.path.length;){let r=a.path[n];n===a.path.length-1?(e[r]=e[r]||{_errors:[]},e[r]._errors.push(t(a))):e[r]=e[r]||{_errors:[]},e=e[r],n++}}};return n(this),r}static assert(e){if(!(e instanceof M))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,a.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let t={},r=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):r.push(e(n));return{formErrors:r,fieldErrors:t}}get formErrors(){return this.flatten()}}M.create=e=>new M(e);let U=(e,t)=>{let r;switch(e.code){case L.invalid_type:r=e.received===R.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case L.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(e.expected,a.jsonStringifyReplacer)}`;break;case L.unrecognized_keys:r=`Unrecognized key(s) in object: ${a.joinValues(e.keys,", ")}`;break;case L.invalid_union:r="Invalid input";break;case L.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${a.joinValues(e.options)}`;break;case L.invalid_enum_value:r=`Invalid enum value. Expected ${a.joinValues(e.options)}, received '${e.received}'`;break;case L.invalid_arguments:r="Invalid function arguments";break;case L.invalid_return_type:r="Invalid function return type";break;case L.invalid_date:r="Invalid date";break;case L.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(r=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(r=`${r} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?r=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?r=`Invalid input: must end with "${e.validation.endsWith}"`:a.assertNever(e.validation):r="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case L.too_small:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case L.too_big:r="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case L.custom:r="Invalid input";break;case L.invalid_intersection_types:r="Intersection results could not be merged";break;case L.not_multiple_of:r=`Number must be a multiple of ${e.multipleOf}`;break;case L.not_finite:r="Number must be finite";break;default:r=t.defaultError,a.assertNever(e)}return{message:r}};function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let z=e=>{let{data:t,path:r,errorMaps:n,issueData:a}=e,i=[...r,...a.path||[]],s=q(q({},a),{},{path:i});if(void 0!==a.message)return q(q({},a),{},{path:i,message:a.message});let o="";for(let e of n.filter(e=>!!e).slice().reverse())o=e(s,{data:t,defaultError:o}).message;return q(q({},a),{},{path:i,message:o})};function Z(e,t){let r=z({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,U,U==U?void 0:U].filter(e=>!!e)});e.common.issues.push(r)}class B{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){let r=[];for(let n of t){if("aborted"===n.status)return W;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){let r=[];for(let e of t){let t=await e.key,n=await e.value;r.push({key:t,value:n})}return B.mergeObjectSync(e,r)}static mergeObjectSync(e,t){let r={};for(let n of t){let{key:t,value:a}=n;if("aborted"===t.status||"aborted"===a.status)return W;"dirty"===t.status&&e.dirty(),"dirty"===a.status&&e.dirty(),"__proto__"!==t.value&&(void 0!==a.value||n.alwaysSet)&&(r[t.value]=a.value)}return{status:e.value,value:r}}}let W=Object.freeze({status:"aborted"}),V=e=>({status:"dirty",value:e}),J=e=>({status:"valid",value:e}),X=e=>"aborted"===e.status,H=e=>"dirty"===e.status,G=e=>"valid"===e.status,Y=e=>"undefined"!=typeof Promise&&e instanceof Promise;function K(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Q(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?K(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):K(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}!function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:e?.message}(s||(s={}));var ee=function(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)},et=function(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r};class er{constructor(e,t,r,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=r,this._key=n}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let en=(e,t)=>{if(G(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new M(e.common.issues);return this._error=t,this._error}}};function ea(e){if(!e)return{};let{errorMap:t,invalid_type_error:r,required_error:n,description:a}=e;if(t&&(r||n))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return t?{errorMap:t,description:a}:{errorMap:(t,a)=>{let{message:i}=e;return"invalid_enum_value"===t.code?{message:i??a.defaultError}:void 0===a.data?{message:i??n??a.defaultError}:"invalid_type"!==t.code?{message:a.defaultError}:{message:i??r??a.defaultError}},description:a}}class ei{get description(){return this._def.description}_getType(e){return $(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:$(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new B,ctx:{common:e.parent.common,data:e.data,parsedType:$(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(Y(t))throw Error("Synchronous parse encountered promise.");return t}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,t){let r=this.safeParse(e,t);if(r.success)return r.data;throw r.error}safeParse(e,t){let r={common:{issues:[],async:t?.async??!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:$(e)},n=this._parseSync({data:e,path:r.path,parent:r});return en(r,n)}"~validate"(e){let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:$(e)};if(!this["~standard"].async)try{let r=this._parseSync({data:e,path:[],parent:t});return G(r)?{value:r.value}:{issues:t.common.issues}}catch(e){e?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>G(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,t){let r=await this.safeParseAsync(e,t);if(r.success)return r.data;throw r.error}async safeParseAsync(e,t){let r={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:$(e)},n=this._parse({data:e,path:r.path,parent:r});return en(r,await (Y(n)?n:Promise.resolve(n)))}refine(e,t){let r=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement((t,n)=>{let a=e(t),i=()=>n.addIssue(Q({code:L.custom},r(t)));return"undefined"!=typeof Promise&&a instanceof Promise?a.then(e=>!!e||(i(),!1)):!!a||(i(),!1)})}refinement(e,t){return this._refinement((r,n)=>!!e(r)||(n.addIssue("function"==typeof t?t(r,n):t),!1))}_refinement(e){return new eK({schema:this,typeName:c.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return eQ.create(this,this._def)}nullable(){return e0.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return eR.create(this)}promise(){return eY.create(this,this._def)}or(e){return eL.create([this,e],this._def)}and(e){return eF.create(this,e,this._def)}transform(e){return new eK(Q(Q({},ea(this._def)),{},{schema:this,typeName:c.ZodEffects,effect:{type:"transform",transform:e}}))}default(e){return new e1(Q(Q({},ea(this._def)),{},{innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:c.ZodDefault}))}brand(){return new e9(Q({typeName:c.ZodBranded,type:this},ea(this._def)))}catch(e){return new e3(Q(Q({},ea(this._def)),{},{innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:c.ZodCatch}))}describe(e){return new this.constructor(Q(Q({},this._def),{},{description:e}))}pipe(e){return e4.create(this,e)}readonly(){return e5.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let es=/^c[^\s-]{8,}$/i,eo=/^[0-9a-z]+$/,el=/^[0-9A-HJKMNP-TV-Z]{26}$/i,ec=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,eu=/^[a-z0-9_-]{21}$/i,ed=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,ep=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,ef=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,eh=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,em=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,eg=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,ey=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,eb=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ev=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,eO="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",ex=RegExp(`^${eO}$`);function ew(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);let r=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${r}`}class e_ extends ei{_parse(e){var t,r,i,s;let o;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==R.string){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.string,received:t.parsedType}),W}let l=new B;for(let c of this._def.checks)if("min"===c.kind)e.data.length<c.value&&(Z(o=this._getOrReturnCtx(e,o),{code:L.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),l.dirty());else if("max"===c.kind)e.data.length>c.value&&(Z(o=this._getOrReturnCtx(e,o),{code:L.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!1,message:c.message}),l.dirty());else if("length"===c.kind){let t=e.data.length>c.value,r=e.data.length<c.value;(t||r)&&(o=this._getOrReturnCtx(e,o),t?Z(o,{code:L.too_big,maximum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}):r&&Z(o,{code:L.too_small,minimum:c.value,type:"string",inclusive:!0,exact:!0,message:c.message}),l.dirty())}else if("email"===c.kind)ef.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"email",code:L.invalid_string,message:c.message}),l.dirty());else if("emoji"===c.kind)n||(n=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),n.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"emoji",code:L.invalid_string,message:c.message}),l.dirty());else if("uuid"===c.kind)ec.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"uuid",code:L.invalid_string,message:c.message}),l.dirty());else if("nanoid"===c.kind)eu.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"nanoid",code:L.invalid_string,message:c.message}),l.dirty());else if("cuid"===c.kind)es.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"cuid",code:L.invalid_string,message:c.message}),l.dirty());else if("cuid2"===c.kind)eo.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"cuid2",code:L.invalid_string,message:c.message}),l.dirty());else if("ulid"===c.kind)el.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"ulid",code:L.invalid_string,message:c.message}),l.dirty());else if("url"===c.kind)try{new URL(e.data)}catch{Z(o=this._getOrReturnCtx(e,o),{validation:"url",code:L.invalid_string,message:c.message}),l.dirty()}else"regex"===c.kind?(c.regex.lastIndex=0,c.regex.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"regex",code:L.invalid_string,message:c.message}),l.dirty())):"trim"===c.kind?e.data=e.data.trim():"includes"===c.kind?e.data.includes(c.value,c.position)||(Z(o=this._getOrReturnCtx(e,o),{code:L.invalid_string,validation:{includes:c.value,position:c.position},message:c.message}),l.dirty()):"toLowerCase"===c.kind?e.data=e.data.toLowerCase():"toUpperCase"===c.kind?e.data=e.data.toUpperCase():"startsWith"===c.kind?e.data.startsWith(c.value)||(Z(o=this._getOrReturnCtx(e,o),{code:L.invalid_string,validation:{startsWith:c.value},message:c.message}),l.dirty()):"endsWith"===c.kind?e.data.endsWith(c.value)||(Z(o=this._getOrReturnCtx(e,o),{code:L.invalid_string,validation:{endsWith:c.value},message:c.message}),l.dirty()):"datetime"===c.kind?(function(e){let t=`${eO}T${ew(e)}`,r=[];return r.push(e.local?"Z?":"Z"),e.offset&&r.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${r.join("|")})`,RegExp(`^${t}$`)})(c).test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{code:L.invalid_string,validation:"datetime",message:c.message}),l.dirty()):"date"===c.kind?ex.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{code:L.invalid_string,validation:"date",message:c.message}),l.dirty()):"time"===c.kind?RegExp(`^${ew(c)}$`).test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{code:L.invalid_string,validation:"time",message:c.message}),l.dirty()):"duration"===c.kind?ep.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"duration",code:L.invalid_string,message:c.message}),l.dirty()):"ip"===c.kind?(t=e.data,!(("v4"===(r=c.version)||!r)&&eh.test(t)||("v6"===r||!r)&&eg.test(t))&&1&&(Z(o=this._getOrReturnCtx(e,o),{validation:"ip",code:L.invalid_string,message:c.message}),l.dirty())):"jwt"===c.kind?!function(e,t){if(!ed.test(e))return!1;try{let[r]=e.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),a=JSON.parse(atob(n));if("object"!=typeof a||null===a||"typ"in a&&a?.typ!=="JWT"||!a.alg||t&&a.alg!==t)return!1;return!0}catch{return!1}}(e.data,c.alg)&&(Z(o=this._getOrReturnCtx(e,o),{validation:"jwt",code:L.invalid_string,message:c.message}),l.dirty()):"cidr"===c.kind?(i=e.data,!(("v4"===(s=c.version)||!s)&&em.test(i)||("v6"===s||!s)&&ey.test(i))&&1&&(Z(o=this._getOrReturnCtx(e,o),{validation:"cidr",code:L.invalid_string,message:c.message}),l.dirty())):"base64"===c.kind?eb.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"base64",code:L.invalid_string,message:c.message}),l.dirty()):"base64url"===c.kind?ev.test(e.data)||(Z(o=this._getOrReturnCtx(e,o),{validation:"base64url",code:L.invalid_string,message:c.message}),l.dirty()):a.assertNever(c);return{status:l.value,value:e.data}}_regex(e,t,r){return this.refinement(t=>e.test(t),Q({validation:t,code:L.invalid_string},s.errToObj(r)))}_addCheck(e){return new e_(Q(Q({},this._def),{},{checks:[...this._def.checks,e]}))}email(e){return this._addCheck(Q({kind:"email"},s.errToObj(e)))}url(e){return this._addCheck(Q({kind:"url"},s.errToObj(e)))}emoji(e){return this._addCheck(Q({kind:"emoji"},s.errToObj(e)))}uuid(e){return this._addCheck(Q({kind:"uuid"},s.errToObj(e)))}nanoid(e){return this._addCheck(Q({kind:"nanoid"},s.errToObj(e)))}cuid(e){return this._addCheck(Q({kind:"cuid"},s.errToObj(e)))}cuid2(e){return this._addCheck(Q({kind:"cuid2"},s.errToObj(e)))}ulid(e){return this._addCheck(Q({kind:"ulid"},s.errToObj(e)))}base64(e){return this._addCheck(Q({kind:"base64"},s.errToObj(e)))}base64url(e){return this._addCheck(Q({kind:"base64url"},s.errToObj(e)))}jwt(e){return this._addCheck(Q({kind:"jwt"},s.errToObj(e)))}ip(e){return this._addCheck(Q({kind:"ip"},s.errToObj(e)))}cidr(e){return this._addCheck(Q({kind:"cidr"},s.errToObj(e)))}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck(Q({kind:"datetime",precision:void 0===e?.precision?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1},s.errToObj(e?.message)))}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck(Q({kind:"time",precision:void 0===e?.precision?null:e?.precision},s.errToObj(e?.message)))}duration(e){return this._addCheck(Q({kind:"duration"},s.errToObj(e)))}regex(e,t){return this._addCheck(Q({kind:"regex",regex:e},s.errToObj(t)))}includes(e,t){return this._addCheck(Q({kind:"includes",value:e,position:t?.position},s.errToObj(t?.message)))}startsWith(e,t){return this._addCheck(Q({kind:"startsWith",value:e},s.errToObj(t)))}endsWith(e,t){return this._addCheck(Q({kind:"endsWith",value:e},s.errToObj(t)))}min(e,t){return this._addCheck(Q({kind:"min",value:e},s.errToObj(t)))}max(e,t){return this._addCheck(Q({kind:"max",value:e},s.errToObj(t)))}length(e,t){return this._addCheck(Q({kind:"length",value:e},s.errToObj(t)))}nonempty(e){return this.min(1,s.errToObj(e))}trim(){return new e_(Q(Q({},this._def),{},{checks:[...this._def.checks,{kind:"trim"}]}))}toLowerCase(){return new e_(Q(Q({},this._def),{},{checks:[...this._def.checks,{kind:"toLowerCase"}]}))}toUpperCase(){return new e_(Q(Q({},this._def),{},{checks:[...this._def.checks,{kind:"toUpperCase"}]}))}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}e_.create=e=>new e_(Q({checks:[],typeName:c.ZodString,coerce:e?.coerce??!1},ea(e)));class ej extends ei{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let t;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==R.number){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.number,received:t.parsedType}),W}let r=new B;for(let n of this._def.checks)"int"===n.kind?a.isInteger(e.data)||(Z(t=this._getOrReturnCtx(e,t),{code:L.invalid_type,expected:"integer",received:"float",message:n.message}),r.dirty()):"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(Z(t=this._getOrReturnCtx(e,t),{code:L.too_small,minimum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(Z(t=this._getOrReturnCtx(e,t),{code:L.too_big,maximum:n.value,type:"number",inclusive:n.inclusive,exact:!1,message:n.message}),r.dirty()):"multipleOf"===n.kind?0!==function(e,t){let r=(e.toString().split(".")[1]||"").length,n=(t.toString().split(".")[1]||"").length,a=r>n?r:n;return Number.parseInt(e.toFixed(a).replace(".",""))%Number.parseInt(t.toFixed(a).replace(".",""))/10**a}(e.data,n.value)&&(Z(t=this._getOrReturnCtx(e,t),{code:L.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):"finite"===n.kind?Number.isFinite(e.data)||(Z(t=this._getOrReturnCtx(e,t),{code:L.not_finite,message:n.message}),r.dirty()):a.assertNever(n);return{status:r.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,s.toString(t))}gt(e,t){return this.setLimit("min",e,!1,s.toString(t))}lte(e,t){return this.setLimit("max",e,!0,s.toString(t))}lt(e,t){return this.setLimit("max",e,!1,s.toString(t))}setLimit(e,t,r,n){return new ej(Q(Q({},this._def),{},{checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:s.toString(n)}]}))}_addCheck(e){return new ej(Q(Q({},this._def),{},{checks:[...this._def.checks,e]}))}int(e){return this._addCheck({kind:"int",message:s.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:s.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:s.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:s.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:s.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:s.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:s.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:s.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:s.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&a.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let r of this._def.checks)if("finite"===r.kind||"int"===r.kind||"multipleOf"===r.kind)return!0;else"min"===r.kind?(null===t||r.value>t)&&(t=r.value):"max"===r.kind&&(null===e||r.value<e)&&(e=r.value);return Number.isFinite(t)&&Number.isFinite(e)}}ej.create=e=>new ej(Q({checks:[],typeName:c.ZodNumber,coerce:e?.coerce||!1},ea(e)));class eP extends ei{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let t;if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==R.bigint)return this._getInvalidInput(e);let r=new B;for(let n of this._def.checks)"min"===n.kind?(n.inclusive?e.data<n.value:e.data<=n.value)&&(Z(t=this._getOrReturnCtx(e,t),{code:L.too_small,type:"bigint",minimum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"max"===n.kind?(n.inclusive?e.data>n.value:e.data>=n.value)&&(Z(t=this._getOrReturnCtx(e,t),{code:L.too_big,type:"bigint",maximum:n.value,inclusive:n.inclusive,message:n.message}),r.dirty()):"multipleOf"===n.kind?e.data%n.value!==BigInt(0)&&(Z(t=this._getOrReturnCtx(e,t),{code:L.not_multiple_of,multipleOf:n.value,message:n.message}),r.dirty()):a.assertNever(n);return{status:r.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.bigint,received:t.parsedType}),W}gte(e,t){return this.setLimit("min",e,!0,s.toString(t))}gt(e,t){return this.setLimit("min",e,!1,s.toString(t))}lte(e,t){return this.setLimit("max",e,!0,s.toString(t))}lt(e,t){return this.setLimit("max",e,!1,s.toString(t))}setLimit(e,t,r,n){return new eP(Q(Q({},this._def),{},{checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:s.toString(n)}]}))}_addCheck(e){return new eP(Q(Q({},this._def),{},{checks:[...this._def.checks,e]}))}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:s.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:s.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:s.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:s.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:s.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}eP.create=e=>new eP(Q({checks:[],typeName:c.ZodBigInt,coerce:e?.coerce??!1},ea(e)));class eS extends ei{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==R.boolean){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.boolean,received:t.parsedType}),W}return J(e.data)}}eS.create=e=>new eS(Q({typeName:c.ZodBoolean,coerce:e?.coerce||!1},ea(e)));class eE extends ei{_parse(e){let t;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==R.date){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.date,received:t.parsedType}),W}if(Number.isNaN(e.data.getTime()))return Z(this._getOrReturnCtx(e),{code:L.invalid_date}),W;let r=new B;for(let n of this._def.checks)"min"===n.kind?e.data.getTime()<n.value&&(Z(t=this._getOrReturnCtx(e,t),{code:L.too_small,message:n.message,inclusive:!0,exact:!1,minimum:n.value,type:"date"}),r.dirty()):"max"===n.kind?e.data.getTime()>n.value&&(Z(t=this._getOrReturnCtx(e,t),{code:L.too_big,message:n.message,inclusive:!0,exact:!1,maximum:n.value,type:"date"}),r.dirty()):a.assertNever(n);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new eE(Q(Q({},this._def),{},{checks:[...this._def.checks,e]}))}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:s.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:s.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}eE.create=e=>new eE(Q({checks:[],coerce:e?.coerce||!1,typeName:c.ZodDate},ea(e)));class eA extends ei{_parse(e){if(this._getType(e)!==R.symbol){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.symbol,received:t.parsedType}),W}return J(e.data)}}eA.create=e=>new eA(Q({typeName:c.ZodSymbol},ea(e)));class eN extends ei{_parse(e){if(this._getType(e)!==R.undefined){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.undefined,received:t.parsedType}),W}return J(e.data)}}eN.create=e=>new eN(Q({typeName:c.ZodUndefined},ea(e)));class eT extends ei{_parse(e){if(this._getType(e)!==R.null){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.null,received:t.parsedType}),W}return J(e.data)}}eT.create=e=>new eT(Q({typeName:c.ZodNull},ea(e)));class eI extends ei{constructor(){super(...arguments),this._any=!0}_parse(e){return J(e.data)}}eI.create=e=>new eI(Q({typeName:c.ZodAny},ea(e)));class eD extends ei{constructor(){super(...arguments),this._unknown=!0}_parse(e){return J(e.data)}}eD.create=e=>new eD(Q({typeName:c.ZodUnknown},ea(e)));class ek extends ei{_parse(e){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.never,received:t.parsedType}),W}}ek.create=e=>new ek(Q({typeName:c.ZodNever},ea(e)));class eC extends ei{_parse(e){if(this._getType(e)!==R.undefined){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.void,received:t.parsedType}),W}return J(e.data)}}eC.create=e=>new eC(Q({typeName:c.ZodVoid},ea(e)));class eR extends ei{_parse(e){let{ctx:t,status:r}=this._processInputParams(e),n=this._def;if(t.parsedType!==R.array)return Z(t,{code:L.invalid_type,expected:R.array,received:t.parsedType}),W;if(null!==n.exactLength){let e=t.data.length>n.exactLength.value,a=t.data.length<n.exactLength.value;(e||a)&&(Z(t,{code:e?L.too_big:L.too_small,minimum:a?n.exactLength.value:void 0,maximum:e?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),r.dirty())}if(null!==n.minLength&&t.data.length<n.minLength.value&&(Z(t,{code:L.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),r.dirty()),null!==n.maxLength&&t.data.length>n.maxLength.value&&(Z(t,{code:L.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),r.dirty()),t.common.async)return Promise.all([...t.data].map((e,r)=>n.type._parseAsync(new er(t,e,t.path,r)))).then(e=>B.mergeArray(r,e));let a=[...t.data].map((e,r)=>n.type._parseSync(new er(t,e,t.path,r)));return B.mergeArray(r,a)}get element(){return this._def.type}min(e,t){return new eR(Q(Q({},this._def),{},{minLength:{value:e,message:s.toString(t)}}))}max(e,t){return new eR(Q(Q({},this._def),{},{maxLength:{value:e,message:s.toString(t)}}))}length(e,t){return new eR(Q(Q({},this._def),{},{exactLength:{value:e,message:s.toString(t)}}))}nonempty(e){return this.min(1,e)}}eR.create=(e,t)=>new eR(Q({type:e,minLength:null,maxLength:null,exactLength:null,typeName:c.ZodArray},ea(t)));class e$ extends ei{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),t=a.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==R.object){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.object,received:t.parsedType}),W}let{status:t,ctx:r}=this._processInputParams(e),{shape:n,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof ek&&"strip"===this._def.unknownKeys))for(let e in r.data)a.includes(e)||i.push(e);let s=[];for(let e of a){let t=n[e],a=r.data[e];s.push({key:{status:"valid",value:e},value:t._parse(new er(r,a,r.path,e)),alwaysSet:e in r.data})}if(this._def.catchall instanceof ek){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of i)s.push({key:{status:"valid",value:e},value:{status:"valid",value:r.data[e]}});else if("strict"===e)i.length>0&&(Z(r,{code:L.unrecognized_keys,keys:i}),t.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let t of i){let n=r.data[t];s.push({key:{status:"valid",value:t},value:e._parse(new er(r,n,r.path,t)),alwaysSet:t in r.data})}}return r.common.async?Promise.resolve().then(async()=>{let e=[];for(let t of s){let r=await t.key,n=await t.value;e.push({key:r,value:n,alwaysSet:t.alwaysSet})}return e}).then(e=>B.mergeObjectSync(t,e)):B.mergeObjectSync(t,s)}get shape(){return this._def.shape()}strict(e){return s.errToObj,new e$(Q(Q({},this._def),{},{unknownKeys:"strict"},void 0!==e?{errorMap:(t,r)=>{let n=this._def.errorMap?.(t,r).message??r.defaultError;return"unrecognized_keys"===t.code?{message:s.errToObj(e).message??n}:{message:n}}}:{}))}strip(){return new e$(Q(Q({},this._def),{},{unknownKeys:"strip"}))}passthrough(){return new e$(Q(Q({},this._def),{},{unknownKeys:"passthrough"}))}extend(e){return new e$(Q(Q({},this._def),{},{shape:()=>Q(Q({},this._def.shape()),e)}))}merge(e){return new e$({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>Q(Q({},this._def.shape()),e._def.shape()),typeName:c.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new e$(Q(Q({},this._def),{},{catchall:e}))}pick(e){let t={};for(let r of a.objectKeys(e))e[r]&&this.shape[r]&&(t[r]=this.shape[r]);return new e$(Q(Q({},this._def),{},{shape:()=>t}))}omit(e){let t={};for(let r of a.objectKeys(this.shape))e[r]||(t[r]=this.shape[r]);return new e$(Q(Q({},this._def),{},{shape:()=>t}))}deepPartial(){return function e(t){if(t instanceof e$){let r={};for(let n in t.shape){let a=t.shape[n];r[n]=eQ.create(e(a))}return new e$(Q(Q({},t._def),{},{shape:()=>r}))}if(t instanceof eR)return new eR(Q(Q({},t._def),{},{type:e(t.element)}));if(t instanceof eQ)return eQ.create(e(t.unwrap()));if(t instanceof e0)return e0.create(e(t.unwrap()));if(t instanceof eq)return eq.create(t.items.map(t=>e(t)));else return t}(this)}partial(e){let t={};for(let r of a.objectKeys(this.shape)){let n=this.shape[r];e&&!e[r]?t[r]=n:t[r]=n.optional()}return new e$(Q(Q({},this._def),{},{shape:()=>t}))}required(e){let t={};for(let r of a.objectKeys(this.shape))if(e&&!e[r])t[r]=this.shape[r];else{let e=this.shape[r];for(;e instanceof eQ;)e=e._def.innerType;t[r]=e}return new e$(Q(Q({},this._def),{},{shape:()=>t}))}keyof(){return eX(a.objectKeys(this.shape))}}e$.create=(e,t)=>new e$(Q({shape:()=>e,unknownKeys:"strip",catchall:ek.create(),typeName:c.ZodObject},ea(t))),e$.strictCreate=(e,t)=>new e$(Q({shape:()=>e,unknownKeys:"strict",catchall:ek.create(),typeName:c.ZodObject},ea(t))),e$.lazycreate=(e,t)=>new e$(Q({shape:e,unknownKeys:"strip",catchall:ek.create(),typeName:c.ZodObject},ea(t)));class eL extends ei{_parse(e){let{ctx:t}=this._processInputParams(e),r=this._def.options;if(t.common.async)return Promise.all(r.map(async e=>{let r=Q(Q({},t),{},{common:Q(Q({},t.common),{},{issues:[]}),parent:null});return{result:await e._parseAsync({data:t.data,path:t.path,parent:r}),ctx:r}})).then(function(e){for(let t of e)if("valid"===t.result.status)return t.result;for(let r of e)if("dirty"===r.result.status)return t.common.issues.push(...r.ctx.common.issues),r.result;let r=e.map(e=>new M(e.ctx.common.issues));return Z(t,{code:L.invalid_union,unionErrors:r}),W});{let e,n=[];for(let a of r){let r=Q(Q({},t),{},{common:Q(Q({},t.common),{},{issues:[]}),parent:null}),i=a._parseSync({data:t.data,path:t.path,parent:r});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:r}),r.common.issues.length&&n.push(r.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;let a=n.map(e=>new M(e));return Z(t,{code:L.invalid_union,unionErrors:a}),W}}get options(){return this._def.options}}eL.create=(e,t)=>new eL(Q({options:e,typeName:c.ZodUnion},ea(t)));let eM=e=>{if(e instanceof eV)return eM(e.schema);if(e instanceof eK)return eM(e.innerType());if(e instanceof eJ)return[e.value];if(e instanceof eH)return e.options;if(e instanceof eG)return a.objectValues(e.enum);else if(e instanceof e1)return eM(e._def.innerType);else if(e instanceof eN)return[void 0];else if(e instanceof eT)return[null];else if(e instanceof eQ)return[void 0,...eM(e.unwrap())];else if(e instanceof e0)return[null,...eM(e.unwrap())];else if(e instanceof e9)return eM(e.unwrap());else if(e instanceof e5)return eM(e.unwrap());else if(e instanceof e3)return eM(e._def.innerType);else return[]};class eU extends ei{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==R.object)return Z(t,{code:L.invalid_type,expected:R.object,received:t.parsedType}),W;let r=this.discriminator,n=t.data[r],a=this.optionsMap.get(n);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(Z(t,{code:L.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[r]}),W)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,r){let n=new Map;for(let r of t){let t=eM(r.shape[e]);if(!t.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let a of t){if(n.has(a))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);n.set(a,r)}}return new eU(Q({typeName:c.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n},ea(r)))}}class eF extends ei{_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=(e,n)=>{if(X(e)||X(n))return W;let i=function e(t,r){let n=$(t),i=$(r);if(t===r)return{valid:!0,data:t};if(n===R.object&&i===R.object){let n=a.objectKeys(r),i=a.objectKeys(t).filter(e=>-1!==n.indexOf(e)),s=Q(Q({},t),r);for(let n of i){let a=e(t[n],r[n]);if(!a.valid)return{valid:!1};s[n]=a.data}return{valid:!0,data:s}}if(n===R.array&&i===R.array){if(t.length!==r.length)return{valid:!1};let n=[];for(let a=0;a<t.length;a++){let i=e(t[a],r[a]);if(!i.valid)return{valid:!1};n.push(i.data)}return{valid:!0,data:n}}if(n===R.date&&i===R.date&&+t==+r)return{valid:!0,data:t};return{valid:!1}}(e.value,n.value);return i.valid?((H(e)||H(n))&&t.dirty(),{status:t.value,value:i.data}):(Z(r,{code:L.invalid_intersection_types}),W)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([e,t])=>n(e,t)):n(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}eF.create=(e,t,r)=>new eF(Q({left:e,right:t,typeName:c.ZodIntersection},ea(r)));class eq extends ei{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==R.array)return Z(r,{code:L.invalid_type,expected:R.array,received:r.parsedType}),W;if(r.data.length<this._def.items.length)return Z(r,{code:L.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),W;!this._def.rest&&r.data.length>this._def.items.length&&(Z(r,{code:L.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let n=[...r.data].map((e,t)=>{let n=this._def.items[t]||this._def.rest;return n?n._parse(new er(r,e,r.path,t)):null}).filter(e=>!!e);return r.common.async?Promise.all(n).then(e=>B.mergeArray(t,e)):B.mergeArray(t,n)}get items(){return this._def.items}rest(e){return new eq(Q(Q({},this._def),{},{rest:e}))}}eq.create=(e,t)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new eq(Q({items:e,typeName:c.ZodTuple,rest:null},ea(t)))};class ez extends ei{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==R.object)return Z(r,{code:L.invalid_type,expected:R.object,received:r.parsedType}),W;let n=[],a=this._def.keyType,i=this._def.valueType;for(let e in r.data)n.push({key:a._parse(new er(r,e,r.path,e)),value:i._parse(new er(r,r.data[e],r.path,e)),alwaysSet:e in r.data});return r.common.async?B.mergeObjectAsync(t,n):B.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,r){return new ez(t instanceof ei?Q({keyType:e,valueType:t,typeName:c.ZodRecord},ea(r)):Q({keyType:e_.create(),valueType:e,typeName:c.ZodRecord},ea(t)))}}class eZ extends ei{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==R.map)return Z(r,{code:L.invalid_type,expected:R.map,received:r.parsedType}),W;let n=this._def.keyType,a=this._def.valueType,i=[...r.data.entries()].map(([e,t],i)=>({key:n._parse(new er(r,e,r.path,[i,"key"])),value:a._parse(new er(r,t,r.path,[i,"value"]))}));if(r.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let r of i){let n=await r.key,a=await r.value;if("aborted"===n.status||"aborted"===a.status)return W;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}})}{let e=new Map;for(let r of i){let n=r.key,a=r.value;if("aborted"===n.status||"aborted"===a.status)return W;("dirty"===n.status||"dirty"===a.status)&&t.dirty(),e.set(n.value,a.value)}return{status:t.value,value:e}}}}eZ.create=(e,t,r)=>new eZ(Q({valueType:t,keyType:e,typeName:c.ZodMap},ea(r)));class eB extends ei{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.parsedType!==R.set)return Z(r,{code:L.invalid_type,expected:R.set,received:r.parsedType}),W;let n=this._def;null!==n.minSize&&r.data.size<n.minSize.value&&(Z(r,{code:L.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),null!==n.maxSize&&r.data.size>n.maxSize.value&&(Z(r,{code:L.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let a=this._def.valueType;function i(e){let r=new Set;for(let n of e){if("aborted"===n.status)return W;"dirty"===n.status&&t.dirty(),r.add(n.value)}return{status:t.value,value:r}}let s=[...r.data.values()].map((e,t)=>a._parse(new er(r,e,r.path,t)));return r.common.async?Promise.all(s).then(e=>i(e)):i(s)}min(e,t){return new eB(Q(Q({},this._def),{},{minSize:{value:e,message:s.toString(t)}}))}max(e,t){return new eB(Q(Q({},this._def),{},{maxSize:{value:e,message:s.toString(t)}}))}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}eB.create=(e,t)=>new eB(Q({valueType:e,minSize:null,maxSize:null,typeName:c.ZodSet},ea(t)));class eW extends ei{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==R.function)return Z(t,{code:L.invalid_type,expected:R.function,received:t.parsedType}),W;function r(e,r){return z({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,U,U].filter(e=>!!e),issueData:{code:L.invalid_arguments,argumentsError:r}})}function n(e,r){return z({data:e,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,U,U].filter(e=>!!e),issueData:{code:L.invalid_return_type,returnTypeError:r}})}let a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof eY){let e=this;return J(async function(...t){let s=new M([]),o=await e._def.args.parseAsync(t,a).catch(e=>{throw s.addIssue(r(t,e)),s}),l=await Reflect.apply(i,this,o);return await e._def.returns._def.type.parseAsync(l,a).catch(e=>{throw s.addIssue(n(l,e)),s})})}{let e=this;return J(function(...t){let s=e._def.args.safeParse(t,a);if(!s.success)throw new M([r(t,s.error)]);let o=Reflect.apply(i,this,s.data),l=e._def.returns.safeParse(o,a);if(!l.success)throw new M([n(o,l.error)]);return l.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new eW(Q(Q({},this._def),{},{args:eq.create(e).rest(eD.create())}))}returns(e){return new eW(Q(Q({},this._def),{},{returns:e}))}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,r){return new eW(Q({args:e||eq.create([]).rest(eD.create()),returns:t||eD.create(),typeName:c.ZodFunction},ea(r)))}}class eV extends ei{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}eV.create=(e,t)=>new eV(Q({getter:e,typeName:c.ZodLazy},ea(t)));class eJ extends ei{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return Z(t,{received:t.data,code:L.invalid_literal,expected:this._def.value}),W}return{status:"valid",value:e.data}}get value(){return this._def.value}}function eX(e,t){return new eH(Q({values:e,typeName:c.ZodEnum},ea(t)))}eJ.create=(e,t)=>new eJ(Q({value:e,typeName:c.ZodLiteral},ea(t)));class eH extends ei{constructor(){super(...arguments),o.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let t=this._getOrReturnCtx(e),r=this._def.values;return Z(t,{expected:a.joinValues(r),received:t.parsedType,code:L.invalid_type}),W}if(ee(this,o,"f")||et(this,o,new Set(this._def.values),"f"),!ee(this,o,"f").has(e.data)){let t=this._getOrReturnCtx(e),r=this._def.values;return Z(t,{received:t.data,code:L.invalid_enum_value,options:r}),W}return J(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return eH.create(e,Q(Q({},this._def),t))}exclude(e,t=this._def){return eH.create(this.options.filter(t=>!e.includes(t)),Q(Q({},this._def),t))}}o=new WeakMap,eH.create=eX;class eG extends ei{constructor(){super(...arguments),l.set(this,void 0)}_parse(e){let t=a.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(e);if(r.parsedType!==R.string&&r.parsedType!==R.number){let e=a.objectValues(t);return Z(r,{expected:a.joinValues(e),received:r.parsedType,code:L.invalid_type}),W}if(ee(this,l,"f")||et(this,l,new Set(a.getValidEnumValues(this._def.values)),"f"),!ee(this,l,"f").has(e.data)){let e=a.objectValues(t);return Z(r,{received:r.data,code:L.invalid_enum_value,options:e}),W}return J(e.data)}get enum(){return this._def.values}}l=new WeakMap,eG.create=(e,t)=>new eG(Q({values:e,typeName:c.ZodNativeEnum},ea(t)));class eY extends ei{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);return t.parsedType!==R.promise&&!1===t.common.async?(Z(t,{code:L.invalid_type,expected:R.promise,received:t.parsedType}),W):J((t.parsedType===R.promise?t.data:Promise.resolve(t.data)).then(e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap})))}}eY.create=(e,t)=>new eY(Q({type:e,typeName:c.ZodPromise},ea(t)));class eK extends ei{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===c.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:r}=this._processInputParams(e),n=this._def.effect||null,i={addIssue:e=>{Z(r,e),e.fatal?t.abort():t.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===n.type){let e=n.transform(r.data,i);if(r.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===t.value)return W;let n=await this._def.schema._parseAsync({data:e,path:r.path,parent:r});return"aborted"===n.status?W:"dirty"===n.status||"dirty"===t.value?V(n.value):n});{if("aborted"===t.value)return W;let n=this._def.schema._parseSync({data:e,path:r.path,parent:r});return"aborted"===n.status?W:"dirty"===n.status||"dirty"===t.value?V(n.value):n}}if("refinement"===n.type){let e=e=>{let t=n.refinement(e,i);if(r.common.async)return Promise.resolve(t);if(t instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(r=>"aborted"===r.status?W:("dirty"===r.status&&t.dirty(),e(r.value).then(()=>({status:t.value,value:r.value}))));{let n=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===n.status?W:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}}if("transform"===n.type)if(!1!==r.common.async)return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(e=>G(e)?Promise.resolve(n.transform(e.value,i)).then(e=>({status:t.value,value:e})):e);else{let e=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!G(e))return e;let a=n.transform(e.value,i);if(a instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}a.assertNever(n)}}eK.create=(e,t,r)=>new eK(Q({schema:e,typeName:c.ZodEffects,effect:t},ea(r))),eK.createWithPreprocess=(e,t,r)=>new eK(Q({schema:t,effect:{type:"preprocess",transform:e},typeName:c.ZodEffects},ea(r)));class eQ extends ei{_parse(e){return this._getType(e)===R.undefined?J(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}eQ.create=(e,t)=>new eQ(Q({innerType:e,typeName:c.ZodOptional},ea(t)));class e0 extends ei{_parse(e){return this._getType(e)===R.null?J(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}e0.create=(e,t)=>new e0(Q({innerType:e,typeName:c.ZodNullable},ea(t)));class e1 extends ei{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return t.parsedType===R.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}e1.create=(e,t)=>new e1(Q({innerType:e,typeName:c.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default},ea(t)));class e3 extends ei{_parse(e){let{ctx:t}=this._processInputParams(e),r=Q(Q({},t),{},{common:Q(Q({},t.common),{},{issues:[]})}),n=this._def.innerType._parse({data:r.data,path:r.path,parent:Q({},r)});return Y(n)?n.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new M(r.common.issues)},input:r.data})})):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new M(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}e3.create=(e,t)=>new e3(Q({innerType:e,typeName:c.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch},ea(t)));class e2 extends ei{_parse(e){if(this._getType(e)!==R.nan){let t=this._getOrReturnCtx(e);return Z(t,{code:L.invalid_type,expected:R.nan,received:t.parsedType}),W}return{status:"valid",value:e.data}}}e2.create=e=>new e2(Q({typeName:c.ZodNaN},ea(e))),Symbol("zod_brand");class e9 extends ei{_parse(e){let{ctx:t}=this._processInputParams(e),r=t.data;return this._def.type._parse({data:r,path:t.path,parent:t})}unwrap(){return this._def.type}}class e4 extends ei{_parse(e){let{status:t,ctx:r}=this._processInputParams(e);if(r.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?W:"dirty"===e.status?(t.dirty(),V(e.value)):this._def.out._parseAsync({data:e.value,path:r.path,parent:r})})();{let e=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return"aborted"===e.status?W:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:r.path,parent:r})}}static create(e,t){return new e4({in:e,out:t,typeName:c.ZodPipeline})}}class e5 extends ei{_parse(e){let t=this._def.innerType._parse(e),r=e=>(G(e)&&(e.value=Object.freeze(e.value)),e);return Y(t)?t.then(e=>r(e)):r(t)}unwrap(){return this._def.innerType}}e5.create=(e,t)=>new e5(Q({innerType:e,typeName:c.ZodReadonly},ea(t))),e$.lazycreate,function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(c||(c={}));let e6=e_.create,e8=ej.create;e2.create,eP.create,eS.create,eE.create,eA.create;let e7=eN.create,te=eT.create;eI.create,eD.create,ek.create,eC.create;let tt=eR.create,tr=e$.create;e$.strictCreate;let tn=eL.create;eU.create,eF.create,eq.create,ez.create,eZ.create,eB.create,eW.create,eV.create;let ta=eJ.create;eH.create,eG.create,eY.create,eK.create,eQ.create,e0.create,eK.createWithPreprocess,e4.create;let ti=tr({id:e6().optional(),titulo:e6().min(1).max(200),contenido:e6().min(1),categoria:e6().optional().nullable(),numero_tema:tn([e8().int().positive(),e6(),te(),e7()]).optional(),creado_en:e6().optional(),actualizado_en:e6().optional(),user_id:e6().optional(),tipo_original:e6().optional()}),ts=tr({pregunta:e6().min(1).max(500),documentos:tt(ti).min(1)}),to=tr({action:ta("generarTest"),peticion:e6().min(1).max(500),contextos:tt(e6().min(1)),cantidad:e8().int().min(1).max(50).optional()}),tl=tr({action:ta("generarFlashcards"),peticion:e6().min(1).max(500),contextos:tt(e6().min(1)),cantidad:e8().int().min(1).max(50).optional()}),tc=tr({action:ta("generarMapaMental"),peticion:e6().min(1).max(500),contextos:tt(e6().min(1))}),tu=tr({action:ta("generarPlanEstudios"),peticion:e6().min(1),contextos:tt(e6()).optional()}),td=tn([ts,to,tl,tc,tu,tr({action:ta("generarResumen"),peticion:e6().min(1).max(1e3),contextos:tt(e6().min(1)).length(1)}),tr({action:ta("editarResumen"),contextos:tt(e6().min(1)).length(1)})]);var tp=r(85315),tf=r(83256),th=r(28319);async function tm(e,t,r=1){try{let n=await th.SupabaseAdminService.getUserProfile(e);n?.subscription_plan==="free"&&(await tf.FreeAccountService.incrementUsageCount(e,t,r)?console.log(`✅ Incremented ${t} usage by ${r} for free user ${e}`):console.warn(`⚠️ Failed to increment ${t} usage count for user ${e}`))}catch(t){console.error(`❌ Error incrementing usage for user ${e}:`,t)}}async function tg(e){try{let t,r=await (0,m.createServerSupabaseClient)(),{data:{user:n},error:a}=await r.auth.getUser();if(!n)return h.NextResponse.json({error:"Unauthorized",debug:{userError:a?.message,hasCookies:!!e.headers.get("cookie")}},{status:401});let i=await e.json(),s=td.safeParse(i);if(!s.success)return h.NextResponse.json({error:"Datos inv\xe1lidos",detalles:s.error.errors},{status:400});if(i.pregunta&&i.documentos){let e=await tp.o.canUserPerformAction(n.id,"ai_chat",1);if(!e.allowed)return h.NextResponse.json({error:"Acceso denegado: "+e.reason},{status:403});let t=await w(i.pregunta,i.documentos);return h.NextResponse.json({result:t})}let{action:o,peticion:l,contextos:c,cantidad:u,temarioId:d}=i;switch(o){case"generarTest":let p=await tp.o.canUserPerformAction(n.id,"test_generation",u||1);if(!p.allowed)return h.NextResponse.json({error:"Acceso denegado: "+p.reason},{status:403});t=await P(l,c,u),await tm(n.id,"tests",u||1);break;case"generarFlashcards":let f=await tp.o.canUserPerformAction(n.id,"flashcard_generation",u||1);if(!f.allowed)return h.NextResponse.json({error:"Acceso denegado: "+f.reason},{status:403});t=await _(l,c,u),await tm(n.id,"flashcards",u||1);break;case"generarMapaMental":let g=await tp.o.canUserPerformAction(n.id,"mind_map_generation",1);if(!g.allowed)return h.NextResponse.json({error:"Acceso denegado: "+g.reason},{status:403});t=await j(l,c),await tm(n.id,"mindMaps",1);break;case"generarResumen":let y=await tp.o.canUserPerformAction(n.id,"summary_generation",1);if(!y.allowed)return h.NextResponse.json({error:"Acceso denegado: "+y.reason},{status:403});if(!c||1!==c.length)throw Error("Se requiere exactamente un documento para generar un resumen");let b={titulo:l.split("|")[0]||"Documento sin t\xedtulo",contenido:c[0],categoria:l.split("|")[1],numero_tema:l.split("|")[2]?parseInt(l.split("|")[2]):void 0},v=l.split("|")[3]||void 0;t=await S(b,v);break;case"editarResumen":if(!c[0])throw Error("Se requiere el contenido del resumen para editarlo");t=await D(c[0]);break;case"generarPlanEstudios":let O=await tp.o.canUserPerformAction(n.id,"study_planning",1);if(!O.allowed)return h.NextResponse.json({error:"Acceso denegado: "+O.reason},{status:403});let x=l||d;if(!x)throw Error("Se requiere temarioId para generar el plan de estudios");t=await I(x,n);break;default:return h.NextResponse.json({error:"Acci\xf3n no soportada"},{status:400})}return h.NextResponse.json({result:t})}catch(e){return console.error("❌ Error en API AI:",e),h.NextResponse.json({error:"Error interno del servidor",detalles:e.message},{status:500})}}let ty=new d.AppRouteRouteModule({definition:{kind:p.RouteKind.APP_ROUTE,page:"/api/ai/route",pathname:"/api/ai",filename:"route",bundlePath:"app/api/ai/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\api\\ai\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:tb,workUnitAsyncStorage:tv,serverHooks:tO}=ty;function tx(){return(0,f.patchFetch)({workAsyncStorage:tb,workUnitAsyncStorage:tv})}},86007:(e,t,r)=>{r.d(t,{HV:()=>s,Q_:()=>o,RT:()=>i,f7:()=>a,fD:()=>l,y1:()=>c,zM:()=>n});let n=`
Eres "Mentor Opositor AI", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misi\xf3n principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en \xfaltima instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, did\xe1ctico, motivador y emp\xe1tico.

Responde SIEMPRE en espa\xf1ol.

CONTEXTO DEL TEMARIO (Informaci\xf3n base para tus explicaciones):
{documentos}

PREGUNTA DEL OPOSITOR/A:
{pregunta}

INSTRUCCIONES DETALLADAS PARA ACTUAR COMO "MENTOR OPOSITOR AI":

I. PRINCIPIOS GENERALES DE RESPUESTA:

1.  Adaptabilidad de la Extensi\xf3n y Tono Inicial:
    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como "\xa1Excelente pregunta!". Puedes usar una frase validando la pregunta o mostrando empat\xeda de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayor\xeda de los casos, es mejor empezar directamente con la informaci\xf3n solicitada.
    -   Preguntas Espec\xedficas sobre Contenido: Si la pregunta es sobre un concepto, definici\xf3n, detalle del temario, o pide una explicaci\xf3n profunda de una secci\xf3n, puedes extenderte para asegurar una comprensi\xf3n completa, siempre bas\xe1ndote en el CONTEXTO.
    -   Preguntas sobre Estructura, Planificaci\xf3n o Consejos Generales: Si la pregunta es sobre c\xf3mo abordar el estudio de un tema, cu\xe1les son sus apartados principales, o pide consejos generales, s\xe9 estrat\xe9gico y conciso. Evita resumir todo el contenido del tema. C\xe9ntrate en el m\xe9todo, la estructura o los puntos clave de forma resumida.
    -   Claridad ante Todo: Independientemente de la extensi\xf3n, la claridad y la precisi\xf3n son primordiales.

2.  Respuesta Basada en el Contexto (Precisi\xf3n Absoluta):
    -   Tu respuesta DEBE basarse ESTRICTA y \xdaNICAMENTE en la informaci\xf3n proporcionada en el "CONTEXTO DEL TEMARIO".
    -   Si la informaci\xf3n necesaria no est\xe1 en el contexto, ind\xedcalo claramente (e.g., "El temario que me has proporcionado aborda X de esta manera... Para un detalle m\xe1s exhaustivo sobre Y, ser\xeda necesario consultar fuentes complementarias."). NO INVENTES INFORMACI\xd3N.
    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisi\xf3n o para ilustrar un punto crucial, introduci\xe9ndolas de forma natural.

II. FORMATO DE LISTAS JER\xc1RQUICAS (CUANDO APLIQUE):
Al presentar informaci\xf3n estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jer\xe1rquica ESTRICTO:
Ejemplo de formato:
1.  Apartado Principal Uno
    a)  Subapartado Nivel 1
        -   Elemento Nivel 2 (con un guion y espacio)
            *   Detalle Nivel 3 (con un asterisco y espacio)
    b)  Otro Subapartado Nivel 1
2.  Apartado Principal Dos
    a)  Subapartado...

-   Utiliza n\xfameros seguidos de un punto (1., 2.) para el nivel m\xe1s alto.
-   Utiliza letras min\xfasculas seguidas de un par\xe9ntesis (a), b)) para el segundo nivel, indentado.
-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.
-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.
-   Aseg\xfarate de que la indentaci\xf3n sea clara para reflejar la jerarqu\xeda.
-   NO uses formato markdown de \xe9nfasis (como dobles asteriscos) para los t\xedtulos de los elementos de la lista en TU SALIDA; la propia estructura jer\xe1rquica y la numeraci\xf3n/vi\xf1eta son suficientes.

III. TIPOS DE RESPUESTA Y ENFOQUES ESPEC\xcdFICOS:

A.  Si la PREGUNTA es sobre "CU\xc1LES SON LOS APARTADOS DE UN TEMA" o "ESTRUCTURA DEL TEMA":
    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JER\xc1RQUICAS detallado en la secci\xf3n II.
    -   Contenido por Elemento de Lista:
        1.  Apartados Principales (Nivel 1 - N\xfameros): Indica su t\xedtulo exacto o una par\xe1frasis muy fiel. A continuaci\xf3n, en 1-2 frases concisas, describe su prop\xf3sito general.
        2.  Subapartados (Nivel 2 - Letras): Solo el t\xedtulo o idea principal en muy pocas palabras.
        3.  Niveles Inferiores (Guion, Asterisco): Solo el t\xedtulo o idea principal en muy pocas palabras.
    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aqu\xed.
    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes a\xf1adir una frase sugiriendo un orden de estudio.
    -   Qu\xe9 EVITAR: Descripciones largas del contenido de cada elemento de la lista. P\xe1rrafos extensos dentro de la lista.

B.  Si la PREGUNTA es sobre C\xd3MO ESTUDIAR UN TEMA (enfoque metodol\xf3gico):
    -   Enfoque Estrat\xe9gico y Conciso:
        1.  Visi\xf3n General Breve.
        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o t\xe9cnicas de estudio clave y concretas.
        3.  Menciona 2-3 Puntos Transversales Cr\xedticos (si los hay).
        4.  Consejo General Final.
    -   Qu\xe9 EVITAR: Resumir detalladamente el contenido al explicar la t\xe9cnica. Uso excesivo de \xe9nfasis.

C.  Si la PREGUNTA es sobre un CONCEPTO ESPEC\xcdFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACI\xd3N PROFUNDA:
    -   Enfoque Explicativo y Did\xe1ctico (Puedes Extenderte):
        (Mantener las sub-instrucciones de explicaci\xf3n detallada: Definici\xf3n, Terminolog\xeda, Relevancia, Puntos Clave, Ejemplos, Conexiones).
    -   Si necesitas desglosar una explicaci\xf3n en m\xfaltiples puntos, puedes usar el FORMATO DE LISTAS JER\xc1RQUICAS de la secci\xf3n II.

IV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):

1.  Claridad y Estructura: Utiliza p\xe1rrafos bien definidos. Cuando uses listas, sigue el formato especificado. No destaques ning\xfan concepto o palabra (sin negrita)
2.  Tono: Profesional, did\xe1ctico, paciente, motivador y positivo. S\xe9 directo y ve al grano, especialmente al inicio de la respuesta.
3.  Cierre:
    -   Finaliza ofreciendo m\xe1s ayuda o preguntando si la explicaci\xf3n ha sido clara (e.g., "\xbfQueda clara la estructura as\xed?", "\xbfNecesitas que profundicemos en alg\xfan punto de estos apartados?").
    -   Termina con una frase de \xe1nimo variada y natural, no siempre la misma.

PRIORIDAD M\xc1XIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensi\xf3n y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir informaci\xf3n no contextual.

`,a=`
Eres "Mentor Opositor AI", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards ser\xe1n utilizadas por un estudiante para repasar conceptos clave.

CONTEXTO DEL TEMARIO (Informaci\xf3n base para tus flashcards):
{documentos}

PETICI\xd3N DEL USUARIO:
Genera {cantidad} flashcards de alta calidad.
{instrucciones}

RAZONAMIENTO PASO A PASO (Chain of Thought):
Antes de generar las flashcards, piensa paso a paso:

1. CONTEO REQUERIDO: Debo generar EXACTAMENTE {cantidad} flashcards
2. PLANIFICACI\xd3N: Voy a crear {cantidad} flashcards numeradas del 1 al {cantidad}
3. ESTRATEGIA: Distribuir\xe9 las flashcards cubriendo diferentes conceptos del contenido
4. VERIFICACI\xd3N: Al final contar\xe9 las flashcards para asegurarme de que son exactamente {cantidad}

Ahora procedo a generar las {cantidad} flashcards siguiendo este plan:

Flashcard 1 de {cantidad}: [generar\xe9 la primera flashcard]
Flashcard 2 de {cantidad}: [generar\xe9 la segunda flashcard]
...
Flashcard {cantidad} de {cantidad}: [generar\xe9 la \xfaltima flashcard]

VERIFICACI\xd3N FINAL: Confirmar\xe9 que he generado exactamente {cantidad} flashcards como se solicit\xf3.

INSTRUCCIONES PARA CREAR FLASHCARDS:

1. Genera EXACTAMENTE {cantidad} flashcards de alta calidad basadas \xdaNICAMENTE en la informaci\xf3n proporcionada en el CONTEXTO DEL TEMARIO.
2. Cada flashcard debe tener:
   - Una pregunta clara y concisa en el anverso
   - Una respuesta completa pero concisa en el reverso
3. Las preguntas deben ser variadas e incluir:
   - Definiciones de conceptos clave
   - Relaciones entre conceptos
   - Aplicaciones pr\xe1cticas
   - Clasificaciones o categor\xedas
4. Las respuestas deben:
   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO
   - Incluir la informaci\xf3n esencial sin ser excesivamente largas
   - Estar redactadas de forma clara y did\xe1ctica
5. NO inventes informaci\xf3n que no est\xe9 en el CONTEXTO.
6. Responde SIEMPRE en espa\xf1ol.

FORMATO DE RESPUESTA:
Debes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades "pregunta" y "respuesta". Ejemplo:

[
  {
    "pregunta": "\xbfQu\xe9 es X concepto?",
    "respuesta": "X concepto es..."
  },
  {
    "pregunta": "Enumera las caracter\xedsticas principales de Y",
    "respuesta": "Las caracter\xedsticas principales de Y son: 1)..., 2)..., 3)..."
  }
]

IMPORTANTE: Tu respuesta debe contener \xdaNICAMENTE el array JSON, sin texto adicional antes o despu\xe9s. No incluyas marcadores de c\xf3digo ni la palabra json antes del array.

VERIFICACI\xd3N FINAL OBLIGATORIA: Antes de enviar tu respuesta, cuenta que el array contenga exactamente {cantidad} flashcards. Si tienes menos, genera las que faltan. Si tienes m\xe1s, elimina las sobrantes.
`,i=`
Eres "Mentor Opositor AI", un preparador de \xe9lite especializado en la redacci\xf3n de temas para el segundo ejercicio de las oposiciones a los Cuerpos Superiores (A1 y A2) de la Administraci\xf3n P\xfablica Espa\xf1ola. Tu misi\xf3n es generar un desarrollo tem\xe1tico completo, exhaustivo y estructuralmente impecable, redactado al nivel de excelencia exigido por los tribunales de oposici\xf3n.

Tu resultado no debe ser un resumen, sino el **texto modelo completo que un opositor de alto rendimiento memorizar\xeda y desarrollar\xeda en su examen**. Tu funci\xf3n es de **expansi\xf3n, desarrollo y precisi\xf3n, no de s\xedntesis**.

**T\xcdTULO DEL TEMA A DESARROLLAR:**
{titulo_del_tema}

**INFORMACI\xd3N BASE (Documentaci\xf3n, legislaci\xf3n, apuntes):**
{documento}

**PETICI\xd3N ADICIONAL DEL OPOSITOR (si la hubiera):**
{instrucciones}

---

**INSTRUCCIONES DE REDACCI\xd3N OBLIGATORIAS:**

**1. ESTRUCTURA FORMAL Y L\xd3GICA (ANTI-REDUNDANCIA):**
   - **a) Jerarqu\xeda Numerada:** Organiza TODO el contenido en **ep\xedgrafes y sub-ep\xedgrafes numerados** (ej: 1., 1.1., 1.2., 2., 2.1., etc.).
   - **b) Prohibici\xf3n de Introducci\xf3n/Conclusi\xf3n:** El texto debe comenzar directamente en el ep\xedgrafe 1 y terminar en el \xfaltimo ep\xedgrafe, sin p\xe1rrafos introductorios o conclusivos generales.
   - **c) INTELIGENCIA ESTRUCTURAL ANTI-REDUNDANCIA (CR\xcdTICO):** Debes analizar la estructura del tema en su conjunto. **Est\xe1 terminantemente PROHIBIDO crear ep\xedgrafes que repitan o resuman informaci\xf3n ya desarrollada en ep\xedgrafes anteriores.** Si el documento base presenta un ep\xedgrafe de "Delimitaci\xf3n de Tipos Contractuales" despu\xe9s de haber explicado ya los "Tipos de Contratos", **DEBES unificar toda la informaci\xf3n en el primer ep\xedgrafe correspondiente (el de "Tipos de Contratos") y omitir por completo el ep\xedgrafe redundante.** Tu tarea es crear una estructura tem\xe1tica l\xf3gica y eficiente, no replicar ciegamente la del documento base si es repetitiva.

**2. PROFUNDIDAD, PRECISI\xd3N Y COMPLETITUD (ANTI-OMISIONES):**

   - **a) Desarrollo Exhaustivo:** Debes **recorrer el \xedndice o la estructura del documento base y desarrollar CADA UNO de los ep\xedgrafes y sub-ep\xedgrafes** que contiene, expandiendo la informaci\xf3n de forma detallada.
   - **b) Enriquecimiento Activo (Doctrina y Jurisprudencia):** Cuando el texto mencione un concepto clave (ej. "encargo a medio propio"), debes **enriquecerlo con su contexto doctrinal o jurisprudencial** (ej. "la doctrina *in-house* derivada de la **sentencia Teckal del TJUE**").
   - **c) Explicaci\xf3n de Consecuencias Pr\xe1cticas:** En lugar de solo enumerar requisitos, **explica sus implicaciones pr\xe1cticas** (ej. procedimiento de declaraci\xf3n de una prohibici\xf3n de contratar, consecuencias de ser un contrato SARA).
   - **d) Clarificaci\xf3n Proactiva de Ambig\xfcedades y Distinciones Conceptuales:**
     - **IMPRESCINDIBLE:** Cuando un concepto pueda dar lugar a confusi\xf3n, **debes OBLIGATORIAMENTE clarificar expl\xedcitamente las distinciones**.
     - **Ejemplo concreto:** Si al definir el "contrato de obras" se menciona que "puede incluir la redacci\xf3n del proyecto", debes a\xf1adir una aclaraci\xf3n expl\xedcita indicando que la "redacci\xf3n de proyecto" como objeto \xfanico constituye un "contrato de servicios" (Art. 17 LCSP), y solo se integra en el "contrato de obras" (Art. 13 LCSP) cuando la prestaci\xf3n principal es la ejecuci\xf3n de la obra.
   - **e) OBLIGACI\xd3N DE COMPLETITUD FACTUAL (CR\xcdTICO):** **No puedes omitir datos esenciales ni realizar simplificaciones que resten rigor.** Debes prestar especial atenci\xf3n a:
       - **Datos cuantitativos espec\xedficos:** Por ejemplo, al hablar de encargos a medios propios, es imprescindible mencionar el **l\xedmite del 50%** para la colaboraci\xf3n con empresarios particulares (Art. 30.1 LCSP).
       - **Matices en clasificaciones:** Al describir el \xe1mbito subjetivo, evita simplificaciones. Aclara que la pertenencia de una entidad (ej. una fundaci\xf3n p\xfablica) a una u otra categor\xeda depende de criterios espec\xedficos de control y financiaci\xf3n, y no es una adscripci\xf3n fija.

**3. ESTILO DE REDACCI\xd3N Y DATOS (FUSI\xd3N DE PROSA Y PRECISI\xd3N):**

   - **a) Rigor Jur\xeddico-Administrativo:** **Cita expl\xedcitamente la normativa clave** (CE, Leyes, art\xedculos). Utiliza la **terminolog\xeda t\xe9cnica precisa**.
   - **b) FUSI\xd3N DE PROSA Y PRECISI\xd3N CUANTITATIVA (CR\xcdTICO):** Tu redacci\xf3n debe ser narrativa, pero la prosa **DEBE INTEGRAR, NUNCA REEMPLAZAR, los datos cuantitativos clave.** La omisi\xf3n de umbrales, porcentajes o plazos se considerar\xe1 un fallo grave.
       - **Ejemplo de aplicaci\xf3n OBLIGATORIA (Contratos SARA):**
         > **Incorrecto (narrativo pero impreciso):** "Los contratos sujetos a regulaci\xf3n armonizada son aquellos que superan ciertos umbrales de alta cuant\xeda para obras, servicios o suministros."
         > **Correcto (narrativo Y preciso):** "Los contratos sujetos a regulaci\xf3n armonizada (SARA) son aquellos cuyo valor estimado, IVA excluido, iguala o supera los umbrales fijados por la normativa europea. Concretamente, seg\xfan el art\xedculo 21 de la LCSP, el umbral para contratos de obras y concesiones es de **5.538.000 euros**. Para los contratos de suministro y servicios, el umbral general es de **221.000 euros**, si bien se reduce a **143.000 euros** cuando el poder adjudicador es la Administraci\xf3n General del Estado, sus organismos aut\xf3nomos o las Entidades Gestoras de la Seguridad Social."
   - **c) Estilo de Redacci\xf3n: Prosa Desarrollada con Integraci\xf3n de Listas (M\xe9todo "P\xe1rrafo-Lista-P\xe1rrafo")**
     - **REGLA DE ORO:** La base de tu redacci\xf3n debe ser siempre la **prosa narrativa en p\xe1rrafos completos y bien conectados**.
     - **INTEGRACI\xd3N DE LISTAS:** Usa listas a), b), c)... exclusivamente para enumeraciones concretas (requisitos, funciones, etc.) y siempre deben estar "envueltas" en prosa (p\xe1rrafo introductorio y, si procede, p\xe1rrafo de conexi\xf3n posterior). **PROHIBIDO el uso de vi\xf1etas o guiones (•, -)**.

**4. FORMATO Y EXTENSI\xd3N:**
   - **Formato:** Utiliza **Markdown** para la jerarqu\xeda (## 1., ### 1.1., etc.) y las listas (a), b), c)...).
   - **Extensi\xf3n:** El objetivo de extensi\xf3n es de **3.200 a 3.800 palabras**. La prioridad es la **densidad, precisi\xf3n y calidad de la informaci\xf3n**, no alcanzar un n\xfamero de palabras mediante repeticiones o prosa superflua.

**MANDATO FINAL:**
Tu objetivo es crear el material de estudio definitivo. Procede a **redactar el tema de forma exhaustiva, fusionando una prosa narrativa de alto nivel con una precisi\xf3n cuantitativa y factual absoluta.** Presta especial atenci\xf3n a **no omitir datos clave y a no crear ep\xedgrafes redundantes**, unificando la informaci\xf3n de manera l\xf3gica y eficiente.
`,s=`
Eres "Mentor Editor AI", un editor de \xe9lite especializado en refinar temas para opositores a los Cuerpos Superiores (A1 y A2) de la Administraci\xf3n P\xfablica Espa\xf1ola. Tu misi\xf3n es tomar un borrador extenso y detallado, y convertirlo en un texto final pulido, conciso y redactado en prosa, manteniendo toda la informaci\xf3n esencial y su claridad estructural.

**BORRADOR INICIAL A EDITAR:**
{texto_largo_del_paso_1}

---

**INSTRUCCIONES DE EDICI\xd3N OBLIGATORIAS:**

**1. OBJETIVO PRINCIPAL: CONDENSAR MANTENIENDO LA CLARIDAD ESTRUCTURAL**
   - Tu tarea es reducir la longitud del texto eliminando **\xfanicamente la redundancia y la prosa superflua**, pero **preservando las estructuras que aporten claridad**, como las listas internas.

**2. REGLAS DE PRESERVACI\xd3N Y REFINAMIENTO (QU\xc9 NO PUEDES TOCAR O C\xd3MO TRATARLO):**
   - **a) Informaci\xf3n Clave Intacta:** NO puedes eliminar ning\xfan dato cuantitativo (porcentajes, plazos, cuant\xedas), ninguna cita de art\xedculo de ley, ni ninguna distinci\xf3n jur\xeddica conceptual. Toda la informaci\xf3n "dura" y los detalles espec\xedficos deben permanecer.
   - **b) Estructura Jer\xe1rquica Original:** Conserva la estructura numerada del texto (## 1., ### 1.1., etc.). No fusiones ep\xedgrafes principales.
   - **c) Preservaci\xf3n y Refinamiento de Listas:**
     - **OBLIGATORIO:** Si el borrador inicial contiene listas bien formadas (enumerando requisitos, funciones, etc.), **tu deber es preservarlas**.
     - **PROHIBIDO:** No elimines una lista para "aplanarla" y convertirla en un \xfanico p\xe1rrafo denso y dif\xedcil de leer.
     - **TU TAREA DE EDICI\xd3N:** Puedes y debes refinar estas listas. Por ejemplo, aseg\xfarate de que todos los puntos de la lista tengan una estructura gramatical paralela (p. ej., que todos empiecen por un verbo en infinitivo) y que su redacci\xf3n sea lo m\xe1s concisa posible sin perder informaci\xf3n.
     - **d) Prohibici\xf3n de Formato Gr\xe1fico:** Est\xe1 terminantemente prohibido a\xf1adir elementos gr\xe1ficos o visuales como separadores de l\xednea (--- o ───), asteriscos o cualquier otro adorno que no sea parte del texto est\xe1ndar. La separaci\xf3n entre ep\xedgrafes se debe realizar \xfanicamente mediante los encabezados Markdown y los saltos de l\xednea.

**3. T\xc9CNICAS DE EDICI\xd3N (C\xd3MO DEBES CONDENSAR LA PROSA):**
   - **a) Unificar Informaci\xf3n Repetida:** Si detectas que el mismo concepto o conjunto de datos se explica en diferentes secciones del borrador, tu trabajo es **unificar esa informaci\xf3n en una \xfanica explicaci\xf3n coherente y completa** dentro del ep\xedgrafe m\xe1s apropiado, y eliminar las menciones repetitivas de las otras secciones.
   - **b) Reescritura para la Concisi\xf3n:** En lugar de simplemente borrar frases, **reescribe los p\xe1rrafos para que sean m\xe1s directos y densos**. Transforma frases largas y complejas en construcciones m\xe1s claras y precisas sin perder el significado.
   - **c) Eliminar "Relleno":** Suprime frases introductorias gen\xe9ricas, muletillas y p\xe1rrafos conclusivos intermedios o finales que no aporten informaci\xf3n nueva. El texto debe fluir directamente de una idea sustantiva a la siguiente.

**4. REQUISITO DE ESTILO FINAL:**
   - El texto editado debe leerse como un tema de desarrollo de alta calidad, no como un resumen. Debe mantener un tono acad\xe9mico, una redacci\xf3n formal y una estructura clara, conectando las ideas con frases de transici\xf3n cuando sea necesario para la coherencia l\xf3gica, tanto en la prosa como en la introducci\xf3n a las listas.

**5. OBJETIVO DE EXTENSI\xd3N:**
   - Reduce la longitud total del borrador para que se ajuste a un rango de **3.200 a 3.800 palabras**. Este objetivo debe lograrse exclusivamente mediante las t\xe9cnicas de edici\xf3n descritas, no mediante la omisi\xf3n de datos o la eliminaci\xf3n de listas.

**MANDATO FINAL:**
Procede a editar el borrador proporcionado. Transf\xf3rmalo de un texto extenso y potencialmente repetitivo en un tema final, pulido, denso en informaci\xf3n y estructuralmente claro, listo para ser estudiado por un opositor de \xe9lite. Aseg\xfarate de preservar y refinar las listas como elementos clave para la legibilidad.
`,o=`
Eres "Mentor Opositor AI", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un mapa mental basado en el contenido proporcionado. Este mapa mental ser\xe1 utilizado por un estudiante para visualizar la estructura y las relaciones entre los conceptos.

CONTEXTO DEL TEMARIO (Informaci\xf3n base para tu mapa mental):
{documentos}

PETICI\xd3N DEL USUARIO (Tema principal y estructura deseada del mapa mental):
Genera un mapa mental sobre el tema proporcionado.
{instrucciones}

INSTRUCCIONES EXTREMADAMENTE DETALLADAS PARA EL C\xd3DIGO D3.JS:

**A. ESTRUCTURA DEL ARCHIVO Y CONFIGURACI\xd3N B\xc1SICA:**
1.  **HTML Completo:** Genera un solo archivo \`<!DOCTYPE html>...</html>\`.
2.  **CSS Integrado:** Todo el CSS debe estar dentro de etiquetas \`<style>\` en el \`<head>\`.
3.  **JavaScript Integrado:** Todo el JavaScript debe estar dentro de una etiqueta \`<script>\` antes de cerrar \`</body>\`.
4.  **D3.js CDN:** Carga D3.js v7 (o la m\xe1s reciente v7.x) desde su CDN oficial: \`https://d3js.org/d3.v7.min.js\`.
5.  **SVG y Body:**
    *   \`body { margin: 0; overflow: hidden; font-family: sans-serif; background-color: #f0f2f5; }\`.
    *   El \`<svg>\` debe ocupar toda la ventana: \`width: 100vw; height: 100vh;\`.
    *   A\xf1ade un grupo principal \`<g class="main-group">\` dentro del SVG para aplicar transformaciones de zoom/pan.
    *   **NUEVO:** Define una duraci\xf3n para las transiciones: \`const duration = 750;\`.

**B. ESTRUCTURA DE DATOS PARA D3.JS:**
1.  **Jerarqu\xeda JSON:** Extrae los conceptos del CONTEXTO y organ\xedzalos en una estructura jer\xe1rquica JSON.
2.  **Propiedades del Nodo de Datos:** Cada objeto en tu estructura de datos DEBE tener:
    *   \`name\`: (string) El texto a mostrar en el nodo.
    *   \`id\`: (string) Un identificador \xdaNICO y ESTABLE para este nodo (e.g., "concepto-raiz", "hijo1-concepto-raiz").
    *   \`children\`: (array, opcional) Un array de objetos nodo hijos.
    *   **NUEVO:** \`_children\`: (array, opcional, inicialmente null o undefined) Se usar\xe1 para guardar los hijos cuando un nodo est\xe9 colapsado.
3.  **Jerarqu\xeda D3:** Usa \`let root = d3.hierarchy(datosJSON);\`.
4.  **ESTADO INICIAL DE EXPANSI\xd3N (RA\xcdZ Y PRIMER NIVEL EXPANDIDOS):**
     *   Despu\xe9s de crear la jerarqu\xeda D3 con d3.hierarchy(datosJSON) (como se indica en el punto B.3), debes colapsar inmediatamente todos los nodos que tengan una profundidad (d.depth) mayor que 1.
     *   Esto asegura que el nodo ra\xedz (d.depth === 0) y sus hijos directos (d.depth === 1) permanezcan expandidos (es decir, sus datos de children no se mueven a _children).
     *   Todos los nodos nietos de la ra\xedz y cualquier nodo en niveles m\xe1s profundos deben comenzar colapsados (sus children se mueven a _children y children se establece en null).
     *   **Implementa esto utilizando el siguiente fragmento de c\xf3digo. Coloca este fragmento INMEDIATAMENTE DESPU\xc9S de la l\xednea donde defines let root = d3.hierarchy(datosJSON); y ANTES de cualquier llamada a la funci\xf3n update(root) o de la configuraci\xf3n de root.x0 y root.y0:**
       \`root.each(d => { 
           if (d.depth > 1) { // Solo colapsar nodos m\xe1s all\xe1 del primer nivel de hijos
               if (d.children) { 
                   d._children = d.children; 
                   d.children = null; 
               } 
           } else if (d.children && d.depth <= 1) { // Asegurar que la ra\xedz y el primer nivel no tengan _children si tienen children
               d._children = null; 
           }
       });\`
     *   **Nota Importante para la IA:** Al hacer clic en un nodo para expandirlo (en la funci\xf3n handleClick), si tiene _children, estos se mover\xe1n a children y _children se pondr\xe1 a null. Si se colapsa, children se mueve a _children y children se pone a null.
**C. LAYOUT DEL \xc1RBOL (D3.JS TREE):**
1.  **Tipo de Layout:** Usa \`d3.tree()\`.
2.  **Espaciado de Nodos (\`nodeSize\`):**
    *   \`const nodeVerticalSeparation = 80;\`.
    *   \`const nodeHorizontalSeparation = 250;\`.
    *   \`const treeLayout = d3.tree().nodeSize([nodeVerticalSeparation, nodeHorizontalSeparation]);\`.
3.  **Posici\xf3n Inicial:** Guarda la posici\xf3n inicial de la ra\xedz con validaci\xf3n:
    \`const viewportHeight = window.innerHeight || 600;
     const viewportWidth = window.innerWidth || 800;
     root.x0 = isNaN(viewportHeight / 2) ? 300 : viewportHeight / 2;
     root.y0 = 0;\` (Ajusta y0 si la ra\xedz no empieza en el borde).

// ******** INICIO FUNCI\xd3N TEXT WRAPPING ********
function wrapText(textElement, width, lineHeight) {
    let totalComputedHeight = 0;
    textElement.each(function() { 
        const textNode = d3.select(this);
        const words = textNode.text().split(/s+/).reverse();
        let word;
        let line = []; // Declared 'line' here
        textNode.text(null); 

        let tspan = textNode.append("tspan")
            .attr("x", 0) 
            .attr("dy", lineHeight + "px"); 
            .attr("dy", "0.8em");
        let numLines = 1;

        while (word = words.pop()) {
            line.push(word);
            tspan.text(line.join(" "));
            if (tspan.node().getComputedTextLength() > width && line.length > 1) {
                line.pop();
                tspan.text(line.join(" "));
                line = [word]; // Reset line for the new tspan
                tspan = textNode.append("tspan")
                    .attr("x", 0)
                    .attr("dy", lineHeight + "px") 
                    .text(word);
                numLines++;
            }
        }
        totalComputedHeight = numLines * lineHeight;
    });
    return totalComputedHeight; 
}
// ******** FIN FUNCI\xd3N TEXT WRAPPING ********

**D. FUNCI\xd3N \`update(sourceNode)\` (VITAL PARA INTERACTIVIDAD):**
   Esta funci\xf3n ser\xe1 la responsable de renderizar/actualizar el \xe1rbol cada vez que se expanda/colapse un nodo.
   \`sourceNode\` es el nodo que fue clickeado.

1.  **Calcular Nuevo Layout:**
    *   \`const treeData = treeLayout(root);\`.
    *   \`const nodes = treeData.descendants();\`.
    *   \`const links = treeData.links();\`.
    *   **Orientaci\xf3n (Ajustar Coordenadas):** Aseg\xfarate de que despu\xe9s del layout, los nodos se posicionen horizontalmente. \`nodes.forEach(d => { d.y = d.depth * nodeHorizontalSeparation; });\` (Si \`nodeSize\` no lo hace directamente, o si quieres controlar la separaci\xf3n de niveles manualmente).
    *   **VALIDACI\xd3N CR\xcdTICA:** Aseg\xfarate de que todas las coordenadas sean n\xfameros v\xe1lidos:
        \`nodes.forEach(d => {
          d.x = isNaN(d.x) ? 0 : d.x;
          d.y = isNaN(d.y) ? d.depth * nodeHorizontalSeparation : d.y;
          d.x0 = d.x0 || d.x;
          d.y0 = d.y0 || d.y;
        });\`

2.  **NODOS:**
    * // **OBJETIVOS ADICIONALES PARA NODOS:**
        *INDICADOR DE EXPANSI\xd3N:** Los nodos con hijos (visibles u ocultos en _children) deben mostrar un peque\xf1o c\xedrculo a su derecha. Este c\xedrculo contendr\xe1 un texto "+" si el nodo est\xe1 colapsado y tiene _children, o "-" si est\xe1 expandido y tiene children. El c\xedrculo debe ser visible solo si hay hijos/_children. El color del c\xedrculo puede cambiar para reflejar el estado (ej. naranja para colapsado, azul para expandido).
        *CENTRADO VERTICAL DEL TEXTO:** El texto de varias l\xedneas dentro de cada nodo rectangular debe estar lo m\xe1s centrado verticalmente posible. Ajusta el atributo 'y' del elemento <text> y/o el 'dy' del primer <tspan> en la funci\xf3n wrapText para lograrlo, considerando la computedTextHeight y lineHeight.
        *ANCHO DE NODO DIN\xc1MICO:** El ancho de los rect\xe1ngulos de los nodos (d.rectWidth) debe ajustarse al ancho real del texto envuelto (con un m\xednimo y m\xe1ximo), en lugar de usar siempre maxNodeTextWidth. La funci\xf3n wrapText debe ayudar a determinar este ancho real.
    *   **NOTA IMPORTANTE SOBRE EL COLOR DE NODOS:** El color de fondo (fill) de los rect\xe1ngulos de los nodos se definir\xe1 exclusivamente a trav\xe9s de las clases CSS .node.depth-X especificadas en la Secci\xf3n I. Por lo tanto, NO se debe establecer un estilo fill en l\xednea en JavaScript para los elementos rect (ni en nodeEnter ni en nodeUpdate). El JavaScript solo se encarga de la estructura y los atributos como width, height, stroke, pero el fill principal vendr\xe1 del CSS.
    *   Selecci\xf3n: \`const node = g.selectAll("g.node").data(nodes, d => d.data.id);\`.
    *   **Nodos Entrantes (\`nodeEnter\`):**
        *   Crea el grupo principal del nodo:
            \`const nodeEnter = node.enter().append("g")
                .attr("class", d => "node depth-" + d.depth) // A\xf1ade clase de profundidad
                .attr("transform", d => \`translate(\${sourceNode.y0 || 0},\${sourceNode.x0 || 0})\`) // Posici\xf3n inicial validada
                .on("click", handleClick);\`
        *   **C\xe1lculo de Dimensiones, Text Wrapping y Creaci\xf3n de Elementos Internos (Rect y Text):**
            \`nodeEnter.each(function(d) {
                const nodeGroup = d3.select(this);
                const horizontalPadding = 12;
                const verticalPadding = 8;
                const maxNodeTextWidth = 150;
                const lineHeight = 12; // Asumiendo font-size 12px, so 1.2em = 12px

                d.rectWidth = maxNodeTextWidth + 2 * horizontalPadding;

                // A\xf1ade el rect\xe1ngulo primero (visualmente detr\xe1s del texto)
                const rectElement = nodeGroup.append("rect")
                    .attr("rx", "3")
                    .attr("ry", "3")
                    .style("stroke-width", "1px")
                    .style("stroke", "#777");

                // A\xf1ade el elemento de texto
                const textElement = nodeGroup.append("text")
                    .attr("text-anchor", "middle")
                    .style("font-size", "10px") 
                    .style("fill", "#333")
                    .text(d.data.name); // Nombre del nodo

                // Aplica text wrapping y obt\xe9n la altura calculada del texto
                const computedTextHeight = wrapText(textElement, maxNodeTextWidth, lineHeight);
                
                // Calcula la altura final del rect\xe1ngulo
                d.rectHeight = Math.max(computedTextHeight + 2 * verticalPadding, 30); // Altura m\xednima de 30px

                // Ajusta la posici\xf3n Y del elemento de texto para centrarlo verticalmente
                // El primer tspan dentro de wrapText tendr\xe1 un dy de 'lineHeight'
                // por lo que el 'y' del textElement debe ser la parte superior del \xe1rea de texto.
                const textBlockYOffset = -d.rectHeight / 2 + verticalPadding;
                textElement.attr("y", textBlockYOffset);

                // Ahora establece las dimensiones y posici\xf3n del rect\xe1ngulo
                rectElement
                    .attr("width", d.rectWidth)
                    .attr("height", d.rectHeight)
                    .attr("x", -d.rectWidth / 2)
                    .attr("y", -d.rectHeight / 2);
            });\`

             \`nodeEnter.each(function(d) {
                if (d.children || d._children) {
                    const nodeGroup = d3.select(this);
                    const indicatorGroup = nodeGroup.append("g")
                        .attr("class", "exp-indicator");

                    // Posiciona el grupo del indicador en el borde derecho y centrado verticalmente.
                    indicatorGroup.attr("transform", \`translate(\${d.rectWidth / 2}, 0)\`);

                    indicatorGroup.append("circle")
                        .attr("r", 8);

                    indicatorGroup.append("text")
                        .attr("text-anchor", "middle")
                        .attr("dy", "0.3em")
                        .text(d.children ? "-" : "+");
                }
            });\`
    *   **Nodos Actualizados (\`nodeUpdate\`):**
        *   **VALIDACI\xd3N DE COORDENADAS:** \`const validX = isNaN(d.x) ? 0 : d.x; const validY = isNaN(d.y) ? 0 : d.y;\`
        *   Transici\xf3n a la nueva posici\xf3n: \`node.merge(nodeEnter).transition().duration(duration).attr("transform", d => \`translate(\${isNaN(d.y) ? 0 : d.y},\${isNaN(d.x) ? 0 : d.x})\`);\`.
     *   **Actualizar atributos del rect\xe1ngulo (CR\xcdTICO: seleccionar el 'rect' correctamente):**
            \`node.merge(nodeEnter).each(function(d) {
                const currentRect = d3.select(this).select("rect"); // Selecciona el rect dentro del grupo 'this'
                if (currentRect.node()) { // Asegura que el rect exista
                    currentRect.transition().duration(duration) // A\xf1adir transici\xf3n tambi\xe9n al cambio de color
                    // Si necesitaras actualizar width/height aqu\xed, tambi\xe9n deber\xedan transicionar:
                     currentRect.transition().duration(duration)
                        .attr("width", d.rectWidth)
                        .attr("height", d.rectHeight);
                } else {
                    console.warn("Rect\xe1ngulo no encontrado para actualizar en nodo:", d.data.name);
                }
            });\` 
            
            \`node.merge(nodeEnter).each(function(d) {
                const indicator = d3.select(this).select(".exp-indicator");
                if (!indicator.empty()) {
                    indicator.select("text").text(d.children ? "-" : "+");
                    indicator.select("circle")
                        .style("fill", d.children ? "#a1d99b" : "#fcae91");
                }
            });\`

            *   **Nodos Salientes (\`nodeExit\`):**
        *   **VALIDACI\xd3N DE POSICI\xd3N FINAL:** \`const finalX = isNaN(sourceNode.x) ? 0 : sourceNode.x; const finalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;\`
        *   Transici\xf3n a la posici\xf3n del nodo padre: \`nodeExit.transition().duration(duration).attr("transform", \`translate(\${finalY},\${finalX})\`).remove();\`.
        *   Reduce la opacidad del rect\xe1ngulo y texto a 0.

3.  **ENLACES:**
    *   Selecci\xf3n: \`const link = g.selectAll("path.link").data(links, d => d.target.data.id);\`.
    *   **Enlaces Entrantes (\`linkEnter\`):**
        *   A\xf1ade \`<path class="link">\`.
        *   **VALIDACI\xd3N DE POSICI\xd3N INICIAL:**
            \`const sourceInitialX = isNaN(sourceNode.x0) ? 0 : sourceNode.x0;
             const sourceInitialY = isNaN(sourceNode.y0) ? 0 : sourceNode.y0;
             const sourceInitialWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\`
        *   Posici\xf3n inicial desde el padre: \`linkEnter.insert("path", "g").attr("class", "link").attr("d", d => { const o = {x: sourceInitialX, y: sourceInitialY, rectWidth: sourceInitialWidth }; return diagonal({source: o, target: o}); }).style("fill", "none").style("stroke", "#ccc").style("stroke-width", "1.5px");\`
    *   **Enlaces Actualizados (\`linkUpdate\`):**
        *   Transici\xf3n a la nueva posici\xf3n: \`link.merge(linkEnter).transition().duration(duration).attr("d", diagonal);\`.
    *   **Enlaces Salientes (\`linkExit\`):**
        *   **VALIDACI\xd3N DE POSICI\xd3N FINAL:**
            \`const sourceFinalX = isNaN(sourceNode.x) ? 0 : sourceNode.x;
             const sourceFinalY = isNaN(sourceNode.y) ? 0 : sourceNode.y;
             const sourceFinalWidth = isNaN(sourceNode.rectWidth) ? 20 : (sourceNode.rectWidth || 20);\`
        *   Transici\xf3n a la posici\xf3n del padre y remove: \`linkExit.transition().duration(duration).attr("d", d => { const o = {x: sourceFinalX, y: sourceFinalY, rectWidth: sourceFinalWidth }; return diagonal({source: o, target: o}); }).remove();\`.

4.  **Guardar Posiciones Antiguas:**
    *   Al final de \`update\`: \`nodes.forEach(d => { d.x0 = d.x; d.y0 = d.y; });\`.

**E. FUNCI\xd3N \`diagonal(linkObject)\` (PARA DIBUJAR ENLACES A BORDES DE RECT\xc1NGULOS):**
   Debe generar un path string para el atributo \`d\` del path.
   \`\`\`javascript
   function diagonal({ source, target }) {
     // source y target son nodos con propiedades x, y, rectWidth
     // VALIDACI\xd3N CR\xcdTICA: Asegurar que todos los valores sean n\xfameros v\xe1lidos
     const sourceX = isNaN(source.x) ? 0 : source.x;
     const sourceY = isNaN(source.y) ? 0 : source.y;
     const targetX = isNaN(target.x) ? 0 : target.x;
     const targetY = isNaN(target.y) ? 0 : target.y;
     const sourceWidth = isNaN(source.rectWidth) ? 20 : (source.rectWidth || 20);
     const targetWidth = isNaN(target.rectWidth) ? 20 : (target.rectWidth || 20);

     const sx = sourceY + sourceWidth / 2;
     const sy = sourceX;
     const tx = targetY - targetWidth / 2;
     const ty = targetX;

     // Validar que los puntos calculados sean n\xfameros v\xe1lidos
     const validSx = isNaN(sx) ? 0 : sx;
     const validSy = isNaN(sy) ? 0 : sy;
     const validTx = isNaN(tx) ? 0 : tx;
     const validTy = isNaN(ty) ? 0 : ty;

     // Path curvado simple
     return \`M \${validSx} \${validSy}
             C \${(validSx + validTx) / 2} \${validSy},
               \${(validSx + validTx) / 2} \${validTy},
               \${validTx} \${validTy}\`;
   }
   \`\`\`

**F. FUNCI\xd3N \`handleClick(event, d)\` (MANEJADOR DE CLIC EN NODO):**
   \`\`\`javascript
   function handleClick(event, d) {
     if (d.children) { // Si est\xe1 expandido, colapsar
       d._children = d.children;
       d.children = null;
     } else if (d._children) { // Si est\xe1 colapsado y tiene hijos ocultos, expandir
       d.children = d._children;
       d._children = null;
     }
     // Si es un nodo hoja (sin d.children ni d._children), no hacer nada o una acci\xf3n espec\xedfica.
     // Para este caso, solo expandir/colapsar.
     update(d); // Llama a update con el nodo clickeado como 'sourceNode'
   }
   \`\`\`

**G. VISUALIZACI\xd3N INICIAL Y ZOOM/PAN:**
1.  Llama a \`update(root);\` para el primer renderizado.
2.  **C\xe1lculo de Extensiones y Escala Inicial (Adaptar del prompt anterior):**
    *   NECESITAS calcular las dimensiones del \xe1rbol DESPU\xc9S de que el layout inicial (\`update(root)\`) haya asignado \`rectWidth\` y \`rectHeight\` a los nodos visibles.
    *   Obt\xe9n minX, maxX, minYActual, maxYActual de los nodos en root.descendants() que no est\xe9n colapsados (o de todos para un c\xe1lculo m\xe1s simple que puede ser ajustado por el zoom).
    *   Considera el \`rectWidth/2\` y \`rectHeight/2\` para los bordes.
3.  **Traslaci\xf3n y Escala:**
    *   Calcula \`initialScale\`, \`initialTranslateX\`, \`initialTranslateY\` como en el prompt anterior, pero usando el \`<g class="main-group">\` para el zoom.
    *   \`const zoom = d3.zoom().scaleExtent([0.1, 3]).on("zoom", (event) => mainGroup.attr("transform", event.transform));\`
    *   \`svg.call(zoom);\`.
    *   \`svg.call(zoom.transform, d3.zoomIdentity.translate(initialTranslateX, initialTranslateY).scale(initialScale));\`.

**H. MANEJO DE REDIMENSIONAMIENTO DE VENTANA (Como en el prompt anterior):**
    *   Reajusta el SVG y recalcula la transformaci\xf3n de zoom/pan para centrar.

**I. ESTILO CSS:**
   \`\`\`css
   .node text { font: 10px sans-serif; pointer-events: none; }
   .link { fill: none; stroke: #ccc; stroke-width: 1.5px; }
   .node rect { cursor: pointer; }
   .node rect:hover { stroke-opacity: 1; stroke-width: 2px; }
   /* Colores por profundidad (opcional) */
   .node.depth-0 rect { fill: #d1e5f0; stroke: #67a9cf; }
   .node.depth-1 rect { fill: #fddbc7; stroke: #ef8a62; }
   .node.depth-2 rect { fill: #e0f3f8; stroke: #92c5de; }
   .node.depth-3 rect { fill: #f7f7f7; stroke: #bababa; }
   .node.depth-4 rect, .node.depth-5 rect { fill: #e2e3e5; stroke: #bababa; }
   \`\`\`
   Aseg\xfarate de a\xf1adir la clase de profundidad al grupo del nodo:
   \`nodeEnter.attr("class", d => "node depth-" + d.depth)\`
* --- Estilos para el Indicador de Expansi\xf3n --- *
    .exp-indicator circle {
       stroke: #555;
       stroke-width: 1px;
       cursor: pointer;
       transition: fill 0.3s;
   }
   .exp-indicator text {
       font-size: 14px;
       font-weight: bold;
       fill: #1C1C1C; /* Texto oscuro para ser legible sobre colores claros */
       pointer-events: none; /* El clic se captura en el grupo del nodo principal */
   }
   \`\`\`

**J. REVISI\xd3N FINAL ANTES DE GENERAR (PARA LA IA):**
*   \xbfLa variable zoom (que contiene d3.zoom()) se define e inicializa ANTES de que cualquier funci\xf3n (como centerAndFitView o la l\xf3gica de transformaci\xf3n inicial) intente usarla para llamar a zoom.transform?
*   \xbfSe usa una funci\xf3n \`update(sourceNode)\` para manejar todas las actualizaciones del DOM? S\xcd.
*   \xbfLa funci\xf3n \`handleClick\` alterna entre \`d.children\` y \`d._children\` y luego llama a \`update(d)\`? S\xcd.
*   \xbfLos nodos y enlaces entrantes aparecen desde la posici\xf3n del padre (\`sourceNode\`)? S\xcd.
*   \xbfLos nodos y enlaces salientes se mueven hacia la posici\xf3n del padre antes de eliminarse? S\xcd.
*   \xbfSe usan transiciones D3 con una \`duration\` constante? S\xcd.
*   \xbfSe almacenan y usan \`x0\`, \`y0\` para las posiciones iniciales/finales de las transiciones? S\xcd.
*   \xbfLa funci\xf3n \`diagonal\` calcula correctamente los puntos de inicio/fin en los bordes de los rect\xe1ngulos? S\xcd.
*   \xbfEl c\xe1lculo din\xe1mico de \`rectWidth\` y \`rectHeight\` se realiza para cada nodo al entrar? S\xcd.

**RESTRICCIONES IMPORTANTES:**
-   Tu respuesta DEBE SER \xdaNICAMENTE el c\xf3digo HTML completo. Sin explicaciones, comentarios introductorios o finales fuera del c\xf3digo.
-   Sigue las instrucciones de D3.js al pie de la letra, especialmente el patr\xf3n Enter-Update-Exit dentro de la funci\xf3n \`update\`.
-   **CR\xcdTICO:** SIEMPRE valida que las coordenadas y dimensiones sean n\xfameros v\xe1lidos usando \`isNaN()\` antes de usarlas en transformaciones SVG. Esto evita errores como \`translate(NaN,NaN)\` o \`scale(NaN)\`.
-   **CR\xcdTICO:** Usa valores por defecto seguros (como 0, 20, 300) cuando los c\xe1lculos resulten en NaN o undefined.

`,l=`
Eres "QuantumTest Engine", un sistema de IA que sigue algoritmos precisos para generar bancos de preguntas en formato JSON. Tu operaci\xf3n se basa en un mandato y un proceso de ejecuci\xf3n fijos.

---
**MANDATO PRINCIPAL**
---
**TAREA:** Generar un array JSON que contenga **EXACTAMENTE {cantidad}** objetos de pregunta.
**CONDICI\xd3N DE \xc9XITO:** El n\xfamero de objetos en el array de salida debe ser igual a {cantidad}. Cualquier otra cantidad es un fallo total.

---
**MATERIALES DE ENTRADA**
---
-   **Material Fuente {documentos}:**
    {documentos}
-   **Instrucciones de Enfoque ({instrucciones}):**
    {instrucciones}

---
**PROCESO DE RAZONAMIENTO Y EJECUCI\xd3N (Chain of Thought Obligatorio)**
---
Debes seguir este proceso mental de forma estricta y literal antes de generar la respuesta final:

1.  **Confirmaci\xf3n del Objetivo:** "Mi objetivo es generar un array con exactamente {cantidad} elementos. Este es el requisito m\xe1s importante."
2.  **Planificaci\xf3n de la Creaci\xf3n:** "Voy a crear las preguntas de una en una, llevando la cuenta mentalmente: 'Generando pregunta 1 de {cantidad}', 'Generando pregunta 2 de {cantidad}', y as\xed sucesivamente hasta llegar a {cantidad}."
3.  **Directiva de Prioridad:** "Si creo que el material fuente es limitado, mi directiva es priorizar la cantidad sobre la variedad. Generar\xe9 preguntas m\xe1s simples o directas si es necesario para cumplir la cuota de {cantidad}."
4.  **Verificaci\xf3n Final del Plan:** "Una vez generadas todas las preguntas, realizar\xe9 una \xfaltima cuenta para confirmar que el array contiene {cantidad} objetos antes de producir la salida final."

Ahora, ejecuta este proceso y genera el resultado en el formato JSON especificado.

---
**ESPECIFICACIONES DEL FORMATO DE SALIDA (JSON)**
---
1.  **Tipo de Salida:** Un \xfanico array JSON v\xe1lido, empezando con [ y terminando con ]. Sin texto adicional.
2.  Cada objeto de pregunta en el array JSON resultante debe tener EXACTAMENTE estas propiedades DIRECTAS (respeta los nombres exactos):

    "pregunta": (string) El texto de la pregunta.

    "opcion_a": (string) El texto para la opci\xf3n A. IMPORTANTE: usa "opcion_a" con gui\xf3n bajo.

    "opcion_b": (string) El texto para la opci\xf3n B. IMPORTANTE: usa "opcion_b" con gui\xf3n bajo.

    "opcion_c": (string) El texto para la opci\xf3n C. IMPORTANTE: usa "opcion_c" con gui\xf3n bajo.

    "opcion_d": (string) El texto para la opci\xf3n D. IMPORTANTE: usa "opcion_d" con gui\xf3n bajo.

    "respuesta_correcta": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cu\xe1l de las opciones es la correcta.

    NO anides las opciones dentro de otro objeto llamado "opciones". Deben ser propiedades directas del objeto de la pregunta.3.  **Contenido:** Basado 100% en el Material Fuente y enfocado seg\xfan las instrucciones.
Ejecuta el mandato.
`,c=`Eres "Mentor Opositor AI", un preparador de oposiciones virtual excepcionalmente experimentado, organizado, emp\xe1tico y con una metodolog\xeda de estudio probada. Tu misi\xf3n es crear una propuesta de plan de estudio inicial, altamente personalizada y realista para el opositor, bas\xe1ndote en la informaci\xf3n que te proporcionar\xe1 y los principios de una preparaci\xf3n de oposiciones de \xe9lite.

**Informaci\xf3n Clave Recopilada del Opositor:**

{informacionUsuario}

**Principios Fundamentales para la Creaci\xf3n de tu Propuesta de Plan de Estudio:**

Debes internalizar y aplicar los siguientes principios como si fueras un preparador humano experto:

1.  **Organizaci\xf3n y Realismo Absoluto:**
    *   Calcula el tiempo total disponible hasta el examen, considerando la disponibilidad diaria REAL del opositor.
    *   Compara este tiempo con la suma de las estimaciones de horas por tema (ajustadas por dificultad/importancia).
    *   **Si el tiempo es claramente insuficiente para cubrir todo el temario de forma realista, ind\xedcalo con honestidad al inicio de tu propuesta de plan**, sugiriendo posibles enfoques (priorizar, extender el plazo si es posible, etc.). No crees un plan imposible.
    *   Distribuye el temario de forma l\xf3gica a lo largo del tiempo, dejando m\xe1rgenes para imprevistos.

2.  **Metodolog\xeda Probada y Adaptable (Tu Enfoque):**
    *   **An\xe1lisis de Temas:** Como experto preparador, analiza cada tema del temario bas\xe1ndote en:
        *   **T\xedtulo y contenido del tema:** Identifica la complejidad y densidad del material
        *   **Posici\xf3n en el temario:** Los primeros temas suelen ser fundamentales, los finales pueden ser m\xe1s espec\xedficos
        *   **Palabras clave:** T\xe9rminos como "constitucional", "procedimiento", "r\xe9gimen jur\xeddico" indican complejidad
        *   **Extensi\xf3n aparente:** Temas con descripciones largas o m\xfaltiples apartados requieren m\xe1s tiempo
    *   **Estimaci\xf3n Inteligente de Tiempo:** Asigna autom\xe1ticamente horas de estudio seg\xfan tu an\xe1lisis:
        *   **Temas fundamentales/constitucionales:** 8-12 horas (alta importancia)
        *   **Temas procedimentales complejos:** 6-10 horas (dificultad media-alta)
        *   **Temas espec\xedficos/aplicados:** 4-6 horas (importancia media)
        *   **Temas introductorios/generales:** 2-4 horas (dificultad baja)
    *   **Clasificaci\xf3n:** Determina las caracter\xedsticas de cada tema:
        *   **Muy Importante:** Temas constitucionales, fundamentales del \xe1rea de estudio, bases legales principales
        *   **Dif\xedcil:** Temas con procedimientos complejos, m\xfaltiples apartados, conceptos abstractos
        *   **Ya Dominado:** Ninguno (asume que el usuario necesita estudiar todo el temario)
    *   **Orden de Estudio Inteligente:** Sugiere empezar por los temas que has identificado como "fundamentales" y "muy importantes". Alterna temas densos con otros m\xe1s ligeros para mantener la motivaci\xf3n.
    *   **Bloques Tem\xe1ticos:** Si identificas temas muy relacionados entre s\xed en el \xedndice, considera agruparlos en bloques de estudio.
    *   **Repasos Sistem\xe1ticos:**
        *   **Post-Tema:** Incluye un breve repaso al finalizar cada tema.
        *   **Peri\xf3dicos:** Integra repasos acumulativos (ej. semanales o quincenales, seg\xfan la frecuenciaRepasoDeseada si el usuario la especific\xf3, o tu recomendaci\xf3n experta). Una buena regla general es dedicar ~30% del tiempo total de estudio a repasos.
        *   **Fase Final de Repaso:** Reserva un periodo significativo antes del examen (ej. las \xfaltimas 3-6 semanas, dependiendo del tiempo total) EXCLUSIVAMENTE para repasos generales, simulacros y consolidaci\xf3n. No se debe introducir material nuevo aqu\xed.
    *   **Metas Claras:** Define metas semanales y/o mensuales (ej. "Semana 1: Completar Tema 1 y Tema 2 (hasta apartado X). Realizar 20 flashcards de Tema 1.").

3.  **Integraci\xf3n Estrat\xe9gica de OposiAI:**
    *   Important\xedsimo: **Al iniciar un Tema Nuevo:** La **primera tarea a realizar** debe ser "Generaci\xf3n de Flashcards y Test con OposiAI". Esta tarea instruir\xe1 al opositor a crear un conjunto inicial de flashcards y una bater\xeda de preguntas de test relevantes para el tema que est\xe1 a punto de empezar a estudiar. (Duraci\xf3n estimada: 10-20 minutos).
    *   **Tras la primera sesi\xf3n de estudio de un apartado:** Despu\xe9s de una sesi\xf3n profunda de estudio de un apartado o sub-tema importante (no necesariamente el tema completo), se debe incluir una tarea para "Generaci\xf3n de Mapas Mentales con OposiAI". Se indicar\xe1 al opositor que use OposiAI para estructurar visualmente los puntos clave y relaciones del contenido reci\xe9n estudiado para una mejor comprensi\xf3n. (Duraci\xf3n estimada: 20-30 minutos, dependiendo de la densidad del apartado).
    *   **Estudio Diario de Flashcards (a partir del D\xeda 2):** A partir del D\xeda 2 del plan de estudios, y de forma DIARIA, se reservar\xe1 un bloque de tiempo fijo para "Revisi\xf3n y Adici\xf3n de Flashcards con OposiAI". Esta tarea instruir\xe1 al opositor a dedicar este tiempo a repasar las flashcards programadas por el algoritmo de repetici\xf3n espaciada de OposiAI y a a\xf1adir nuevas flashcards de los temas recientes. (Duraci\xf3n: 30-60 minutos).
    *   **Realizaci\xf3n Oportuna de Tests:** Cuando un tema haya sido completado o tras un bloque de varios temas, o como parte de los repasos peri\xf3dicos, asigna una tarea para "Realizaci\xf3n de Tests con OposiAI". Estos tests ser\xe1n los generados previamente por OposiAI para los temas correspondientes. El objetivo es evaluar la retenci\xf3n y comprensi\xf3n.
    *   **Repasos Basados en Tests (con cantidad de preguntas):** Para CUALQUIER tipo de repaso (semanal, quincenal, acumulativo, o fase final), la actividad principal ser\xe1 "Repaso Intensivo con Tests de OposiAI". Adem\xe1s de indicar la realizaci\xf3n del test, **ESPECIFICAR\xc1S el n\xfamero de preguntas a generar por OposiAI para cada tema que se repasa**, asignando una mayor cantidad de preguntas a los temas clasificados como 'Muy Importante' o 'Dif\xedcil' en tu an\xe1lisis, y una menor cantidad a temas m\xe1s ligeros. Ejemplo de descripci\xf3n para la tarea: "Repaso Tema 1 (15 preguntas) y Tema 2 (10 preguntas) con Tests OposiAI."

4.  **Experiencia y Conocimiento (Tu Rol):**
    *   Al presentar el plan, a\xf1ade breves comentarios estrat\xe9gicos basados en tu an\xe1lisis autom\xe1tico, como: "Dado que el Tema X es fundamental seg\xfan mi an\xe1lisis del temario, le hemos asignado m\xe1s tiempo y lo abordaremos pronto para tener margen de repaso".
    *   Usa tu "experiencia" como preparador experto para identificar autom\xe1ticamente temas que suelen ser cruciales en oposiciones similares, bas\xe1ndote en el contexto del temario y las palabras clave que identifiques.

5.  **Flexibilidad (Impl\xedcita en la Propuesta):**
    *   Aunque generes un plan estructurado, en tu introducci\xf3n al plan puedes mencionar que es una "propuesta inicial" y que se podr\xe1 ajustar seg\xfan el progreso real.

**Formato de Salida de la Propuesta del Plan:**

Genera una respuesta en **formato JSON estructurado** que permita crear un plan interactivo.

DURACI\xd3N DEL PLAN: El plan debe cubrir COMPLETAMENTE el per\xedodo desde la fecha actual hasta la fecha del examen. NO te limites a 5-10 semanas.

La estructura JSON debe incluir:
- introduccion: Texto de introducci\xf3n y evaluaci\xf3n de viabilidad
- resumen: Objeto con tiempoTotalEstudio, numeroTemas, duracionEstudioNuevo, duracionRepasoFinal
- semanas: Array de objetos semana con numero, fechaInicio, fechaFin, objetivoPrincipal y dias
- Cada dia debe tener: dia, horas, y tareas (array de objetos con titulo, descripcion, tipo, duracionEstimada)
- estrategiaRepasos: Explicaci\xf3n de la estrategia de repasos
- proximosPasos: Consejos y pr\xf3ximos pasos

**IMPORTANTE:**
1.  Devuelve \xdaNICAMENTE el JSON v\xe1lido, sin texto adicional antes o despu\xe9s.
2.  El array "semanas" debe contener TODAS las semanas desde hoy hasta el examen.
3.  Calcula correctamente las fechas de cada semana usando el formato YYYY-MM-DD.
4.  **CR\xcdTICO - NO USES COMENTARIOS:** El JSON debe ser v\xe1lido sin comentarios (//) ni texto explicativo dentro.
6.  **ESTRUCTURA COMPLETA:** Si hay muchas semanas, genera TODAS sin usar "..." o comentarios explicativos.

**Consideraciones para la IA al Generar la Respuesta:**

-   **Lenguaje:** Emp\xe1tico, profesional, claro y motivador.
-   **Personalizaci\xf3n:** Usa la informaci\xf3n del usuario para que el plan se sienta realmente adaptado a \xe9l/ella.
-   **Realismo:** Evita sobrecargar las semanas. Es mejor un progreso constante que picos de trabajo insostenibles.
-   **Accionable:** El plan debe darle al usuario una idea clara de qu\xe9 hacer cada semana/d\xeda.
-   **DURACI\xd3N DEL PLAN:** El plan debe cubrir COMPLETAMENTE el per\xedodo desde la fecha actual hasta la fecha del examen. NO te limites a 5-10 semanas.

Genera el plan de estudios personalizado bas\xe1ndote en toda esta informaci\xf3n.`},89459:(e,t,r)=>{var n,a,i,s,o,l,c,u,d,p,f,h,m,g,y,b,v,O,x,w,_,j,P,S,E,A,N,T,I,D,k,C,R,$,L,M,U,F,q,z,Z,B,W,V,J,X,H,G,Y,K,Q,ee,et,er,en,ea,ei,es,eo,el,ec,eu,ed,ep,ef,eh,em,eg,ey,eb;let ev,eO,ex;function ew(e,t,r,n,a){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!a:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?a.call(e,r):a?a.value=r:t.set(e,r),r}function e_(e,t,r,n){if("a"===r&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?n:"a"===r?n.call(e):n?n.value:t.get(e)}r.d(t,{y5:()=>aK,Jo:()=>aY});let ej=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return ej=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),r=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^r()&15>>e/4).toString(16))};function eP(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eS=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eE extends Error{}class eA extends eE{constructor(e,t,r,n){super(`${eA.makeMessage(e,t,r)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,r){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,r,n){if(!e||!n)return new eT({message:r,cause:eS(t)});let a=t?.error;return 400===e?new eD(e,a,r,n):401===e?new ek(e,a,r,n):403===e?new eC(e,a,r,n):404===e?new eR(e,a,r,n):409===e?new e$(e,a,r,n):422===e?new eL(e,a,r,n):429===e?new eM(e,a,r,n):e>=500?new eU(e,a,r,n):new eA(e,a,r,n)}}class eN extends eA{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eT extends eA{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eI extends eT{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eD extends eA{}class ek extends eA{}class eC extends eA{}class eR extends eA{}class e$ extends eA{}class eL extends eA{}class eM extends eA{}class eU extends eA{}class eF extends eE{constructor(){super("Could not parse response content as the length limit was reached")}}class eq extends eE{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let ez=/^[a-z][a-z0-9+.-]*:/i,eZ=e=>ez.test(e);function eB(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let eW=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eE(`${e} must be an integer`);if(t<0)throw new eE(`${e} must be a positive integer`);return t},eV=e=>{try{return JSON.parse(e)}catch(e){return}},eJ=e=>new Promise(t=>setTimeout(t,e));function eX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}let eH={off:0,error:200,warn:300,info:400,debug:500},eG=(e,t,r)=>{if(e){if(Object.prototype.hasOwnProperty.call(eH,e))return e;e1(r).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eH))}`)}};function eY(){}function eK(e,t,r){return!t||eH[e]>eH[r]?eY:t[e].bind(t)}let eQ={error:eY,warn:eY,info:eY,debug:eY},e0=new WeakMap;function e1(e){let t=e.logger,r=e.logLevel??"off";if(!t)return eQ;let n=e0.get(t);if(n&&n[0]===r)return n[1];let a={error:eK("error",t,r),warn:eK("warn",t,r),info:eK("info",t,r),debug:eK("debug",t,r)};return e0.set(t,[r,a]),a}let e3=e=>(e.options&&(e.options=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eX(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e.options),delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),e2="5.1.1",e9=()=>!1,e4=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":e6(Deno.build.os),"X-Stainless-Arch":e5(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":e6(globalThis.process.platform??"unknown"),"X-Stainless-Arch":e5(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,n=r[2]||0,a=r[3]||0;return{browser:e,version:`${t}.${n}.${a}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},e5=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",e6=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",e8=()=>ev??(ev=e4());function e7(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function te(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return e7({start(){},async pull(e){let{done:r,value:n}=await t.next();r?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function tt(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tr(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),r=t.cancel();t.releaseLock(),await r}let tn=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),ta="RFC3986",ti={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},ts=(Object.prototype.hasOwnProperty,Array.isArray),to=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function tl(e,t){if(ts(e)){let r=[];for(let n=0;n<e.length;n+=1)r.push(t(e[n]));return r}return t(e)}let tc=Object.prototype.hasOwnProperty,tu={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},td=Array.isArray,tp=Array.prototype.push,tf=function(e,t){tp.apply(e,td(t)?t:[t])},th=Date.prototype.toISOString,tm={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,r,n,a)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let s="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,r=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===a&&(40===n||41===n)){r[r.length]=t.charAt(e);continue}if(n<128){r[r.length]=to[n];continue}if(n<2048){r[r.length]=to[192|n>>6]+to[128|63&n];continue}if(n<55296||n>=57344){r[r.length]=to[224|n>>12]+to[128|n>>6&63]+to[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),r[r.length]=to[240|n>>18]+to[128|n>>12&63]+to[128|n>>6&63]+to[128|63&n]}s+=r.join("")}return s},encodeValuesOnly:!1,format:ta,formatter:ti[ta],indices:!1,serializeDate:e=>th.call(e),skipNulls:!1,strictNullHandling:!1},tg={};function ty(e){let t;return(eO??(eO=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function tb(e){let t;return(ex??(ex=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class tv{constructor(){n.set(this,void 0),a.set(this,void 0),ew(this,n,new Uint8Array,"f"),ew(this,a,null,"f")}decode(e){let t;if(null==e)return[];let r=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?ty(e):e;ew(this,n,function(e){let t=0;for(let r of e)t+=r.length;let r=new Uint8Array(t),n=0;for(let t of e)r.set(t,n),n+=t.length;return r}([e_(this,n,"f"),r]),"f");let i=[];for(;null!=(t=function(e,t){for(let r=t??0;r<e.length;r++){if(10===e[r])return{preceding:r,index:r+1,carriage:!1};if(13===e[r])return{preceding:r,index:r+1,carriage:!0}}return null}(e_(this,n,"f"),e_(this,a,"f")));){if(t.carriage&&null==e_(this,a,"f")){ew(this,a,t.index,"f");continue}if(null!=e_(this,a,"f")&&(t.index!==e_(this,a,"f")+1||t.carriage)){i.push(tb(e_(this,n,"f").subarray(0,e_(this,a,"f")-1))),ew(this,n,e_(this,n,"f").subarray(e_(this,a,"f")),"f"),ew(this,a,null,"f");continue}let e=null!==e_(this,a,"f")?t.preceding-1:t.preceding,r=tb(e_(this,n,"f").subarray(0,e));i.push(r),ew(this,n,e_(this,n,"f").subarray(t.index),"f"),ew(this,a,null,"f")}return i}flush(){return e_(this,n,"f").length?this.decode("\n"):[]}}n=new WeakMap,a=new WeakMap,tv.NEWLINE_CHARS=new Set(["\n","\r"]),tv.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class tO{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1;async function*n(){if(r)throw new eE("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let n=!1;try{for await(let r of tx(e,t))if(!n){if(r.data.startsWith("[DONE]")){n=!0;continue}if(null===r.event||r.event.startsWith("response.")||r.event.startsWith("transcript.")){let t;try{t=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if(t&&t.error)throw new eA(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(r.data)}catch(e){throw console.error("Could not parse message into JSON:",r.data),console.error("From chunk:",r.raw),e}if("error"==r.event)throw new eA(void 0,e.error,e.message,void 0);yield{event:r.event,data:e}}}n=!0}catch(e){if(eP(e))return;throw e}finally{n||t.abort()}}return new tO(n,t)}static fromReadableStream(e,t){let r=!1;async function*n(){let t=new tv;for await(let r of tt(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new tO(async function*(){if(r)throw new eE("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eP(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=r.next();e.push(n),t.push(n)}return n.shift()}});return[new tO(()=>n(e),this.controller),new tO(()=>n(t),this.controller)]}toReadableStream(){let e,t=this;return e7({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:r,done:n}=await e.next();if(n)return t.close();let a=ty(JSON.stringify(r)+"\n");t.enqueue(a)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tx(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eE("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eE("Attempted to iterate over a response with no body")}let r=new t_,n=new tv;for await(let t of tw(tt(e.body)))for(let e of n.decode(t)){let t=r.decode(e);t&&(yield t)}for(let e of n.flush()){let t=r.decode(e);t&&(yield t)}}async function*tw(e){let t=new Uint8Array;for await(let r of e){let e;if(null==r)continue;let n=r instanceof ArrayBuffer?new Uint8Array(r):"string"==typeof r?ty(r):r,a=new Uint8Array(t.length+n.length);for(a.set(t),a.set(n,t.length),t=a;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class t_{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,n]=function(e,t){let r=e.indexOf(":");return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}async function tj(e,t){let{response:r,requestLogID:n,retryOfRequestLogID:a,startTime:i}=t,s=await (async()=>{if(t.options.stream)return(e1(e).debug("response",r.status,r.url,r.headers,r.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(r,t.controller):tO.fromSSEResponse(r,t.controller);if(204===r.status)return null;if(t.options.__binaryResponse)return r;let n=r.headers.get("content-type"),a=n?.split(";")[0]?.trim();return a?.includes("application/json")||a?.endsWith("+json")?tP(await r.json(),r):await r.text()})();return e1(e).debug(`[${n}] response parsed`,e3({retryOfRequestLogID:a,url:r.url,status:r.status,body:s,durationMs:Date.now()-i})),s}function tP(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tS extends Promise{constructor(e,t,r=tj){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=r,i.set(this,void 0),ew(this,i,e,"f")}_thenUnwrap(e){return new tS(e_(this,i,"f"),this.responsePromise,async(t,r)=>tP(e(await this.parseResponse(t,r),r),r.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(e_(this,i,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}function tE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tE(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}i=new WeakMap;class tN{constructor(e,t,r,n){s.set(this,void 0),ew(this,s,e,"f"),this.options=n,this.response=t,this.body=r}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eE("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await e_(this,s,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(s=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tT extends tS{constructor(e,t,r){super(e,t,async(e,t)=>new r(e,t.response,await tj(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tI extends tN{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.object=r.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tD extends tN{constructor(e,t,r,n){super(e,t,r,n),this.data=r.data||[],this.has_more=r.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),r=t[t.length-1]?.id;return r?tA(tA({},this.options),{},{query:tA(tA({},"object"!=typeof(e=this.options.query)?{}:e??{}),{},{after:r})}):null}}function tk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tk(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let tR=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function t$(e,t,r){return tR(),new File(e,t??"unknown_file",r)}function tL(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tM=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tU=async(e,t)=>tC(tC({},e),{},{body:await tq(e.body,t)}),tF=new WeakMap,tq=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,r=tF.get(t);if(r)return r;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,r=new FormData;if(r.toString()===await new e(r).text())return!1;return!0}catch{return!0}})();return tF.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let r=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tW(r,e,t))),r},tz=e=>e instanceof Blob&&"name"in e,tZ=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tM(e)||tz(e)),tB=e=>{if(tZ(e))return!0;if(Array.isArray(e))return e.some(tB);if(e&&"object"==typeof e){for(let t in e)if(tB(e[t]))return!0}return!1},tW=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(r instanceof Response)e.append(t,t$([await r.blob()],tL(r)));else if(tM(r))e.append(t,t$([await new Response(te(r)).blob()],tL(r)));else if(tz(r))e.append(t,r,tL(r));else if(Array.isArray(r))await Promise.all(r.map(r=>tW(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,n])=>tW(e,`${t}[${r}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}};function tV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tV(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let tX=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tH=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tX(e),tG=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tY(e,t,r){if(tR(),tH(e=await e))return e instanceof File?e:t$([await e.arrayBuffer()],e.name);if(tG(e)){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),t$(await tK(n),t,r)}let n=await tK(e);if(t||(t=tL(e)),!r?.type){let e=n.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(r=tJ(tJ({},r),{},{type:e}))}return t$(n,t,r)}async function tK(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tX(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tM(e))for await(let r of e)t.push(...await tK(r));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tQ{constructor(e){this._client=e}}function t0(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let t1=((e=t0)=>function(t,...r){let n;if(1===t.length)return t[0];let a=!1,i=t.reduce((t,n,i)=>(/[?#]/.test(n)&&(a=!0),t+n+(i===r.length?"":(a?encodeURIComponent:e)(String(r[i])))),""),s=i.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(s));)o.push({start:n.index,length:n[0].length});if(o.length>0){let e=0,t=o.reduce((t,r)=>{let n=" ".repeat(r.start-e),a="^".repeat(r.length);return e=r.start+r.length,t+n+a},"");throw new eE(`Path parameters result in path with invalid segments:
${i}
${t}`)}return i})(t0);function t3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}class t2 extends tQ{list(e,t={},r){return this._client.getAPIList(t1`/chat/completions/${e}/messages`,tD,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?t3(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({query:t},r))}}let t9=e=>e?.role==="assistant",t4=e=>e?.role==="tool";class t5{constructor(){o.add(this),this.controller=new AbortController,l.set(this,void 0),c.set(this,()=>{}),u.set(this,()=>{}),d.set(this,void 0),p.set(this,()=>{}),f.set(this,()=>{}),h.set(this,{}),m.set(this,!1),g.set(this,!1),y.set(this,!1),b.set(this,!1),ew(this,l,new Promise((e,t)=>{ew(this,c,e,"f"),ew(this,u,t,"f")}),"f"),ew(this,d,new Promise((e,t)=>{ew(this,p,e,"f"),ew(this,f,t,"f")}),"f"),e_(this,l,"f").catch(()=>{}),e_(this,d,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},e_(this,o,"m",v).bind(this))},0)}_connected(){this.ended||(e_(this,c,"f").call(this),this._emit("connect"))}get ended(){return e_(this,m,"f")}get errored(){return e_(this,g,"f")}get aborted(){return e_(this,y,"f")}abort(){this.controller.abort()}on(e,t){return(e_(this,h,"f")[e]||(e_(this,h,"f")[e]=[])).push({listener:t}),this}off(e,t){let r=e_(this,h,"f")[e];if(!r)return this;let n=r.findIndex(e=>e.listener===t);return n>=0&&r.splice(n,1),this}once(e,t){return(e_(this,h,"f")[e]||(e_(this,h,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,r)=>{ew(this,b,!0,"f"),"error"!==e&&this.once("error",r),this.once(e,t)})}async done(){ew(this,b,!0,"f"),await e_(this,d,"f")}_emit(e,...t){if(e_(this,m,"f"))return;"end"===e&&(ew(this,m,!0,"f"),e_(this,p,"f").call(this));let r=e_(this,h,"f")[e];if(r&&(e_(this,h,"f")[e]=r.filter(e=>!e.once),r.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];e_(this,b,"f")||r?.length||Promise.reject(e),e_(this,u,"f").call(this,e),e_(this,f,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];e_(this,b,"f")||r?.length||Promise.reject(e),e_(this,u,"f").call(this,e),e_(this,f,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function t6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function t8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?t6(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function t7(e){return e?.$brand==="auto-parseable-response-format"}function re(e){return e?.$brand==="auto-parseable-tool"}function rt(e,t){let r=e.choices.map(e=>{var r,n;if("length"===e.finish_reason)throw new eF;if("content_filter"===e.finish_reason)throw new eq;return t8(t8({},e),{},{message:t8(t8(t8({},e.message),e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let r=e.tools?.find(e=>e.function?.name===t.function.name);return t8(t8({},t),{},{function:t8(t8({},t.function),{},{parsed_arguments:re(r)?r.$parseRaw(t.function.arguments):r?.function.strict?JSON.parse(t.function.arguments):null})})})(t,e))??void 0}:void 0),{},{parsed:e.message.content&&!e.message.refusal?(r=t,n=e.message.content,r.response_format?.type!=="json_schema"?null:r.response_format?.type==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(n):JSON.parse(n):null):null})})});return t8(t8({},e),{},{choices:r})}function rr(e){return!!t7(e.response_format)||(e.tools?.some(e=>re(e)||"function"===e.type&&!0===e.function.strict)??!1)}l=new WeakMap,c=new WeakMap,u=new WeakMap,d=new WeakMap,p=new WeakMap,f=new WeakMap,h=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,b=new WeakMap,o=new WeakSet,v=function(e){if(ew(this,g,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new eN),e instanceof eN)return ew(this,y,!0,"f"),this._emit("abort",e);if(e instanceof eE)return this._emit("error",e);if(e instanceof Error){let t=new eE(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eE(String(e)))};let rn=["tool_choice","stream"];function ra(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ri(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ra(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ra(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rs extends t5{constructor(){super(...arguments),O.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),t4(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(t9(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eE("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),e_(this,O,"m",x).call(this)}async finalMessage(){return await this.done(),e_(this,O,"m",w).call(this)}async finalFunctionToolCall(){return await this.done(),e_(this,O,"m",_).call(this)}async finalFunctionToolCallResult(){return await this.done(),e_(this,O,"m",j).call(this)}async totalUsage(){return await this.done(),e_(this,O,"m",P).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=e_(this,O,"m",w).call(this);t&&this._emit("finalMessage",t);let r=e_(this,O,"m",x).call(this);r&&this._emit("finalContent",r);let n=e_(this,O,"m",_).call(this);n&&this._emit("finalFunctionToolCall",n);let a=e_(this,O,"m",j).call(this);null!=a&&this._emit("finalFunctionToolCallResult",a),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",e_(this,O,"m",P).call(this))}async _createChatCompletion(e,t,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),e_(this,O,"m",S).call(this,t);let a=await e.chat.completions.create(ri(ri({},t),{},{stream:!1}),ri(ri({},r),{},{signal:this.controller.signal}));return this._connected(),this._addChatCompletion(rt(a,t))}async _runChatCompletion(e,t,r){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,r)}async _runTools(e,t,r){let n="tool",{tool_choice:a="auto",stream:i}=t,s=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(t,rn),o="string"!=typeof a&&a?.function?.name,{maxChatCompletions:l=10}=r||{},c=t.tools.map(e=>{if(re(e)){if(!e.$callback)throw new eE("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let d="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,ri(ri({},s),{},{tool_choice:a,tools:d,messages:[...this.messages]}),r),i=t.choices[0]?.message;if(!i)throw new eE("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let r=e.id,{name:a,arguments:i}=e.function,s=u[a];if(s){if(o&&o!==a){let e=`Invalid tool_call: ${JSON.stringify(a)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:r,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(a)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:r,content:e});continue}try{t="function"==typeof s.parse?await s.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:r,content:e});continue}let l=await s.function(t,this),c=e_(this,O,"m",E).call(this,l);if(this._addMessage({role:n,tool_call_id:r,content:c}),o)return}}}}function ro(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rl(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ro(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ro(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}O=new WeakSet,x=function(){return e_(this,O,"m",w).call(this).content??null},w=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(t9(t))return ri(ri({},t),{},{content:t.content??null,refusal:t.refusal??null})}throw new eE("stream ended without producing a ChatCompletionMessage with role=assistant")},_=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t9(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},j=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(t4(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},P=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},S=function(e){if(null!=e.n&&e.n>1)throw new eE("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},E=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class rc extends rs{static runTools(e,t,r){let n=new rc,a=rl(rl({},r),{},{headers:rl(rl({},r?.headers),{},{"X-Stainless-Helper-Method":"runTools"})});return n._run(()=>n._runTools(e,t,a)),n}_addMessage(e,t=!0){super._addMessage(e,t),t9(e)&&e.content&&this._emit("content",e.content)}}let ru={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class rd extends Error{}class rp extends Error{}let rf=(e,t)=>{let r=e.length,n=0,a=e=>{throw new rd(`${e} at position ${n}`)},i=e=>{throw new rp(`${e} at position ${n}`)},s=()=>(d(),n>=r&&a("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||ru.NULL&t&&r-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||ru.BOOL&t&&r-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||ru.BOOL&t&&r-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||ru.INFINITY&t&&r-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||ru.MINUS_INFINITY&t&&1<r-n&&r-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||ru.NAN&t&&r-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let s=n,o=!1;for(n++;n<r&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(s,++n-Number(o)))}catch(e){i(String(e))}else if(ru.STR&t)try{return JSON.parse(e.substring(s,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(s,e.lastIndexOf("\\"))+'"')}a("Unterminated string literal")},l=()=>{n++,d();let i={};try{for(;"}"!==e[n];){if(d(),n>=r&&ru.OBJ&t)return i;let a=o();d(),n++;try{let e=s();Object.defineProperty(i,a,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(ru.OBJ&t)return i;throw e}d(),","===e[n]&&n++}}catch(e){if(ru.OBJ&t)return i;a("Expected '}' at end of object")}return n++,i},c=()=>{n++;let r=[];try{for(;"]"!==e[n];)r.push(s()),d(),","===e[n]&&n++}catch(e){if(ru.ARR&t)return r;a("Expected ']' at end of array")}return n++,r},u=()=>{if(0===n){"-"===e&&ru.NUM&t&&a("Not sure what '-' is");try{return JSON.parse(e)}catch(r){if(ru.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(r))}}let s=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=r||ru.NUM&t||a("Unterminated number literal");try{return JSON.parse(e.substring(s,n))}catch(r){"-"===e.substring(s,n)&&ru.NUM&t&&a("Not sure what '-' is");try{return JSON.parse(e.substring(s,e.lastIndexOf("e")))}catch(e){i(String(e))}}},d=()=>{for(;n<r&&" \n\r	".includes(e[n]);)n++};return s()},rh=e=>(function(e,t=ru.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return rf(e.trim(),t)})(e,ru.ALL^ru.NUM),rm=["choices"],rg=["delta","finish_reason","index","logprobs"],ry=["content","refusal"],rb=["content","refusal","function_call","role","tool_calls"],rv=["index","id","type","function"],rO=["id","choices","created","model","system_fingerprint"],rx=["message","finish_reason","index","logprobs"],rw=["content","function_call","tool_calls"],r_=["function","type","id"],rj=["arguments","name"];function rP(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function rS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rE(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rS(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rA extends rs{constructor(e){super(),A.add(this),N.set(this,void 0),T.set(this,void 0),I.set(this,void 0),ew(this,N,e,"f"),ew(this,T,[],"f")}get currentChatCompletionSnapshot(){return e_(this,I,"f")}static fromReadableStream(e){let t=new rA(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,r){let n=new rA(t);return n._run(()=>n._runChatCompletion(e,rE(rE({},t),{},{stream:!0}),rE(rE({},r),{},{headers:rE(rE({},r?.headers),{},{"X-Stainless-Helper-Method":"stream"})}))),n}async _createChatCompletion(e,t,r){super._createChatCompletion;let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),e_(this,A,"m",D).call(this);let a=await e.chat.completions.create(rE(rE({},t),{},{stream:!0}),rE(rE({},r),{},{signal:this.controller.signal}));for await(let e of(this._connected(),a))e_(this,A,"m",C).call(this,e);if(a.controller.signal?.aborted)throw new eN;return this._addChatCompletion(e_(this,A,"m",L).call(this))}async _fromReadableStream(e,t){let r,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),e_(this,A,"m",D).call(this),this._connected();let a=tO.fromReadableStream(e,this.controller);for await(let e of a)r&&r!==e.id&&this._addChatCompletion(e_(this,A,"m",L).call(this)),e_(this,A,"m",C).call(this,e),r=e.id;if(a.controller.signal?.aborted)throw new eN;return this._addChatCompletion(e_(this,A,"m",L).call(this))}[(N=new WeakMap,T=new WeakMap,I=new WeakMap,A=new WeakSet,D=function(){this.ended||ew(this,I,void 0,"f")},k=function(e){let t=e_(this,T,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},e_(this,T,"f")[e.index]=t),t},C=function(e){if(this.ended)return;let t=e_(this,A,"m",U).call(this,e);for(let r of(this._emit("chunk",e,t),e.choices)){let e=t.choices[r.index];null!=r.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",r.delta.content,e.message.content),this._emit("content.delta",{delta:r.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=r.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:r.delta.refusal,snapshot:e.message.refusal}),r.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:r.logprobs?.content,snapshot:e.logprobs?.content??[]}),r.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:r.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=e_(this,A,"m",k).call(this,e);for(let t of(e.finish_reason&&(e_(this,A,"m",$).call(this,e),null!=n.current_tool_call_index&&e_(this,A,"m",R).call(this,e,n.current_tool_call_index)),r.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(e_(this,A,"m",$).call(this,e),null!=n.current_tool_call_index&&e_(this,A,"m",R).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of r.delta.tool_calls??[]){let r=e.message.tool_calls?.[t.index];r?.type&&(r?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:r.function?.name,index:t.index,arguments:r.function.arguments,parsed_arguments:r.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):r?.type)}}},R=function(e,t){if(e_(this,A,"m",k).call(this,e).done_tool_calls.has(t))return;let r=e.message.tool_calls?.[t];if(!r)throw Error("no tool call snapshot");if(!r.type)throw Error("tool call snapshot missing `type`");if("function"===r.type){let e=e_(this,N,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===r.function.name);this._emit("tool_calls.function.arguments.done",{name:r.function.name,index:t,arguments:r.function.arguments,parsed_arguments:re(e)?e.$parseRaw(r.function.arguments):e?.function.strict?JSON.parse(r.function.arguments):null})}else r.type},$=function(e){let t=e_(this,A,"m",k).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let r=e_(this,A,"m",M).call(this);this._emit("content.done",{content:e.message.content,parsed:r?r.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},L=function(){if(this.ended)throw new eE("stream has ended, this shouldn't happen");let e=e_(this,I,"f");if(!e)throw new eE("request ended without sending any chunks");return ew(this,I,void 0,"f"),ew(this,T,[],"f"),function(e,t){var r;let{id:n,choices:a,created:i,model:s,system_fingerprint:o}=e;return r=rE(rE({},rP(e,rO)),{},{id:n,choices:a.map(t=>{let{message:r,finish_reason:n,index:a,logprobs:i}=t,s=rP(t,rx);if(!n)throw new eE(`missing finish_reason for choice ${a}`);let{content:o=null,function_call:l,tool_calls:c}=r,u=rP(r,rw),d=r.role;if(!d)throw new eE(`missing role for choice ${a}`);if(l){let{arguments:e,name:t}=l;if(null==e)throw new eE(`missing function_call.arguments for choice ${a}`);if(!t)throw new eE(`missing function_call.name for choice ${a}`);return rE(rE({},s),{},{message:{content:o,function_call:{arguments:e,name:t},role:d,refusal:r.refusal??null},finish_reason:n,index:a,logprobs:i})}return c?rE(rE({},s),{},{index:a,finish_reason:n,logprobs:i,message:rE(rE({},u),{},{role:d,content:o,refusal:r.refusal??null,tool_calls:c.map((t,r)=>{let{function:n,type:i,id:s}=t,o=rP(t,r_),l=n||{},{arguments:c,name:u}=l,d=rP(l,rj);if(null==s)throw new eE(`missing choices[${a}].tool_calls[${r}].id
${rN(e)}`);if(null==i)throw new eE(`missing choices[${a}].tool_calls[${r}].type
${rN(e)}`);if(null==u)throw new eE(`missing choices[${a}].tool_calls[${r}].function.name
${rN(e)}`);if(null==c)throw new eE(`missing choices[${a}].tool_calls[${r}].function.arguments
${rN(e)}`);return rE(rE({},o),{},{id:s,type:i,function:rE(rE({},d),{},{name:u,arguments:c})})})})}):rE(rE({},s),{},{message:rE(rE({},u),{},{content:o,role:d,refusal:r.refusal??null}),finish_reason:n,index:a,logprobs:i})}),created:i,model:s,object:"chat.completion"},o?{system_fingerprint:o}:{}),t&&rr(t)?rt(r,t):t8(t8({},r),{},{choices:r.choices.map(e=>t8(t8({},e),{},{message:t8(t8({},e.message),{},{parsed:null},e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0)}))})}(e,e_(this,N,"f"))},M=function(){let e=e_(this,N,"f")?.response_format;return t7(e)?e:null},U=function(e){var t,r,n,a;let i=e_(this,I,"f"),{choices:s}=e,o=rP(e,rm);for(let s of(i?Object.assign(i,o):i=ew(this,I,rE(rE({},o),{},{choices:[]}),"f"),e.choices)){let{delta:e,finish_reason:o,index:l,logprobs:c=null}=s,u=rP(s,rg),d=i.choices[l];if(d||(d=i.choices[l]=rE({finish_reason:o,index:l,message:{},logprobs:c},u)),c)if(d.logprobs){let{content:e,refusal:n}=c,a=rP(c,ry);Object.assign(d.logprobs,a),e&&((t=d.logprobs).content??(t.content=[]),d.logprobs.content.push(...e)),n&&((r=d.logprobs).refusal??(r.refusal=[]),d.logprobs.refusal.push(...n))}else d.logprobs=Object.assign({},c);if(o&&(d.finish_reason=o,e_(this,N,"f")&&rr(e_(this,N,"f")))){if("length"===o)throw new eF;if("content_filter"===o)throw new eq}if(Object.assign(d,u),!e)continue;let{content:p,refusal:f,function_call:h,role:m,tool_calls:g}=e,y=rP(e,rb);if(Object.assign(d.message,y),f&&(d.message.refusal=(d.message.refusal||"")+f),m&&(d.message.role=m),h&&(d.message.function_call?(h.name&&(d.message.function_call.name=h.name),h.arguments&&((n=d.message.function_call).arguments??(n.arguments=""),d.message.function_call.arguments+=h.arguments)):d.message.function_call=h),p&&(d.message.content=(d.message.content||"")+p,!d.message.refusal&&e_(this,A,"m",M).call(this)&&(d.message.parsed=rh(d.message.content))),g)for(let e of(d.message.tool_calls||(d.message.tool_calls=[]),g)){let{index:t,id:r,type:n,function:i}=e,s=rP(e,rv),o=(a=d.message.tool_calls)[t]??(a[t]={});Object.assign(o,s),r&&(o.id=r),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let r=e.tools?.find(e=>e.function?.name===t.function.name);return re(r)||r?.function.strict||!1}(e_(this,N,"f"),o)&&(o.function.parsed_arguments=rh(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("chunk",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tO(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function rN(e){return JSON.stringify(e)}function rT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rT(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rD extends rA{static fromReadableStream(e){let t=new rD(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,r){let n=new rD(t),a=rI(rI({},r),{},{headers:rI(rI({},r?.headers),{},{"X-Stainless-Helper-Method":"runTools"})});return n._run(()=>n._runTools(e,t,a)),n}}function rk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rk(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rR extends tQ{constructor(){super(...arguments),this.messages=new t2(this._client)}create(e,t){return this._client.post("/chat/completions",rC(rC({body:e},t),{},{stream:e.stream??!1}))}retrieve(e,t){return this._client.get(t1`/chat/completions/${e}`,t)}update(e,t,r){return this._client.post(t1`/chat/completions/${e}`,rC({body:t},r))}list(e={},t){return this._client.getAPIList("/chat/completions",tD,rC({query:e},t))}delete(e,t){return this._client.delete(t1`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eE(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eE(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,rC(rC({},t),{},{headers:rC(rC({},t?.headers),{},{"X-Stainless-Helper-Method":"chat.completions.parse"})}))._thenUnwrap(t=>rt(t,e))}runTools(e,t){return e.stream?rD.runTools(this._client,e,t):rc.runTools(this._client,e,t)}stream(e,t){return rA.createChatCompletion(this._client,e,t)}}rR.Messages=t2;class r$ extends tQ{constructor(){super(...arguments),this.completions=new rR(this._client)}}r$.Completions=rR;let rL=Symbol("brand.privateNullableHeaders"),rM=Array.isArray,rU=e=>{let t=new Headers,r=new Set;for(let n of e){let e=new Set;for(let[a,i]of function*(e){let t;if(!e)return;if(rL in e){let{values:t,nulls:r}=e;for(let e of(yield*t.entries(),r))yield[e,null];return}let r=!1;for(let n of(e instanceof Headers?t=e.entries():rM(e)?t=e:(r=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=rM(n[1])?n[1]:[n[1]],a=!1;for(let n of t)void 0!==n&&(r&&!a&&(a=!0,yield[e,null]),yield[e,n])}}(n)){let n=a.toLowerCase();e.has(n)||(t.delete(a),e.add(n)),null===i?(t.delete(a),r.add(n)):(t.append(a,i),r.delete(n))}}return{[rL]:!0,values:t,nulls:r}};function rF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rF(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rz extends tQ{create(e,t){return this._client.post("/audio/speech",rq(rq({body:e},t),{},{headers:rU([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0}))}}function rZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rZ(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rW extends tQ{create(e,t){return this._client.post("/audio/transcriptions",tU(rB(rB({body:e},t),{},{stream:e.stream??!1,__metadata:{model:e.model}}),this._client))}}function rV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rV(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class rX extends tQ{create(e,t){return this._client.post("/audio/translations",tU(rJ(rJ({body:e},t),{},{__metadata:{model:e.model}}),this._client))}}class rH extends tQ{constructor(){super(...arguments),this.transcriptions=new rW(this._client),this.translations=new rX(this._client),this.speech=new rz(this._client)}}function rG(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rG(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rG(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}rH.Transcriptions=rW,rH.Translations=rX,rH.Speech=rz;class rK extends tQ{create(e,t){return this._client.post("/batches",rY({body:e},t))}retrieve(e,t){return this._client.get(t1`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tD,rY({query:e},t))}cancel(e,t){return this._client.post(t1`/batches/${e}/cancel`,t)}}function rQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rQ(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class r1 extends tQ{create(e,t){return this._client.post("/assistants",r0(r0({body:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}retrieve(e,t){return this._client.get(t1`/assistants/${e}`,r0(r0({},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}update(e,t,r){return this._client.post(t1`/assistants/${e}`,r0(r0({body:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}list(e={},t){return this._client.getAPIList("/assistants",tD,r0(r0({query:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}delete(e,t){return this._client.delete(t1`/assistants/${e}`,r0(r0({},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}}function r3(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?r3(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):r3(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class r9 extends tQ{create(e,t){return this._client.post("/realtime/sessions",r2(r2({body:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}}function r4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function r5(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?r4(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):r4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class r6 extends tQ{create(e,t){return this._client.post("/realtime/transcription_sessions",r5(r5({body:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}}class r8 extends tQ{constructor(){super(...arguments),this.sessions=new r9(this._client),this.transcriptionSessions=new r6(this._client)}}r8.Sessions=r9,r8.TranscriptionSessions=r6;let r7=["thread_id"];function ne(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ne(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ne(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class nr extends tQ{create(e,t,r){return this._client.post(t1`/threads/${e}/messages`,nt(nt({body:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}retrieve(e,t,r){let{thread_id:n}=t;return this._client.get(t1`/threads/${n}/messages/${e}`,nt(nt({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}update(e,t,r){let{thread_id:n}=t,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(t,r7);return this._client.post(t1`/threads/${n}/messages/${e}`,nt(nt({body:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}list(e,t={},r){return this._client.getAPIList(t1`/threads/${e}/messages`,tD,nt(nt({query:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}delete(e,t,r){let{thread_id:n}=t;return this._client.delete(t1`/threads/${n}/messages/${e}`,nt(nt({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}}let nn=["thread_id","run_id"],na=["thread_id"];function ni(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ns(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ni(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ni(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function no(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}class nl extends tQ{retrieve(e,t,r){let{thread_id:n,run_id:a}=t,i=no(t,nn);return this._client.get(t1`/threads/${n}/runs/${a}/steps/${e}`,ns(ns({query:i},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}list(e,t,r){let{thread_id:n}=t,a=no(t,na);return this._client.getAPIList(t1`/threads/${n}/runs/${e}/steps`,tD,ns(ns({query:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}}let nc=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),r=t.length,n=new Uint8Array(r);for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}},nu=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;function nd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function np(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nd(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class nf extends t5{constructor(){super(...arguments),F.add(this),z.set(this,[]),Z.set(this,{}),B.set(this,{}),W.set(this,void 0),V.set(this,void 0),J.set(this,void 0),X.set(this,void 0),H.set(this,void 0),G.set(this,void 0),Y.set(this,void 0),K.set(this,void 0),Q.set(this,void 0)}[(z=new WeakMap,Z=new WeakMap,B=new WeakMap,W=new WeakMap,V=new WeakMap,J=new WeakMap,X=new WeakMap,H=new WeakMap,G=new WeakMap,Y=new WeakMap,K=new WeakMap,Q=new WeakMap,F=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new q;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let r=t?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tO.fromReadableStream(e,this.controller);for await(let e of n)e_(this,F,"m",ee).call(this,e);if(n.controller.signal?.aborted)throw new eN;return this._addRun(e_(this,F,"m",et).call(this))}toReadableStream(){return new tO(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,r,n){let a=new q;return a._run(()=>a._runToolAssistantStream(e,t,r,np(np({},n),{},{headers:np(np({},n?.headers),{},{"X-Stainless-Helper-Method":"stream"})}))),a}async _createToolAssistantStream(e,t,r,n){let a=n?.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));let i=np(np({},r),{},{stream:!0}),s=await e.submitToolOutputs(t,i,np(np({},n),{},{signal:this.controller.signal}));for await(let e of(this._connected(),s))e_(this,F,"m",ee).call(this,e);if(s.controller.signal?.aborted)throw new eN;return this._addRun(e_(this,F,"m",et).call(this))}static createThreadAssistantStream(e,t,r){let n=new q;return n._run(()=>n._threadAssistantStream(e,t,np(np({},r),{},{headers:np(np({},r?.headers),{},{"X-Stainless-Helper-Method":"stream"})}))),n}static createAssistantStream(e,t,r,n){let a=new q;return a._run(()=>a._runAssistantStream(e,t,r,np(np({},n),{},{headers:np(np({},n?.headers),{},{"X-Stainless-Helper-Method":"stream"})}))),a}currentEvent(){return e_(this,Y,"f")}currentRun(){return e_(this,K,"f")}currentMessageSnapshot(){return e_(this,W,"f")}currentRunStepSnapshot(){return e_(this,Q,"f")}async finalRunSteps(){return await this.done(),Object.values(e_(this,Z,"f"))}async finalMessages(){return await this.done(),Object.values(e_(this,B,"f"))}async finalRun(){if(await this.done(),!e_(this,V,"f"))throw Error("Final run was not received.");return e_(this,V,"f")}async _createThreadAssistantStream(e,t,r){let n=r?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let a=np(np({},t),{},{stream:!0}),i=await e.createAndRun(a,np(np({},r),{},{signal:this.controller.signal}));for await(let e of(this._connected(),i))e_(this,F,"m",ee).call(this,e);if(i.controller.signal?.aborted)throw new eN;return this._addRun(e_(this,F,"m",et).call(this))}async _createAssistantStream(e,t,r,n){let a=n?.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));let i=np(np({},r),{},{stream:!0}),s=await e.create(t,i,np(np({},n),{},{signal:this.controller.signal}));for await(let e of(this._connected(),s))e_(this,F,"m",ee).call(this,e);if(s.controller.signal?.aborted)throw new eN;return this._addRun(e_(this,F,"m",et).call(this))}static accumulateDelta(e,t){for(let[r,n]of Object.entries(t)){if(!e.hasOwnProperty(r)){e[r]=n;continue}let t=e[r];if(null==t||"index"===r||"type"===r){e[r]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(eB(t)&&eB(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!eB(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let r=e.index;if(null==r)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof r)throw Error(`Expected array delta entry \`index\` property to be a number but got ${r}`);let n=t[r];null==n?t.push(e):t[r]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${r}, deltaValue: ${n}, accValue: ${t}`);e[r]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,r){return await this._createThreadAssistantStream(t,e,r)}async _runAssistantStream(e,t,r,n){return await this._createAssistantStream(t,e,r,n)}async _runToolAssistantStream(e,t,r,n){return await this._createToolAssistantStream(t,e,r,n)}}q=nf,ee=function(e){if(!this.ended)switch(ew(this,Y,e,"f"),e_(this,F,"m",ea).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":e_(this,F,"m",el).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":e_(this,F,"m",en).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":e_(this,F,"m",er).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},et=function(){if(this.ended)throw new eE("stream has ended, this shouldn't happen");if(!e_(this,V,"f"))throw Error("Final run has not been received");return e_(this,V,"f")},er=function(e){let[t,r]=e_(this,F,"m",es).call(this,e,e_(this,W,"f"));for(let e of(ew(this,W,t,"f"),e_(this,B,"f")[t.id]=t,r)){let r=t.content[e.index];r?.type=="text"&&this._emit("textCreated",r.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let r of e.data.delta.content){if("text"==r.type&&r.text){let e=r.text,n=t.content[r.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(r.index!=e_(this,J,"f")){if(e_(this,X,"f"))switch(e_(this,X,"f").type){case"text":this._emit("textDone",e_(this,X,"f").text,e_(this,W,"f"));break;case"image_file":this._emit("imageFileDone",e_(this,X,"f").image_file,e_(this,W,"f"))}ew(this,J,r.index,"f")}ew(this,X,t.content[r.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==e_(this,J,"f")){let t=e.data.content[e_(this,J,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,e_(this,W,"f"));break;case"text":this._emit("textDone",t.text,e_(this,W,"f"))}}e_(this,W,"f")&&this._emit("messageDone",e.data),ew(this,W,void 0,"f")}},en=function(e){let t=e_(this,F,"m",ei).call(this,e);switch(ew(this,Q,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let r=e.data.delta;if(r.step_details&&"tool_calls"==r.step_details.type&&r.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of r.step_details.tool_calls)e.index==e_(this,H,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(e_(this,G,"f")&&this._emit("toolCallDone",e_(this,G,"f")),ew(this,H,e.index,"f"),ew(this,G,t.step_details.tool_calls[e.index],"f"),e_(this,G,"f")&&this._emit("toolCallCreated",e_(this,G,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ew(this,Q,void 0,"f"),"tool_calls"==e.data.step_details.type&&e_(this,G,"f")&&(this._emit("toolCallDone",e_(this,G,"f")),ew(this,G,void 0,"f")),this._emit("runStepDone",e.data,t)}},ea=function(e){e_(this,z,"f").push(e),this._emit("event",e)},ei=function(e){switch(e.event){case"thread.run.step.created":return e_(this,Z,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=e_(this,Z,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let r=e.data;if(r.delta){let n=q.accumulateDelta(t,r.delta);e_(this,Z,"f")[e.data.id]=n}return e_(this,Z,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":e_(this,Z,"f")[e.data.id]=e.data}if(e_(this,Z,"f")[e.data.id])return e_(this,Z,"f")[e.data.id];throw Error("No snapshot available")},es=function(e,t){let r=[];switch(e.event){case"thread.message.created":return[e.data,r];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let r=t.content[e.index];t.content[e.index]=e_(this,F,"m",eo).call(this,e,r)}else t.content[e.index]=e,r.push(e);return[t,r];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,r];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eo=function(e,t){return q.accumulateDelta(t,e)},el=function(e){switch(ew(this,K,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":ew(this,V,e.data,"f"),e_(this,G,"f")&&(this._emit("toolCallDone",e_(this,G,"f")),ew(this,G,void 0,"f"))}};let nh=["include"],nm=["thread_id"],ng=["thread_id"];function ny(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nb(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ny(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ny(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nv(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}class nO extends tQ{constructor(){super(...arguments),this.steps=new nl(this._client)}create(e,t,r){let{include:n}=t,a=nv(t,nh);return this._client.post(t1`/threads/${e}/runs`,nb(nb({query:{include:n},body:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers]),stream:t.stream??!1}))}retrieve(e,t,r){let{thread_id:n}=t;return this._client.get(t1`/threads/${n}/runs/${e}`,nb(nb({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}update(e,t,r){let{thread_id:n}=t,a=nv(t,nm);return this._client.post(t1`/threads/${n}/runs/${e}`,nb(nb({body:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}list(e,t={},r){return this._client.getAPIList(t1`/threads/${e}/runs`,tD,nb(nb({query:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}cancel(e,t,r){let{thread_id:n}=t;return this._client.post(t1`/threads/${n}/runs/${e}/cancel`,nb(nb({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(n.id,{thread_id:e},r)}createAndStream(e,t,r){return nf.createAssistantStream(e,this._client.beta.threads.runs,t,r)}async poll(e,t,r){let n=rU([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:a,response:i}=await this.retrieve(e,t,nb(nb({},r),{},{headers:nb(nb({},r?.headers),n)})).withResponse();switch(a.status){case"queued":case"in_progress":case"cancelling":let s=5e3;if(r?.pollIntervalMs)s=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(s=t)}}await eJ(s);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return a}}}stream(e,t,r){return nf.createAssistantStream(e,this._client.beta.threads.runs,t,r)}submitToolOutputs(e,t,r){let{thread_id:n}=t,a=nv(t,ng);return this._client.post(t1`/threads/${n}/runs/${e}/submit_tool_outputs`,nb(nb({body:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers]),stream:t.stream??!1}))}async submitToolOutputsAndPoll(e,t,r){let n=await this.submitToolOutputs(e,t,r);return await this.poll(n.id,t,r)}submitToolOutputsStream(e,t,r){return nf.createToolAssistantStream(e,this._client.beta.threads.runs,t,r)}}function nx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nx(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nx(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nO.Steps=nl;class n_ extends tQ{constructor(){super(...arguments),this.runs=new nO(this._client),this.messages=new nr(this._client)}create(e={},t){return this._client.post("/threads",nw(nw({body:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}retrieve(e,t){return this._client.get(t1`/threads/${e}`,nw(nw({},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}update(e,t,r){return this._client.post(t1`/threads/${e}`,nw(nw({body:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}delete(e,t){return this._client.delete(t1`/threads/${e}`,nw(nw({},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}createAndRun(e,t){return this._client.post("/threads/runs",nw(nw({body:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1}))}async createAndRunPoll(e,t){let r=await this.createAndRun(e,t);return await this.runs.poll(r.id,{thread_id:r.thread_id},t)}createAndRunStream(e,t){return nf.createThreadAssistantStream(e,this._client.beta.threads,t)}}n_.Runs=nO,n_.Messages=nr;class nj extends tQ{constructor(){super(...arguments),this.realtime=new r8(this._client),this.assistants=new r1(this._client),this.threads=new n_(this._client)}}function nP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nP(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nj.Realtime=r8,nj.Assistants=r1,nj.Threads=n_;class nE extends tQ{create(e,t){return this._client.post("/completions",nS(nS({body:e},t),{},{stream:e.stream??!1}))}}function nA(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nA(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nA(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class nT extends tQ{retrieve(e,t,r){let{container_id:n}=t;return this._client.get(t1`/containers/${n}/files/${e}/content`,nN(nN({},r),{},{headers:rU([{Accept:"application/binary"},r?.headers]),__binaryResponse:!0}))}}function nI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nD(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nI(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class nk extends tQ{constructor(){super(...arguments),this.content=new nT(this._client)}create(e,t,r){return this._client.post(t1`/containers/${e}/files`,tU(nD({body:t},r),this._client))}retrieve(e,t,r){let{container_id:n}=t;return this._client.get(t1`/containers/${n}/files/${e}`,r)}list(e,t={},r){return this._client.getAPIList(t1`/containers/${e}/files`,tD,nD({query:t},r))}delete(e,t,r){let{container_id:n}=t;return this._client.delete(t1`/containers/${n}/files/${e}`,nD(nD({},r),{},{headers:rU([{Accept:"*/*"},r?.headers])}))}}function nC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nC(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nk.Content=nT;class n$ extends tQ{constructor(){super(...arguments),this.files=new nk(this._client)}create(e,t){return this._client.post("/containers",nR({body:e},t))}retrieve(e,t){return this._client.get(t1`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tD,nR({query:e},t))}delete(e,t){return this._client.delete(t1`/containers/${e}`,nR(nR({},t),{},{headers:rU([{Accept:"*/*"},t?.headers])}))}}function nL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nL(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}n$.Files=nk;class nU extends tQ{create(e,t){let r=!!e.encoding_format,n=r?e.encoding_format:"base64";r&&e1(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let a=this._client.post("/embeddings",nM({body:nM(nM({},e),{},{encoding_format:n})},t));return r?a:(e1(this._client).debug("embeddings/decoding base64 embeddings from base64"),a._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=nc(t)}),e)))}}let nF=["eval_id"];function nq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}class nz extends tQ{retrieve(e,t,r){let{eval_id:n,run_id:a}=t;return this._client.get(t1`/evals/${n}/runs/${a}/output_items/${e}`,r)}list(e,t,r){let{eval_id:n}=t,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(t,nF);return this._client.getAPIList(t1`/evals/${n}/runs/${e}/output_items`,tD,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nq(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({query:a},r))}}function nZ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nZ(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nZ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class nW extends tQ{constructor(){super(...arguments),this.outputItems=new nz(this._client)}create(e,t,r){return this._client.post(t1`/evals/${e}/runs`,nB({body:t},r))}retrieve(e,t,r){let{eval_id:n}=t;return this._client.get(t1`/evals/${n}/runs/${e}`,r)}list(e,t={},r){return this._client.getAPIList(t1`/evals/${e}/runs`,tD,nB({query:t},r))}delete(e,t,r){let{eval_id:n}=t;return this._client.delete(t1`/evals/${n}/runs/${e}`,r)}cancel(e,t,r){let{eval_id:n}=t;return this._client.post(t1`/evals/${n}/runs/${e}`,r)}}function nV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nJ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nV(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nW.OutputItems=nz;class nX extends tQ{constructor(){super(...arguments),this.runs=new nW(this._client)}create(e,t){return this._client.post("/evals",nJ({body:e},t))}retrieve(e,t){return this._client.get(t1`/evals/${e}`,t)}update(e,t,r){return this._client.post(t1`/evals/${e}`,nJ({body:t},r))}list(e={},t){return this._client.getAPIList("/evals",tD,nJ({query:e},t))}delete(e,t){return this._client.delete(t1`/evals/${e}`,t)}}function nH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nG(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nH(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}nX.Runs=nW;class nY extends tQ{create(e,t){return this._client.post("/files",tU(nG({body:e},t),this._client))}retrieve(e,t){return this._client.get(t1`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tD,nG({query:e},t))}delete(e,t){return this._client.delete(t1`/files/${e}`,t)}content(e,t){return this._client.get(t1`/files/${e}/content`,nG(nG({},t),{},{headers:rU([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0}))}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:r=18e5}={}){let n=new Set(["processed","error","deleted"]),a=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await eJ(t),i=await this.retrieve(e),Date.now()-a>r)throw new eI({message:`Giving up on waiting for file ${e} to finish processing after ${r} milliseconds.`});return i}}class nK extends tQ{}function nQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nQ(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class n1 extends tQ{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",n0({body:e},t))}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",n0({body:e},t))}}class n3 extends tQ{constructor(){super(...arguments),this.graders=new n1(this._client)}}function n2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n2(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}n3.Graders=n1;class n4 extends tQ{create(e,t,r){return this._client.getAPIList(t1`/fine_tuning/checkpoints/${e}/permissions`,tI,n9({body:t,method:"post"},r))}retrieve(e,t={},r){return this._client.get(t1`/fine_tuning/checkpoints/${e}/permissions`,n9({query:t},r))}delete(e,t,r){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(t1`/fine_tuning/checkpoints/${n}/permissions/${e}`,r)}}class n5 extends tQ{constructor(){super(...arguments),this.permissions=new n4(this._client)}}function n6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}n5.Permissions=n4;class n8 extends tQ{list(e,t={},r){return this._client.getAPIList(t1`/fine_tuning/jobs/${e}/checkpoints`,tD,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n6(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({query:t},r))}}function n7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ae(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n7(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class at extends tQ{constructor(){super(...arguments),this.checkpoints=new n8(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",ae({body:e},t))}retrieve(e,t){return this._client.get(t1`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tD,ae({query:e},t))}cancel(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},r){return this._client.getAPIList(t1`/fine_tuning/jobs/${e}/events`,tD,ae({query:t},r))}pause(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(t1`/fine_tuning/jobs/${e}/resume`,t)}}at.Checkpoints=n8;class ar extends tQ{constructor(){super(...arguments),this.methods=new nK(this._client),this.jobs=new at(this._client),this.checkpoints=new n5(this._client),this.alpha=new n3(this._client)}}ar.Methods=nK,ar.Jobs=at,ar.Checkpoints=n5,ar.Alpha=n3;class an extends tQ{}class aa extends tQ{constructor(){super(...arguments),this.graderModels=new an(this._client)}}function ai(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function as(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ai(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ai(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}aa.GraderModels=an;class ao extends tQ{createVariation(e,t){return this._client.post("/images/variations",tU(as({body:e},t),this._client))}edit(e,t){return this._client.post("/images/edits",tU(as({body:e},t),this._client))}generate(e,t){return this._client.post("/images/generations",as({body:e},t))}}class al extends tQ{retrieve(e,t){return this._client.get(t1`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tI,e)}delete(e,t){return this._client.delete(t1`/models/${e}`,t)}}function ac(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}class au extends tQ{create(e,t){return this._client.post("/moderations",function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ac(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ac(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({body:e},t))}}function ad(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ap(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ad(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ad(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function af(e,t){let r=e.output.map(e=>{if("function_call"===e.type)return ap(ap({},e),{},{parsed_arguments:function(e,t){let r=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return ap(ap(ap({},t),t),{},{parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(r)?r.$parseRaw(t.arguments):r?.strict?JSON.parse(t.arguments):null})}(t,e)});if("message"===e.type){let r=e.content.map(e=>{var r,n;return"output_text"===e.type?ap(ap({},e),{},{parsed:(r=t,n=e.text,r.text?.format?.type!=="json_schema"?null:"$parseRaw"in r.text?.format?(r.text?.format).$parseRaw(n):JSON.parse(n))}):e});return ap(ap({},e),{},{content:r})}return e}),n=Object.assign({},e,{output:r});return Object.getOwnPropertyDescriptor(e,"output_text")||ah(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function ah(e){let t=[];for(let r of e.output)if("message"===r.type)for(let e of r.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}function am(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ag(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?am(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):am(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class ay extends t5{constructor(e){super(),ec.add(this),eu.set(this,void 0),ed.set(this,void 0),ep.set(this,void 0),ew(this,eu,e,"f")}static createResponse(e,t,r){let n=new ay(t);return n._run(()=>n._createOrRetrieveResponse(e,t,ag(ag({},r),{},{headers:ag(ag({},r?.headers),{},{"X-Stainless-Helper-Method":"stream"})}))),n}async _createOrRetrieveResponse(e,t,r){let n,a=r?.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort())),e_(this,ec,"m",ef).call(this);let i=null;for await(let a of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},ag(ag({},r),{},{signal:this.controller.signal,stream:!0})),i=t.starting_after??null):n=await e.responses.create(ag(ag({},t),{},{stream:!0}),ag(ag({},r),{},{signal:this.controller.signal})),this._connected(),n))e_(this,ec,"m",eh).call(this,a,i);if(n.controller.signal?.aborted)throw new eN;return e_(this,ec,"m",em).call(this)}[(eu=new WeakMap,ed=new WeakMap,ep=new WeakMap,ec=new WeakSet,ef=function(){this.ended||ew(this,ed,void 0,"f")},eh=function(e,t){if(this.ended)return;let r=(e,r)=>{(null==t||r.sequence_number>t)&&this._emit(e,r)},n=e_(this,ec,"m",eg).call(this,e);switch(r("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new eE(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new eE(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new eE(`expected content to be 'output_text', got ${n.type}`);r("response.output_text.delta",ag(ag({},e),{},{snapshot:n.text}))}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new eE(`missing output at index ${e.output_index}`);"function_call"===t.type&&r("response.function_call_arguments.delta",ag(ag({},e),{},{snapshot:t.arguments}));break}default:r(e.type,e)}},em=function(){if(this.ended)throw new eE("stream has ended, this shouldn't happen");let e=e_(this,ed,"f");if(!e)throw new eE("request ended without sending any events");ew(this,ed,void 0,"f");let t=function(e,t){var r;return t&&(r=t,t7(r.text?.format))?af(e,t):ap(ap({},e),{},{output_parsed:null,output:e.output.map(e=>"function_call"===e.type?ap(ap({},e),{},{parsed_arguments:null}):"message"===e.type?ap(ap({},e),{},{content:e.content.map(e=>ap(ap({},e),{},{parsed:null}))}):e)})}(e,e_(this,eu,"f"));return ew(this,ep,t,"f"),t},eg=function(e){let t=e_(this,ed,"f");if(!t){if("response.created"!==e.type)throw new eE(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return ew(this,ed,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let r=t.output[e.output_index];if(!r)throw new eE(`missing output at index ${e.output_index}`);"message"===r.type&&r.content.push(e.part);break}case"response.output_text.delta":{let r=t.output[e.output_index];if(!r)throw new eE(`missing output at index ${e.output_index}`);if("message"===r.type){let t=r.content[e.content_index];if(!t)throw new eE(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eE(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let r=t.output[e.output_index];if(!r)throw new eE(`missing output at index ${e.output_index}`);"function_call"===r.type&&(r.arguments+=e.delta);break}case"response.completed":ew(this,ed,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],r=!1;return this.on("event",r=>{let n=t.shift();n?n.resolve(r):e.push(r)}),this.on("end",()=>{for(let e of(r=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(r=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:r?{value:void 0,done:!0}:new Promise((e,r)=>t.push({resolve:e,reject:r})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=e_(this,ep,"f");if(!e)throw new eE("stream ended without producing a ChatCompletion");return e}}function ab(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}class av extends tQ{list(e,t={},r){return this._client.getAPIList(t1`/responses/${e}/input_items`,tD,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ab(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ab(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({query:t},r))}}function aO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ax(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aO(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class aw extends tQ{constructor(){super(...arguments),this.inputItems=new av(this._client)}create(e,t){return this._client.post("/responses",ax(ax({body:e},t),{},{stream:e.stream??!1}))._thenUnwrap(e=>("object"in e&&"response"===e.object&&ah(e),e))}retrieve(e,t={},r){return this._client.get(t1`/responses/${e}`,ax(ax({query:t},r),{},{stream:t?.stream??!1}))}delete(e,t){return this._client.delete(t1`/responses/${e}`,ax(ax({},t),{},{headers:rU([{Accept:"*/*"},t?.headers])}))}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>af(t,e))}stream(e,t){return ay.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(t1`/responses/${e}/cancel`,t)}}function a_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}aw.InputItems=av;class aj extends tQ{create(e,t,r){return this._client.post(t1`/uploads/${e}/parts`,tU(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a_(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({body:t},r),this._client))}}function aP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aP(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class aE extends tQ{constructor(){super(...arguments),this.parts=new aj(this._client)}create(e,t){return this._client.post("/uploads",aS({body:e},t))}cancel(e,t){return this._client.post(t1`/uploads/${e}/cancel`,t)}complete(e,t,r){return this._client.post(t1`/uploads/${e}/complete`,aS({body:t},r))}}aE.Parts=aj;let aA=async e=>{let t=await Promise.allSettled(e),r=t.filter(e=>"rejected"===e.status);if(r.length){for(let e of r)console.error(e.reason);throw Error(`${r.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n},aN=["vector_store_id"];function aT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aT(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class aD extends tQ{create(e,t,r){return this._client.post(t1`/vector_stores/${e}/file_batches`,aI(aI({body:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}retrieve(e,t,r){let{vector_store_id:n}=t;return this._client.get(t1`/vector_stores/${n}/file_batches/${e}`,aI(aI({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}cancel(e,t,r){let{vector_store_id:n}=t;return this._client.post(t1`/vector_stores/${n}/file_batches/${e}/cancel`,aI(aI({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}async createAndPoll(e,t,r){let n=await this.create(e,t);return await this.poll(e,n.id,r)}listFiles(e,t,r){let{vector_store_id:n}=t,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(t,aN);return this._client.getAPIList(t1`/vector_stores/${n}/file_batches/${e}/files`,tD,aI(aI({query:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}async poll(e,t,r){let n=rU([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:a,response:i}=await this.retrieve(t,{vector_store_id:e},aI(aI({},r),{},{headers:n})).withResponse();switch(a.status){case"in_progress":let s=5e3;if(r?.pollIntervalMs)s=r.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(s=t)}}await eJ(s);break;case"failed":case"cancelled":case"completed":return a}}}async uploadAndPoll(e,{files:t,fileIds:r=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let a=Math.min(n?.maxConcurrency??5,t.length),i=this._client,s=t.values(),o=[...r];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(a).fill(s).map(l);return await aA(c),await this.createAndPoll(e,{file_ids:o})}}let ak=["vector_store_id"];function aC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aR(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aC(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class a$ extends tQ{create(e,t,r){return this._client.post(t1`/vector_stores/${e}/files`,aR(aR({body:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}retrieve(e,t,r){let{vector_store_id:n}=t;return this._client.get(t1`/vector_stores/${n}/files/${e}`,aR(aR({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}update(e,t,r){let{vector_store_id:n}=t,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(t,ak);return this._client.post(t1`/vector_stores/${n}/files/${e}`,aR(aR({body:a},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}list(e,t={},r){return this._client.getAPIList(t1`/vector_stores/${e}/files`,tD,aR(aR({query:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}delete(e,t,r){let{vector_store_id:n}=t;return this._client.delete(t1`/vector_stores/${n}/files/${e}`,aR(aR({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}async createAndPoll(e,t,r){let n=await this.create(e,t,r);return await this.poll(e,n.id,r)}async poll(e,t,r){let n=rU([r?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":r?.pollIntervalMs?.toString()??void 0}]);for(;;){let a=await this.retrieve(t,{vector_store_id:e},aR(aR({},r),{},{headers:n})).withResponse(),i=a.data;switch(i.status){case"in_progress":let s=5e3;if(r?.pollIntervalMs)s=r.pollIntervalMs;else{let e=a.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(s=t)}}await eJ(s);break;case"failed":case"completed":return i}}}async upload(e,t,r){let n=await this._client.files.create({file:t,purpose:"assistants"},r);return this.create(e,{file_id:n.id},r)}async uploadAndPoll(e,t,r){let n=await this.upload(e,t,r);return await this.poll(e,n.id,r)}content(e,t,r){let{vector_store_id:n}=t;return this._client.getAPIList(t1`/vector_stores/${n}/files/${e}/content`,tI,aR(aR({},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}}function aL(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aL(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aL(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}class aU extends tQ{constructor(){super(...arguments),this.files=new a$(this._client),this.fileBatches=new aD(this._client)}create(e,t){return this._client.post("/vector_stores",aM(aM({body:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}retrieve(e,t){return this._client.get(t1`/vector_stores/${e}`,aM(aM({},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}update(e,t,r){return this._client.post(t1`/vector_stores/${e}`,aM(aM({body:t},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}list(e={},t){return this._client.getAPIList("/vector_stores",tD,aM(aM({query:e},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}delete(e,t){return this._client.delete(t1`/vector_stores/${e}`,aM(aM({},t),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},t?.headers])}))}search(e,t,r){return this._client.getAPIList(t1`/vector_stores/${e}/search`,tI,aM(aM({body:t,method:"post"},r),{},{headers:rU([{"OpenAI-Beta":"assistants=v2"},r?.headers])}))}}aU.Files=a$,aU.FileBatches=aD;let aF=["baseURL","apiKey","organization","project"],aq=["signal","method"];function az(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aZ(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?az(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):az(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function aB(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},i=Object.keys(e);for(n=0;n<i.length;n++)r=i[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}class aW{constructor(e={}){let{baseURL:t=nu("OPENAI_BASE_URL"),apiKey:r=nu("OPENAI_API_KEY"),organization:n=nu("OPENAI_ORG_ID")??null,project:a=nu("OPENAI_PROJECT_ID")??null}=e,i=aB(e,aF);if(eb.set(this,void 0),this.completions=new nE(this),this.chat=new r$(this),this.embeddings=new nU(this),this.files=new nY(this),this.images=new ao(this),this.audio=new rH(this),this.moderations=new au(this),this.models=new al(this),this.fineTuning=new ar(this),this.graders=new aa(this),this.vectorStores=new aU(this),this.beta=new nj(this),this.batches=new rK(this),this.uploads=new aE(this),this.responses=new aw(this),this.evals=new nX(this),this.containers=new n$(this),void 0===r)throw new eE("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let s=aZ(aZ({apiKey:r,organization:n,project:a},i),{},{baseURL:t||"https://api.openai.com/v1"});if(!s.dangerouslyAllowBrowser&&e9())throw new eE("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=s.baseURL,this.timeout=s.timeout??ey.DEFAULT_TIMEOUT,this.logger=s.logger??console;let o="warn";this.logLevel=o,this.logLevel=eG(s.logLevel,"ClientOptions.logLevel",this)??eG(nu("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??o,this.fetchOptions=s.fetchOptions,this.maxRetries=s.maxRetries??2,this.fetch=s.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ew(this,eb,tn,"f"),this._options=s,this.apiKey=r,this.organization=n,this.project=a}withOptions(e){return new this.constructor(aZ(aZ({},this._options),{},{baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project},e))}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return rU([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let r,n,a=e,i=function(e=tm){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let r=e.charset||tm.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=ta;if(void 0!==e.format){if(!tc.call(ti,e.format))throw TypeError("Unknown format option provided.");n=e.format}let a=ti[n],i=tm.filter;if(("function"==typeof e.filter||td(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in tu?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tm.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let s=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tm.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tm.addQueryPrefix,allowDots:s,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tm.allowEmptyArrays,arrayFormat:t,charset:r,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tm.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tm.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tm.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tm.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tm.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tm.encodeValuesOnly,filter:i,format:n,formatter:a,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tm.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tm.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tm.strictNullHandling}}(t);"function"==typeof i.filter?a=(0,i.filter)("",a):td(i.filter)&&(r=i.filter);let s=[];if("object"!=typeof a||null===a)return"";let o=tu[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;r||(r=Object.keys(a)),i.sort&&r.sort(i.sort);let c=new WeakMap;for(let e=0;e<r.length;++e){let t=r[e];i.skipNulls&&null===a[t]||tf(s,function e(t,r,n,a,i,s,o,l,c,u,d,p,f,h,m,g,y,b){var v,O;let x,w=t,_=b,j=0,P=!1;for(;void 0!==(_=_.get(tg))&&!P;){let e=_.get(t);if(j+=1,void 0!==e)if(e===j)throw RangeError("Cyclic object value");else P=!0;void 0===_.get(tg)&&(j=0)}if("function"==typeof u?w=u(r,w):w instanceof Date?w=f?.(w):"comma"===n&&td(w)&&(w=tl(w,function(e){return e instanceof Date?f?.(e):e})),null===w){if(s)return c&&!g?c(r,tm.encoder,y,"key",h):r;w=""}if("string"==typeof(v=w)||"number"==typeof v||"boolean"==typeof v||"symbol"==typeof v||"bigint"==typeof v||(O=w)&&"object"==typeof O&&O.constructor&&O.constructor.isBuffer&&O.constructor.isBuffer(O)){if(c){let e=g?r:c(r,tm.encoder,y,"key",h);return[m?.(e)+"="+m?.(c(w,tm.encoder,y,"value",h))]}return[m?.(r)+"="+m?.(String(w))]}let S=[];if(void 0===w)return S;if("comma"===n&&td(w))g&&c&&(w=tl(w,c)),x=[{value:w.length>0?w.join(",")||null:void 0}];else if(td(u))x=u;else{let e=Object.keys(w);x=d?e.sort(d):e}let E=l?String(r).replace(/\./g,"%2E"):String(r),A=a&&td(w)&&1===w.length?E+"[]":E;if(i&&td(w)&&0===w.length)return A+"[]";for(let r=0;r<x.length;++r){let v=x[r],O="object"==typeof v&&void 0!==v.value?v.value:w[v];if(o&&null===O)continue;let _=p&&l?v.replace(/\./g,"%2E"):v,P=td(w)?"function"==typeof n?n(A,_):A:A+(p?"."+_:"["+_+"]");b.set(t,j);let E=new WeakMap;E.set(tg,b),tf(S,e(O,P,n,a,i,s,o,l,"comma"===n&&g&&td(w)?null:c,u,d,p,f,h,m,g,y,E))}return S}(a[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=s.join(i.delimiter),d=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),u.length>0?d+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${e2}`}defaultIdempotencyKey(){return`stainless-node-retry-${ej()}`}makeStatusError(e,t,r,n){return eA.generate(e,t,r,n)}buildURL(e,t){let r=new URL(eZ(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(n)&&(t=aZ(aZ({},n),t)),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(r=>aZ({method:e,path:t},r)))}request(e,t=null){return new tS(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,r){let n=await e,a=n.maxRetries??this.maxRetries;null==t&&(t=a),await this.prepareOptions(n);let{req:i,url:s,timeout:o}=this.buildRequest(n,{retryCount:a-t});await this.prepareRequest(i,{url:s,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===r?"":`, retryOf: ${r}`,u=Date.now();if(e1(this).debug(`[${l}] sending request`,e3({retryOfRequestLogID:r,method:n.method,url:s,options:n,headers:i.headers})),n.signal?.aborted)throw new eN;let d=new AbortController,p=await this.fetchWithTimeout(s,i,o,d).catch(eS),f=Date.now();if(p instanceof Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new eN;let a=eP(p)||/timed? ?out/i.test(String(p)+("cause"in p?String(p.cause):""));if(t)return e1(this).info(`[${l}] connection ${a?"timed out":"failed"} - ${e}`),e1(this).debug(`[${l}] connection ${a?"timed out":"failed"} (${e})`,e3({retryOfRequestLogID:r,url:s,durationMs:f-u,message:p.message})),this.retryRequest(n,t,r??l);if(e1(this).info(`[${l}] connection ${a?"timed out":"failed"} - error; no more retries left`),e1(this).debug(`[${l}] connection ${a?"timed out":"failed"} (error; no more retries left)`,e3({retryOfRequestLogID:r,url:s,durationMs:f-u,message:p.message})),a)throw new eI;throw new eT({cause:p})}let h=[...p.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${h}] ${i.method} ${s} ${p.ok?"succeeded":"failed"} with status ${p.status} in ${f-u}ms`;if(!p.ok){let e=this.shouldRetry(p);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tr(p.body),e1(this).info(`${m} - ${e}`),e1(this).debug(`[${l}] response error (${e})`,e3({retryOfRequestLogID:r,url:p.url,status:p.status,headers:p.headers,durationMs:f-u})),this.retryRequest(n,t,r??l,p.headers)}let a=e?"error; no more retries left":"error; not retryable";e1(this).info(`${m} - ${a}`);let i=await p.text().catch(e=>eS(e).message),s=eV(i),o=s?void 0:i;throw e1(this).debug(`[${l}] response error (${a})`,e3({retryOfRequestLogID:r,url:p.url,status:p.status,headers:p.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(p.status,s,o,p.headers)}return e1(this).info(m),e1(this).debug(`[${l}] response start`,e3({retryOfRequestLogID:r,url:p.url,status:p.status,headers:p.headers,durationMs:f-u})),{response:p,options:n,controller:d,requestLogID:l,retryOfRequestLogID:r,startTime:u}}getAPIList(e,t,r){return this.requestAPIList(t,aZ({method:"get",path:e},r))}requestAPIList(e,t){return new tT(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,r,n){let a=t||{},{signal:i,method:s}=a,o=aB(a,aq);i&&i.addEventListener("abort",()=>n.abort());let l=setTimeout(()=>n.abort(),r),c=globalThis.ReadableStream&&o.body instanceof globalThis.ReadableStream||"object"==typeof o.body&&null!==o.body&&Symbol.asyncIterator in o.body,u=aZ(aZ({signal:n.signal},c?{duplex:"half"}:{}),{},{method:"GET"},o);s&&(u.method=s.toUpperCase());try{return await this.fetch.call(void 0,e,u)}finally{clearTimeout(l)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,r,n){let a,i=n?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(a=e)}let s=n?.get("retry-after");if(s&&!a){let e=parseFloat(s);a=Number.isNaN(e)?Date.parse(s)-Date.now():1e3*e}if(!(a&&0<=a&&a<6e4)){let r=e.maxRetries??this.maxRetries;a=this.calculateDefaultRetryTimeoutMillis(t,r)}return await eJ(a),this.makeRequest(e,t-1,r)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let r=aZ({},e),{method:n,path:a,query:i}=r,s=this.buildURL(a,i);"timeout"in r&&eW("timeout",r.timeout),r.timeout=r.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:r}),c=this.buildHeaders({options:e,method:n,bodyHeaders:o,retryCount:t});return{req:aZ(aZ(aZ(aZ(aZ({method:n,headers:c},r.signal&&{signal:r.signal}),globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"}),l&&{body:l}),this.fetchOptions??{}),r.fetchOptions??{}),url:s,timeout:r.timeout}}buildHeaders({options:e,method:t,bodyHeaders:r,retryCount:n}){let a={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey);let i=rU([a,aZ(aZ(aZ({Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n)},e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{}),e8()),{},{"OpenAI-Organization":this.organization,"OpenAI-Project":this.project}),this.authHeaders(e),this._options.defaultHeaders,r,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let r=rU([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&r.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:te(e)}:e_(this,eb,"f").call(this,{body:e,headers:r})}}ey=aW,eb=new WeakMap,aW.OpenAI=ey,aW.DEFAULT_TIMEOUT=6e5,aW.OpenAIError=eE,aW.APIError=eA,aW.APIConnectionError=eT,aW.APIConnectionTimeoutError=eI,aW.APIUserAbortError=eN,aW.NotFoundError=eR,aW.ConflictError=e$,aW.RateLimitError=eM,aW.BadRequestError=eD,aW.AuthenticationError=ek,aW.InternalServerError=eU,aW.PermissionDeniedError=eC,aW.UnprocessableEntityError=eL,aW.toFile=tY,aW.Completions=nE,aW.Chat=r$,aW.Embeddings=nU,aW.Files=nY,aW.Images=ao,aW.Audio=rH,aW.Moderations=au,aW.Models=al,aW.FineTuning=ar,aW.Graders=aa,aW.VectorStores=aU,aW.Beta=nj,aW.Batches=rK,aW.Uploads=aE,aW.Responses=aw,aW.Evals=nX,aW.Containers=n$;let aV={"gpt-4o":{input:2.5,output:10},"gpt-4o-mini":{input:.15,output:.6},"gpt-4.1-mini":{input:.4,output:1.6},"o1-mini":{input:3,output:12},"o3-mini":{input:1.1,output:4.4},"o4-mini":{input:1.1,output:4.4}};function aJ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aJ(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aJ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}let aH=process.env.OPENAI_API_KEY;if(!aH)throw Error("OPENAI_API_KEY no est\xe1 configurada en las variables de entorno");let aG=new aW({apiKey:aH});function aY(e){if(!e||0===e.length)return console.warn("No se proporcionaron documentos para preparar"),"";let t=e.map((e,t)=>{if(!e.contenido||"string"!=typeof e.contenido)return console.warn(`Documento ${t+1} (${e.titulo||"Sin t\xedtulo"}) no tiene contenido v\xe1lido`),null;let r=function(e,t=25e3){if(null==e)return console.warn("Se intent\xf3 truncar un contenido undefined o null"),"";let r=String(e);return r.length<=t?r:r.substring(0,t)+`

[CONTENIDO TRUNCADO: El documento original es m\xe1s largo. Esta es una versi\xf3n reducida para procesamiento.]`}(e.contenido,15e3);return{titulo:e.titulo||`Documento ${t+1}`,contenido:r,categoria:e.categoria||"General",numero_tema:e.numero_tema||t+1}}).filter(e=>null!==e);if(0===t.length)return console.warn("No se pudieron procesar documentos v\xe1lidos"),"";let r=t.map(e=>`
=== DOCUMENTO: ${e.titulo} ===
Categor\xeda: ${e.categoria}
Tema: ${e.numero_tema}

${e.contenido}

=== FIN DOCUMENTO: ${e.titulo} ===
`).join("\n\n");return r.length>5e4?(console.warn(`El contexto total (${r.length} caracteres) excede el l\xedmite de 50000. Se truncar\xe1.`),r.substring(0,5e4)+`

[CONTEXTO TRUNCADO: El contenido total excedi\xf3 el l\xedmite de 50000 caracteres.]`):r}async function aK(e,t={}){let{model:n,temperature:a=.7,max_tokens:i=4e3,activityName:s="OpenAI Call"}=t;if(!n)throw Error("❌ MODELO REQUERIDO: Debe especificarse un modelo espec\xedfico para cada tarea. No se permite usar modelo por defecto.");try{let t=n.includes("o1")||n.includes("o3")||n.includes("o4")||n.startsWith("o"),l={model:n,messages:e};t?l.max_completion_tokens=i:(l.max_tokens=i,l.temperature=a);let c=await aG.chat.completions.create(l),u=c.choices[0]?.message?.content;if(!u)throw Error("OpenAI no devolvi\xf3 una respuesta v\xe1lida");if(c.usage){var o;o=function(e,t,r,n){let a={promptTokens:r.prompt_tokens||0,completionTokens:r.completion_tokens||0,totalTokens:r.total_tokens||0};return a.estimatedCost=function(e,t){let r=e.toLowerCase(),n=aV[r=r.replace(/-\d{4}-\d{2}-\d{2}$/,"")];return n?t.promptTokens/1e6*n.input+t.completionTokens/1e6*n.output:(console.warn(`⚠️ Precios no encontrados para el modelo: ${e} (normalizado: ${r})`),0)}(t,a),{activity:e,model:t,usage:a,timestamp:new Date,userId:void 0}}(s,n,c.usage),console.log(function(e){let{activity:t,model:r,usage:n,timestamp:a}=e,i=a.toLocaleTimeString("es-ES");return`🤖 [${i}] ${t} | ${r} | 📥 ${n.promptTokens} → 📤 ${n.completionTokens} = 🔢 ${n.totalTokens} tokens`}(o)),console.log(`🔄 Intentando guardar tokens en Supabase... (Servidor)`),r.e(6778).then(r.bind(r,6778)).then(({saveTokenUsageServer:e})=>{console.log("✅ Servicio del servidor importado correctamente"),e(o).catch(e=>{console.error("❌ Error al guardar en Supabase (servidor):",e)})}).catch(e=>{console.error("❌ Error al importar servicio del servidor:",e)})}return u}catch(r){if(console.error("❌ Error al llamar a OpenAI:",r),"insufficient_quota"===r.code)throw Error("Cuota de OpenAI agotada. Por favor, verifica tu plan de facturaci\xf3n.");if("invalid_api_key"===r.code)throw Error("API Key de OpenAI inv\xe1lida. Verifica tu configuraci\xf3n.");if("model_not_found"===r.code){if(n.includes("gpt-4.1")||n.includes("o1")||n.includes("o3")||n.includes("o4"))return await aK(e,aX(aX({},t),{},{model:"gpt-4o"}));if(n.includes("gpt-4o")&&!n.includes("mini"))return await aK(e,aX(aX({},t),{},{model:"gpt-4o-mini"}));throw Error(`Modelo ${n} no encontrado. Verifica que tienes acceso a este modelo.`)}throw Error(`Error de OpenAI: ${r.message||"Error desconocido"}`)}}},91645:e=>{e.exports=require("net")},93128:(e,t,r)=>{r.r(t),r.d(t,{generarFlashcards:()=>c});var n=r(29981),a=r(86007),i=r(89459),s=r(55292);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}async function c(e,t=10,r){try{let o=(0,n.Jo)(e);if(!o)throw Error("No se han proporcionado documentos para generar flashcards.");let c=a.f7.replace("{documentos}",o).replace("{cantidad}",t.toString());c=r?c.replace("{instrucciones}",`Instrucciones adicionales:
- ${r}`):c.replace("{instrucciones}","");let u=(0,s.Vj)("FLASHCARDS");console.log(`🃏 Generando flashcards con modelo: ${u.model} (max_tokens: ${u.max_tokens})`);let d=[{role:"user",content:c}],p=(await (0,i.y5)(d,l(l({},u),{},{activityName:`Generaci\xf3n de Flashcards (${t||"N/A"} tarjetas)`}))).match(/\[\s*\{[\s\S]*\}\s*\]/);if(!p)throw Error("No se pudo extraer el formato JSON de la respuesta.");let f=p[0],h=JSON.parse(f);if(!Array.isArray(h)||0===h.length)throw Error("El formato de las flashcards generadas no es v\xe1lido.");return h}catch(e){throw console.error("Error al generar flashcards:",e),e}}},94735:e=>{e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4979,8082,1370,3760,3465,8844,5315],()=>r(84124));module.exports=n})();