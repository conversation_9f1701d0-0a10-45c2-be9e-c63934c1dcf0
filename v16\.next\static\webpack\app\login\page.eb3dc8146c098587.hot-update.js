"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/toConsumableArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\", _this = undefined, _s2 = $RefreshSig$(), _s21 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\nvar BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\nvar BackgroundTasksProvider = function BackgroundTasksProvider(_ref) {\n    _s();\n    _s2();\n    var children = _ref.children;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]), tasks = _useState[0], setTasks = _useState[1];\n    var addTask = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": function(taskData) {\n            var id = \"task_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n            var newTask = _objectSpread(_objectSpread({}, taskData), {}, {\n                id: id,\n                status: 'pending',\n                createdAt: new Date()\n            });\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": function(prev) {\n                    return [].concat((0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(prev), [\n                        newTask\n                    ]);\n                }\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.loading(\"Iniciando: \".concat(newTask.title), {\n                id: \"task_start_\".concat(id),\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    var updateTask = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": function(id, updates) {\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": function(prev) {\n                    return prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": function(task) {\n                            if (task.id === id) {\n                                var updatedTask = _objectSpread(_objectSpread({}, task), updates);\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_start_\".concat(id));\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.loading(\"Procesando: \".concat(task.title), {\n                                        id: \"task_processing_\".concat(id)\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_processing_\".concat(id));\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Completado: \".concat(task.title), {\n                                        id: \"task_completed_\".concat(id),\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_processing_\".concat(id));\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Error: \".concat(task.title), {\n                                        id: \"task_error_\".concat(id),\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n                }\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    var removeTask = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": function(id) {\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": function(prev) {\n                    return prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": function(task) {\n                            return task.id !== id;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n                }\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_start_\".concat(id));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_processing_\".concat(id));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_completed_\".concat(id));\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.dismiss(\"task_error_\".concat(id));\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    var getTask = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": function(id) {\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": function(task) {\n                    return task.id === id;\n                }\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    var getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": function(type) {\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": function(task) {\n                    return task.type === type;\n                }\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    var clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": function() {\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": function(prev) {\n                    return prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": function(task) {\n                            return task.status !== 'completed' && task.status !== 'error';\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n                }\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    var activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": function() {\n            return tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": function(task) {\n                    return task.status === 'pending' || task.status === 'processing';\n                }\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    var completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": function() {\n            return tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": function(task) {\n                    return task.status === 'completed' || task.status === 'error';\n                }\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    var value = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": function() {\n            return {\n                tasks: tasks,\n                addTask: addTask,\n                updateTask: updateTask,\n                removeTask: removeTask,\n                getTask: getTask,\n                getTasksByType: getTasksByType,\n                clearCompletedTasks: clearCompletedTasks,\n                activeTasks: activeTasks,\n                completedTasks: completedTasks\n            };\n        }\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 5\n    }, _this);\n};\n_s(BackgroundTasksProvider, \"BCErhVNJjZcscHytbGGAGFe5w6w=\");\n_c1 = BackgroundTasksProvider;\n_s2(BackgroundTasksProvider, \"KuVVScAMrQM+7NtGP7QityEq3oI=\");\n_c = BackgroundTasksProvider;\nvar useBackgroundTasks = function useBackgroundTasks() {\n    _s1();\n    _s21();\n    var context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n_s1(useBackgroundTasks, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n_s21(useBackgroundTasks, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"BackgroundTasksProvider\");\nvar _c1;\n$RefreshReg$(_c1, \"BackgroundTasksProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9CYWNrZ3JvdW5kVGFza3NDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBYTtBQUFBO0FBQUEsSUFBQUUsWUFBQSwyR0FBQUMsS0FBQSxjQUFBQyxFQUFBLElBQUFDLFlBQUEsSUFBQUMsR0FBQSxJQUFBRCxZQUFBO0FBQUEsU0FBQUUsUUFBQUMsQ0FBQSxFQUFBQyxDQUFBO0lBQUEsSUFBQUMsQ0FBQSxHQUFBQyxNQUFBLENBQUFDLElBQUEsQ0FBQUosQ0FBQTtJQUFBLElBQUFHLE1BQUEsQ0FBQUUscUJBQUE7UUFBQSxJQUFBQyxDQUFBLEdBQUFILE1BQUEsQ0FBQUUscUJBQUEsQ0FBQUwsQ0FBQTtRQUFBQyxDQUFBLEtBQUFLLENBQUEsR0FBQUEsQ0FBQSxDQUFBQyxNQUFBLFVBQUFOLENBQUE7WUFBQSxPQUFBRSxNQUFBLENBQUFLLHdCQUFBLENBQUFSLENBQUEsRUFBQUMsQ0FBQSxFQUFBUSxVQUFBO1FBQUEsS0FBQVAsQ0FBQSxDQUFBUSxJQUFBLENBQUFDLEtBQUEsQ0FBQVQsQ0FBQSxFQUFBSSxDQUFBO0lBQUE7SUFBQSxPQUFBSixDQUFBO0FBQUE7QUFBQSxTQUFBVSxjQUFBWixDQUFBO0lBQUEsUUFBQUMsQ0FBQSxNQUFBQSxDQUFBLEdBQUFZLFNBQUEsQ0FBQUMsTUFBQSxFQUFBYixDQUFBO1FBQUEsSUFBQUMsQ0FBQSxXQUFBVyxTQUFBLENBQUFaLENBQUEsSUFBQVksU0FBQSxDQUFBWixDQUFBO1FBQUFBLENBQUEsT0FBQUYsT0FBQSxDQUFBSSxNQUFBLENBQUFELENBQUEsT0FBQWEsT0FBQSxVQUFBZCxDQUFBO1lBQUFSLDBLQUFBLENBQUFPLENBQUEsRUFBQUMsQ0FBQSxFQUFBQyxDQUFBLENBQUFELENBQUE7UUFBQSxLQUFBRSxNQUFBLENBQUFhLHlCQUFBLEdBQUFiLE1BQUEsQ0FBQWMsZ0JBQUEsQ0FBQWpCLENBQUEsRUFBQUcsTUFBQSxDQUFBYSx5QkFBQSxDQUFBZCxDQUFBLEtBQUFILE9BQUEsQ0FBQUksTUFBQSxDQUFBRCxDQUFBLEdBQUFhLE9BQUEsVUFBQWQsQ0FBQTtZQUFBRSxNQUFBLENBQUFlLGNBQUEsQ0FBQWxCLENBQUEsRUFBQUMsQ0FBQSxFQUFBRSxNQUFBLENBQUFLLHdCQUFBLENBQUFOLENBQUEsRUFBQUQsQ0FBQTtRQUFBO0lBQUE7SUFBQSxPQUFBRCxDQUFBO0FBQUE7QUFFc0Y7QUFDNUQ7QUFBQztBQTBCeEMsSUFBTTRCLHNCQUFzQixpQkFBR1Isb0RBQWEsQ0FBeUNTLFNBQVMsQ0FBQztBQU14Riw4QkFBd0UsU0FBbEVDLHVCQUErREEsQ0FBQUMsSUFBQSxFQUFxQjs7SUFBQW5DLEVBQUE7SUFBQSxJQUFmb0MsUUFBUSxHQUFBRCxJQUFBLENBQVJDLFFBQVE7SUFDeEYsSUFBQUMsU0FBQSxHQUEwQlgsK0NBQVEsQ0FBbUIsRUFBRSxDQUFDLEVBQWpEWSxLQUFLLEdBQUFELFNBQUEsS0FBRUUsUUFBUSxHQUFBRixTQUFBO0lBRXRCLElBQU1HLE9BQU8sR0FBR2Isa0RBQVc7d0RBQUMsU0FBQ2MsUUFBNkQsRUFBSztZQUM3RixJQUFNQyxFQUFFLFdBQUFDLE1BQUEsQ0FBV0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxPQUFBRixNQUFBLENBQUlHLElBQUksQ0FBQ0MsTUFBTSxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLEVBQUUsQ0FBQyxDQUFDQyxNQUFNLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFFO1lBQzFFLElBQU1DLE9BQXVCLEdBQUFsQyxhQUFBLENBQUFBLGFBQUEsS0FDeEJ5QixRQUFRO2dCQUNYQyxFQUFFLEVBQUZBLEVBQUU7Z0JBQ0ZTLE1BQU0sRUFBRSxTQUFTO2dCQUNqQkMsU0FBUyxFQUFFLElBQUlSLElBQUksQ0FBQztZQUFDLEVBQ3RCO1lBRURMLFFBQVE7Z0VBQUMsU0FBQWMsSUFBSTtvQkFBQSxVQUFBVixNQUFBLENBQUEvQyw2S0FBQSxDQUFReUQsSUFBSTt3QkFBRUgsT0FBTztxQkFBQTtnQkFBQSxDQUFDOztZQUVuQztZQUNBckIsa0RBQUssQ0FBQ3lCLE9BQU8sZUFBQVgsTUFBQSxDQUFlTyxPQUFPLENBQUNLLEtBQUssR0FBSTtnQkFDM0NiLEVBQUUsZ0JBQUFDLE1BQUEsQ0FBZ0JELEVBQUUsQ0FBRTtnQkFDdEJjLFFBQVEsRUFBRTtZQUNaLENBQUMsQ0FBQztZQUVGLE9BQU9kLEVBQUU7UUFDWCxDQUFDO3VEQUFFLEVBQUUsQ0FBQztJQUVOLElBQU1lLFVBQVUsR0FBRzlCLGtEQUFXOzJEQUFDLFNBQUNlLEVBQVUsRUFBRWdCLE9BQWdDLEVBQUs7WUFDL0VuQixRQUFRO21FQUFDLFNBQUFjLElBQUk7b0JBQUEsT0FBSUEsSUFBSSxDQUFDTSxHQUFHOzJFQUFDLFNBQUFDLElBQUksRUFBSTs0QkFDaEMsSUFBSUEsSUFBSSxDQUFDbEIsRUFBRSxLQUFLQSxFQUFFLEVBQUU7Z0NBQ2xCLElBQU1tQixXQUFXLEdBQUE3QyxhQUFBLENBQUFBLGFBQUEsS0FBUTRDLElBQUksR0FBS0YsT0FBTyxDQUFFO2dDQUUzQztnQ0FDQSxJQUFJQSxPQUFPLENBQUNQLE1BQU0sS0FBSyxZQUFZLElBQUlTLElBQUksQ0FBQ1QsTUFBTSxLQUFLLFNBQVMsRUFBRTtvQ0FDaEV0QixrREFBSyxDQUFDaUMsT0FBTyxlQUFBbkIsTUFBQSxDQUFlRCxFQUFFLENBQUUsQ0FBQztvQ0FDakNiLGtEQUFLLENBQUN5QixPQUFPLGdCQUFBWCxNQUFBLENBQWdCaUIsSUFBSSxDQUFDTCxLQUFLLEdBQUk7d0NBQ3pDYixFQUFFLHFCQUFBQyxNQUFBLENBQXFCRCxFQUFFO29DQUMzQixDQUFDLENBQUM7Z0NBQ0osQ0FBQyxNQUFNLElBQUlnQixPQUFPLENBQUNQLE1BQU0sS0FBSyxXQUFXLElBQUlTLElBQUksQ0FBQ1QsTUFBTSxLQUFLLFdBQVcsRUFBRTtvQ0FDeEV0QixrREFBSyxDQUFDaUMsT0FBTyxvQkFBQW5CLE1BQUEsQ0FBb0JELEVBQUUsQ0FBRSxDQUFDO29DQUN0Q2Isa0RBQUssQ0FBQ2tDLE9BQU8sZ0JBQUFwQixNQUFBLENBQWdCaUIsSUFBSSxDQUFDTCxLQUFLLEdBQUk7d0NBQ3pDYixFQUFFLG9CQUFBQyxNQUFBLENBQW9CRCxFQUFFLENBQUU7d0NBQzFCYyxRQUFRLEVBQUU7b0NBQ1osQ0FBQyxDQUFDO29DQUNGSyxXQUFXLENBQUNHLFdBQVcsR0FBRyxJQUFJcEIsSUFBSSxDQUFDLENBQUM7Z0NBQ3RDLENBQUMsTUFBTSxJQUFJYyxPQUFPLENBQUNQLE1BQU0sS0FBSyxPQUFPLElBQUlTLElBQUksQ0FBQ1QsTUFBTSxLQUFLLE9BQU8sRUFBRTtvQ0FDaEV0QixrREFBSyxDQUFDaUMsT0FBTyxvQkFBQW5CLE1BQUEsQ0FBb0JELEVBQUUsQ0FBRSxDQUFDO29DQUN0Q2Isa0RBQUssQ0FBQ29DLEtBQUssV0FBQXRCLE1BQUEsQ0FBV2lCLElBQUksQ0FBQ0wsS0FBSyxHQUFJO3dDQUNsQ2IsRUFBRSxnQkFBQUMsTUFBQSxDQUFnQkQsRUFBRSxDQUFFO3dDQUN0QmMsUUFBUSxFQUFFO29DQUNaLENBQUMsQ0FBQztnQ0FDSjtnQ0FFQSxPQUFPSyxXQUFXOzRCQUNwQjs0QkFDQSxPQUFPRCxJQUFJO3dCQUNiLENBQUM7O2dCQUFDOztRQUNKLENBQUM7MERBQUUsRUFBRSxDQUFDO0lBRU4sSUFBTU0sVUFBVSxHQUFHdkMsa0RBQVc7MkRBQUMsU0FBQ2UsRUFBVSxFQUFLO1lBQzdDSCxRQUFRO21FQUFDLFNBQUFjLElBQUk7b0JBQUEsT0FBSUEsSUFBSSxDQUFDMUMsTUFBTTsyRUFBQyxTQUFBaUQsSUFBSTs0QkFBQSxPQUFJQSxJQUFJLENBQUNsQixFQUFFLEtBQUtBLEVBQUU7d0JBQUE7O2dCQUFDOztZQUNwRDtZQUNBYixrREFBSyxDQUFDaUMsT0FBTyxlQUFBbkIsTUFBQSxDQUFlRCxFQUFFLENBQUUsQ0FBQztZQUNqQ2Isa0RBQUssQ0FBQ2lDLE9BQU8sb0JBQUFuQixNQUFBLENBQW9CRCxFQUFFLENBQUUsQ0FBQztZQUN0Q2Isa0RBQUssQ0FBQ2lDLE9BQU8sbUJBQUFuQixNQUFBLENBQW1CRCxFQUFFLENBQUUsQ0FBQztZQUNyQ2Isa0RBQUssQ0FBQ2lDLE9BQU8sZUFBQW5CLE1BQUEsQ0FBZUQsRUFBRSxDQUFFLENBQUM7UUFDbkMsQ0FBQzswREFBRSxFQUFFLENBQUM7SUFFTixJQUFNeUIsT0FBTyxHQUFHeEMsa0RBQVc7d0RBQUMsU0FBQ2UsRUFBVSxFQUFLO1lBQzFDLE9BQU9KLEtBQUssQ0FBQzhCLElBQUk7Z0VBQUMsU0FBQVIsSUFBSTtvQkFBQSxPQUFJQSxJQUFJLENBQUNsQixFQUFFLEtBQUtBLEVBQUU7Z0JBQUE7O1FBQzFDLENBQUM7dURBQUU7UUFBQ0osS0FBSztLQUFDLENBQUM7SUFFWCxJQUFNK0IsY0FBYyxHQUFHMUMsa0RBQVc7K0RBQUMsU0FBQzJDLElBQTRCLEVBQUs7WUFDbkUsT0FBT2hDLEtBQUssQ0FBQzNCLE1BQU07dUVBQUMsU0FBQWlELElBQUk7b0JBQUEsT0FBSUEsSUFBSSxDQUFDVSxJQUFJLEtBQUtBLElBQUk7Z0JBQUE7O1FBQ2hELENBQUM7OERBQUU7UUFBQ2hDLEtBQUs7S0FBQyxDQUFDO0lBRVgsSUFBTWlDLG1CQUFtQixHQUFHNUMsa0RBQVc7b0VBQUMsWUFBTTtZQUM1Q1ksUUFBUTs0RUFBQyxTQUFBYyxJQUFJO29CQUFBLE9BQUlBLElBQUksQ0FBQzFDLE1BQU07b0ZBQUMsU0FBQWlELElBQUk7NEJBQUEsT0FBSUEsSUFBSSxDQUFDVCxNQUFNLEtBQUssV0FBVyxJQUFJUyxJQUFJLENBQUNULE1BQU0sS0FBSyxPQUFPO3dCQUFBOztnQkFBQzs7UUFDOUYsQ0FBQzttRUFBRSxFQUFFLENBQUM7SUFFTixJQUFNcUIsV0FBVyxHQUFHNUMsOENBQU87d0RBQUM7WUFBQSxPQUFNVSxLQUFLLENBQUMzQixNQUFNO2dFQUFDLFNBQUFpRCxJQUFJO29CQUFBLE9BQ2pEQSxJQUFJLENBQUNULE1BQU0sS0FBSyxTQUFTLElBQUlTLElBQUksQ0FBQ1QsTUFBTSxLQUFLLFlBQVk7Z0JBQUEsQ0FDM0Q7O1FBQUM7dURBQUU7UUFBQ2IsS0FBSztLQUFDLENBQUM7SUFFWCxJQUFNbUMsY0FBYyxHQUFHN0MsOENBQU87MkRBQUM7WUFBQSxPQUFNVSxLQUFLLENBQUMzQixNQUFNO21FQUFDLFNBQUFpRCxJQUFJO29CQUFBLE9BQ3BEQSxJQUFJLENBQUNULE1BQU0sS0FBSyxXQUFXLElBQUlTLElBQUksQ0FBQ1QsTUFBTSxLQUFLLE9BQU87Z0JBQUEsQ0FDeEQ7O1FBQUM7MERBQUU7UUFBQ2IsS0FBSztLQUFDLENBQUM7SUFFWCxJQUFNb0MsS0FBaUMsR0FBRzlDLDhDQUFPO2tEQUFDO1lBQUEsT0FBTztnQkFDdkRVLEtBQUssRUFBTEEsS0FBSztnQkFDTEUsT0FBTyxFQUFQQSxPQUFPO2dCQUNQaUIsVUFBVSxFQUFWQSxVQUFVO2dCQUNWUyxVQUFVLEVBQVZBLFVBQVU7Z0JBQ1ZDLE9BQU8sRUFBUEEsT0FBTztnQkFDUEUsY0FBYyxFQUFkQSxjQUFjO2dCQUNkRSxtQkFBbUIsRUFBbkJBLG1CQUFtQjtnQkFDbkJDLFdBQVcsRUFBWEEsV0FBVztnQkFDWEMsY0FBYyxFQUFkQTtZQUNGLENBQUM7UUFBQSxDQUFDO2lEQUFFO1FBQUNuQyxLQUFLO1FBQUVFLE9BQU87UUFBRWlCLFVBQVU7UUFBRVMsVUFBVTtRQUFFQyxPQUFPO1FBQUVFLGNBQWM7UUFBRUUsbUJBQW1CO1FBQUVDLFdBQVc7UUFBRUMsY0FBYztLQUFDLENBQUM7SUFFeEgscUJBQ0UxQyw2REFBQSxDQUFDQyxzQkFBc0IsQ0FBQzJDLFFBQVE7UUFBQ0QsS0FBSyxFQUFFQSxLQUFNO1FBQUF0QyxRQUFBLEVBQzNDQTtJQUFRO1FBQUF3QyxRQUFBLEVBQUE5RSxZQUFBO1FBQUErRSxVQUFBO1FBQUFDLFlBQUE7SUFBQSxHQUFBL0UsS0FDc0IsQ0FBQztBQUV0QyxDQUFDOztNQXJHWW1DLHVCQUErRDtBQXFHMUVsQyxFQUFBLEVBckdXa0MsdUJBQStEO0FBQUE2QyxFQUFBLEdBQS9EN0MsdUJBQStEO0FBdUdyRSx5QkFBMkIsU0FBckI4QyxrQkFBa0JBLENBQUEsRUFBUzs7SUFBQTlFLEdBQUE7SUFDdEMsSUFBTStFLE9BQU8sR0FBR3hELGlEQUFVLENBQUNPLHNCQUFzQixDQUFDO0lBQ2xELElBQUlpRCxPQUFPLEtBQUtoRCxTQUFTLEVBQUU7UUFDekIsTUFBTSxJQUFJaUQsS0FBSyxDQUFDLGtFQUFrRSxDQUFDO0lBQ3JGO0lBQ0EsT0FBT0QsT0FBTztBQUNoQixDQUFDO0lBTllELGtCQUFrQjtBQU03QjlFLEdBQUEsRUFOVzhFLGtCQUFrQjtBQUFBLElBQUFELEVBQUE7QUFBQUksWUFBQSxDQUFBSixFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcY29udGV4dHNcXEJhY2tncm91bmRUYXNrc0NvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgUmVhY3ROb2RlLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJhY2tncm91bmRUYXNrIHtcbiAgaWQ6IHN0cmluZztcbiAgdHlwZTogJ21hcGEtbWVudGFsJyB8ICd0ZXN0JyB8ICdmbGFzaGNhcmRzJyB8ICdwbGFuLWVzdHVkaW9zJyB8ICdyZXN1bWVuJztcbiAgdGl0bGU6IHN0cmluZztcbiAgc3RhdHVzOiAncGVuZGluZycgfCAncHJvY2Vzc2luZycgfCAnY29tcGxldGVkJyB8ICdlcnJvcic7XG4gIHByb2dyZXNzPzogbnVtYmVyO1xuICByZXN1bHQ/OiBhbnk7XG4gIGVycm9yPzogc3RyaW5nO1xuICBjcmVhdGVkQXQ6IERhdGU7XG4gIGNvbXBsZXRlZEF0PzogRGF0ZTtcbn1cblxuaW50ZXJmYWNlIEJhY2tncm91bmRUYXNrc0NvbnRleHRUeXBlIHtcbiAgdGFza3M6IEJhY2tncm91bmRUYXNrW107XG4gIGFkZFRhc2s6ICh0YXNrOiBPbWl0PEJhY2tncm91bmRUYXNrLCAnaWQnIHwgJ3N0YXR1cycgfCAnY3JlYXRlZEF0Jz4pID0+IHN0cmluZztcbiAgdXBkYXRlVGFzazogKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8QmFja2dyb3VuZFRhc2s+KSA9PiB2b2lkO1xuICByZW1vdmVUYXNrOiAoaWQ6IHN0cmluZykgPT4gdm9pZDtcbiAgZ2V0VGFzazogKGlkOiBzdHJpbmcpID0+IEJhY2tncm91bmRUYXNrIHwgdW5kZWZpbmVkO1xuICBnZXRUYXNrc0J5VHlwZTogKHR5cGU6IEJhY2tncm91bmRUYXNrWyd0eXBlJ10pID0+IEJhY2tncm91bmRUYXNrW107XG4gIGNsZWFyQ29tcGxldGVkVGFza3M6ICgpID0+IHZvaWQ7XG4gIGFjdGl2ZVRhc2tzOiBCYWNrZ3JvdW5kVGFza1tdO1xuICBjb21wbGV0ZWRUYXNrczogQmFja2dyb3VuZFRhc2tbXTtcbn1cblxuY29uc3QgQmFja2dyb3VuZFRhc2tzQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QmFja2dyb3VuZFRhc2tzQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG5cbmludGVyZmFjZSBCYWNrZ3JvdW5kVGFza3NQcm92aWRlclByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGNvbnN0IEJhY2tncm91bmRUYXNrc1Byb3ZpZGVyOiBSZWFjdC5GQzxCYWNrZ3JvdW5kVGFza3NQcm92aWRlclByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3QgW3Rhc2tzLCBzZXRUYXNrc10gPSB1c2VTdGF0ZTxCYWNrZ3JvdW5kVGFza1tdPihbXSk7XG5cbiAgY29uc3QgYWRkVGFzayA9IHVzZUNhbGxiYWNrKCh0YXNrRGF0YTogT21pdDxCYWNrZ3JvdW5kVGFzaywgJ2lkJyB8ICdzdGF0dXMnIHwgJ2NyZWF0ZWRBdCc+KSA9PiB7XG4gICAgY29uc3QgaWQgPSBgdGFza18ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyKDIsIDkpfWA7XG4gICAgY29uc3QgbmV3VGFzazogQmFja2dyb3VuZFRhc2sgPSB7XG4gICAgICAuLi50YXNrRGF0YSxcbiAgICAgIGlkLFxuICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgfTtcblxuICAgIHNldFRhc2tzKHByZXYgPT4gWy4uLnByZXYsIG5ld1Rhc2tdKTtcblxuICAgIC8vIE1vc3RyYXIgbm90aWZpY2FjacOzbiBkZSBpbmljaW9cbiAgICB0b2FzdC5sb2FkaW5nKGBJbmljaWFuZG86ICR7bmV3VGFzay50aXRsZX1gLCB7XG4gICAgICBpZDogYHRhc2tfc3RhcnRfJHtpZH1gLFxuICAgICAgZHVyYXRpb246IDIwMDAsXG4gICAgfSk7XG5cbiAgICByZXR1cm4gaWQ7XG4gIH0sIFtdKTtcblxuICBjb25zdCB1cGRhdGVUYXNrID0gdXNlQ2FsbGJhY2soKGlkOiBzdHJpbmcsIHVwZGF0ZXM6IFBhcnRpYWw8QmFja2dyb3VuZFRhc2s+KSA9PiB7XG4gICAgc2V0VGFza3MocHJldiA9PiBwcmV2Lm1hcCh0YXNrID0+IHtcbiAgICAgIGlmICh0YXNrLmlkID09PSBpZCkge1xuICAgICAgICBjb25zdCB1cGRhdGVkVGFzayA9IHsgLi4udGFzaywgLi4udXBkYXRlcyB9O1xuXG4gICAgICAgIC8vIE1hbmVqYXIgbm90aWZpY2FjaW9uZXMgc2Vnw7puIGVsIGVzdGFkb1xuICAgICAgICBpZiAodXBkYXRlcy5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJyAmJiB0YXNrLnN0YXR1cyA9PT0gJ3BlbmRpbmcnKSB7XG4gICAgICAgICAgdG9hc3QuZGlzbWlzcyhgdGFza19zdGFydF8ke2lkfWApO1xuICAgICAgICAgIHRvYXN0LmxvYWRpbmcoYFByb2Nlc2FuZG86ICR7dGFzay50aXRsZX1gLCB7XG4gICAgICAgICAgICBpZDogYHRhc2tfcHJvY2Vzc2luZ18ke2lkfWAsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSBpZiAodXBkYXRlcy5zdGF0dXMgPT09ICdjb21wbGV0ZWQnICYmIHRhc2suc3RhdHVzICE9PSAnY29tcGxldGVkJykge1xuICAgICAgICAgIHRvYXN0LmRpc21pc3MoYHRhc2tfcHJvY2Vzc2luZ18ke2lkfWApO1xuICAgICAgICAgIHRvYXN0LnN1Y2Nlc3MoYENvbXBsZXRhZG86ICR7dGFzay50aXRsZX1gLCB7XG4gICAgICAgICAgICBpZDogYHRhc2tfY29tcGxldGVkXyR7aWR9YCxcbiAgICAgICAgICAgIGR1cmF0aW9uOiA0MDAwLFxuICAgICAgICAgIH0pO1xuICAgICAgICAgIHVwZGF0ZWRUYXNrLmNvbXBsZXRlZEF0ID0gbmV3IERhdGUoKTtcbiAgICAgICAgfSBlbHNlIGlmICh1cGRhdGVzLnN0YXR1cyA9PT0gJ2Vycm9yJyAmJiB0YXNrLnN0YXR1cyAhPT0gJ2Vycm9yJykge1xuICAgICAgICAgIHRvYXN0LmRpc21pc3MoYHRhc2tfcHJvY2Vzc2luZ18ke2lkfWApO1xuICAgICAgICAgIHRvYXN0LmVycm9yKGBFcnJvcjogJHt0YXNrLnRpdGxlfWAsIHtcbiAgICAgICAgICAgIGlkOiBgdGFza19lcnJvcl8ke2lkfWAsXG4gICAgICAgICAgICBkdXJhdGlvbjogNTAwMCxcbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuXG4gICAgICAgIHJldHVybiB1cGRhdGVkVGFzaztcbiAgICAgIH1cbiAgICAgIHJldHVybiB0YXNrO1xuICAgIH0pKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IHJlbW92ZVRhc2sgPSB1c2VDYWxsYmFjaygoaWQ6IHN0cmluZykgPT4ge1xuICAgIHNldFRhc2tzKHByZXYgPT4gcHJldi5maWx0ZXIodGFzayA9PiB0YXNrLmlkICE9PSBpZCkpO1xuICAgIC8vIExpbXBpYXIgbm90aWZpY2FjaW9uZXMgcmVsYWNpb25hZGFzXG4gICAgdG9hc3QuZGlzbWlzcyhgdGFza19zdGFydF8ke2lkfWApO1xuICAgIHRvYXN0LmRpc21pc3MoYHRhc2tfcHJvY2Vzc2luZ18ke2lkfWApO1xuICAgIHRvYXN0LmRpc21pc3MoYHRhc2tfY29tcGxldGVkXyR7aWR9YCk7XG4gICAgdG9hc3QuZGlzbWlzcyhgdGFza19lcnJvcl8ke2lkfWApO1xuICB9LCBbXSk7XG5cbiAgY29uc3QgZ2V0VGFzayA9IHVzZUNhbGxiYWNrKChpZDogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIHRhc2tzLmZpbmQodGFzayA9PiB0YXNrLmlkID09PSBpZCk7XG4gIH0sIFt0YXNrc10pO1xuXG4gIGNvbnN0IGdldFRhc2tzQnlUeXBlID0gdXNlQ2FsbGJhY2soKHR5cGU6IEJhY2tncm91bmRUYXNrWyd0eXBlJ10pID0+IHtcbiAgICByZXR1cm4gdGFza3MuZmlsdGVyKHRhc2sgPT4gdGFzay50eXBlID09PSB0eXBlKTtcbiAgfSwgW3Rhc2tzXSk7XG5cbiAgY29uc3QgY2xlYXJDb21wbGV0ZWRUYXNrcyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBzZXRUYXNrcyhwcmV2ID0+IHByZXYuZmlsdGVyKHRhc2sgPT4gdGFzay5zdGF0dXMgIT09ICdjb21wbGV0ZWQnICYmIHRhc2suc3RhdHVzICE9PSAnZXJyb3InKSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBhY3RpdmVUYXNrcyA9IHVzZU1lbW8oKCkgPT4gdGFza3MuZmlsdGVyKHRhc2sgPT5cbiAgICB0YXNrLnN0YXR1cyA9PT0gJ3BlbmRpbmcnIHx8IHRhc2suc3RhdHVzID09PSAncHJvY2Vzc2luZydcbiAgKSwgW3Rhc2tzXSk7XG5cbiAgY29uc3QgY29tcGxldGVkVGFza3MgPSB1c2VNZW1vKCgpID0+IHRhc2tzLmZpbHRlcih0YXNrID0+XG4gICAgdGFzay5zdGF0dXMgPT09ICdjb21wbGV0ZWQnIHx8IHRhc2suc3RhdHVzID09PSAnZXJyb3InXG4gICksIFt0YXNrc10pO1xuXG4gIGNvbnN0IHZhbHVlOiBCYWNrZ3JvdW5kVGFza3NDb250ZXh0VHlwZSA9IHVzZU1lbW8oKCkgPT4gKHtcbiAgICB0YXNrcyxcbiAgICBhZGRUYXNrLFxuICAgIHVwZGF0ZVRhc2ssXG4gICAgcmVtb3ZlVGFzayxcbiAgICBnZXRUYXNrLFxuICAgIGdldFRhc2tzQnlUeXBlLFxuICAgIGNsZWFyQ29tcGxldGVkVGFza3MsXG4gICAgYWN0aXZlVGFza3MsXG4gICAgY29tcGxldGVkVGFza3MsXG4gIH0pLCBbdGFza3MsIGFkZFRhc2ssIHVwZGF0ZVRhc2ssIHJlbW92ZVRhc2ssIGdldFRhc2ssIGdldFRhc2tzQnlUeXBlLCBjbGVhckNvbXBsZXRlZFRhc2tzLCBhY3RpdmVUYXNrcywgY29tcGxldGVkVGFza3NdKTtcblxuICByZXR1cm4gKFxuICAgIDxCYWNrZ3JvdW5kVGFza3NDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9CYWNrZ3JvdW5kVGFza3NDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZUJhY2tncm91bmRUYXNrcyA9ICgpID0+IHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQmFja2dyb3VuZFRhc2tzQ29udGV4dCk7XG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZUJhY2tncm91bmRUYXNrcyBtdXN0IGJlIHVzZWQgd2l0aGluIGEgQmFja2dyb3VuZFRhc2tzUHJvdmlkZXInKTtcbiAgfVxuICByZXR1cm4gY29udGV4dDtcbn07XG4iXSwibmFtZXMiOlsiX3RvQ29uc3VtYWJsZUFycmF5IiwiX2RlZmluZVByb3BlcnR5IiwiX2pzeEZpbGVOYW1lIiwiX3RoaXMiLCJfcyIsIiRSZWZyZXNoU2lnJCIsIl9zMiIsIm93bktleXMiLCJlIiwiciIsInQiLCJPYmplY3QiLCJrZXlzIiwiZ2V0T3duUHJvcGVydHlTeW1ib2xzIiwibyIsImZpbHRlciIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsImVudW1lcmFibGUiLCJwdXNoIiwiYXBwbHkiLCJfb2JqZWN0U3ByZWFkIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiZm9yRWFjaCIsImdldE93blByb3BlcnR5RGVzY3JpcHRvcnMiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiZGVmaW5lUHJvcGVydHkiLCJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlU3RhdGUiLCJ1c2VDYWxsYmFjayIsInVzZU1lbW8iLCJ0b2FzdCIsImpzeERFViIsIl9qc3hERVYiLCJCYWNrZ3JvdW5kVGFza3NDb250ZXh0IiwidW5kZWZpbmVkIiwiQmFja2dyb3VuZFRhc2tzUHJvdmlkZXIiLCJfcmVmIiwiY2hpbGRyZW4iLCJfdXNlU3RhdGUiLCJ0YXNrcyIsInNldFRhc2tzIiwiYWRkVGFzayIsInRhc2tEYXRhIiwiaWQiLCJjb25jYXQiLCJEYXRlIiwibm93IiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwibmV3VGFzayIsInN0YXR1cyIsImNyZWF0ZWRBdCIsInByZXYiLCJsb2FkaW5nIiwidGl0bGUiLCJkdXJhdGlvbiIsInVwZGF0ZVRhc2siLCJ1cGRhdGVzIiwibWFwIiwidGFzayIsInVwZGF0ZWRUYXNrIiwiZGlzbWlzcyIsInN1Y2Nlc3MiLCJjb21wbGV0ZWRBdCIsImVycm9yIiwicmVtb3ZlVGFzayIsImdldFRhc2siLCJmaW5kIiwiZ2V0VGFza3NCeVR5cGUiLCJ0eXBlIiwiY2xlYXJDb21wbGV0ZWRUYXNrcyIsImFjdGl2ZVRhc2tzIiwiY29tcGxldGVkVGFza3MiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwiZmlsZU5hbWUiLCJsaW5lTnVtYmVyIiwiY29sdW1uTnVtYmVyIiwiX2MiLCJ1c2VCYWNrZ3JvdW5kVGFza3MiLCJjb250ZXh0IiwiRXJyb3IiLCIkUmVmcmVzaFJlZyQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/BackgroundTasksContext.tsx\n"));

/***/ })

});