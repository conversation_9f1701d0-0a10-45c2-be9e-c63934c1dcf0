{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|manifest.json|robots.txt|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Boe_EwkkkEiXVPZN8QoGl", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "204RUUtRLXHBYXmVyktehpqhhmihkVwQj1t8QzAwKtg=", "__NEXT_PREVIEW_MODE_ID": "caad5d562d543623a0b589f121e3885d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f87bb9ff390afc62684daa519e4e38de7eff8f65e5e3670f503e5ee20e8fba17", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ab4806ae48ad1f90f74a54271957f30de888d98acc5b7f4cc1020755d6fad63c"}}}, "functions": {}, "sortedMiddleware": ["/"]}