"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx":
/*!****************************************************************!*\
  !*** ./src/features/summaries/components/SummaryGenerator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryGenerator)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/resumenesService */ \"(app-pages-browser)/./src/lib/supabase/resumenesService.ts\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\summaries\\\\components\\\\SummaryGenerator.tsx\", _s1 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\n\nvar summarySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    instrucciones: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres')\n});\nfunction SummaryGenerator(_ref) {\n    _s();\n    _s1();\n    var documentosSeleccionados = _ref.documentosSeleccionados, onSummaryGenerated = _ref.onSummaryGenerated;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), resumenGenerado = _useState[0], setResumenGenerado = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), tiempoEstimado = _useState2[0], setTiempoEstimado = _useState2[1];\n    var _useBackgroundGenerat = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration)(), generateResumen = _useBackgroundGenerat.generateResumen, isGenerating = _useBackgroundGenerat.isGenerating, getActiveTask = _useBackgroundGenerat.getActiveTask;\n    var _useForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(summarySchema),\n        defaultValues: {\n            instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'\n        }\n    }), register = _useForm.register, handleSubmit = _useForm.handleSubmit, errors = _useForm.formState.errors, reset = _useForm.reset;\n    // Funciones de validación\n    var validarDocumentoParaResumen = function validarDocumentoParaResumen(documento) {\n        if (!documento) {\n            return {\n                valido: false,\n                error: 'No se ha proporcionado ningún documento'\n            };\n        }\n        if (!documento.titulo || documento.titulo.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener un título'\n            };\n        }\n        if (!documento.contenido || documento.contenido.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener contenido'\n            };\n        }\n        if (documento.contenido.trim().length < 50) {\n            return {\n                valido: false,\n                error: 'El contenido del documento es demasiado corto para generar un resumen útil'\n            };\n        }\n        return {\n            valido: true\n        };\n    };\n    var estimarTiempoGeneracion = function estimarTiempoGeneracion(documento) {\n        if (!documento || !documento.contenido) {\n            return 30; // 30 segundos por defecto\n        }\n        var palabras = documento.contenido.split(/\\s+/).length;\n        // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos\n        var tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));\n        return tiempoEstimado;\n    };\n    // Validaciones\n    var puedeGenerar = documentosSeleccionados.length === 1;\n    var documento = documentosSeleccionados[0];\n    var validacionDocumento = documento ? validarDocumentoParaResumen(documento) : {\n        valido: false,\n        error: 'No hay documento seleccionado'\n    };\n    // Estado de la tarea en segundo plano\n    var activeTask = getActiveTask('resumen');\n    var isCurrentlyGenerating = isGenerating('resumen');\n    // Observar estado de la tarea para mostrar resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SummaryGenerator.useEffect\": function() {\n            if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'completed' && activeTask.result) {\n                setResumenGenerado(activeTask.result);\n                setTiempoEstimado(0);\n            } else if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'error') {\n                setTiempoEstimado(0);\n            }\n        }\n    }[\"SummaryGenerator.useEffect\"], [\n        activeTask\n    ]);\n    var onSubmit = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2(data) {\n            var existe, errorMessage;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee2$(_context2) {\n                while(1)switch(_context2.prev = _context2.next){\n                    case 0:\n                        if (!(!puedeGenerar || !documento || !validacionDocumento.valido)) {\n                            _context2.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');\n                        return _context2.abrupt(\"return\");\n                    case 3:\n                        _context2.prev = 3;\n                        console.log('🚀 Iniciando generación de resumen...');\n                        setTiempoEstimado(estimarTiempoGeneracion(documento));\n                        // Verificar si ya existe un resumen para este documento\n                        console.log('🔍 Verificando si ya existe resumen...');\n                        _context2.next = 9;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.existeResumenParaDocumento)(documento.id);\n                    case 9:\n                        existe = _context2.sent;\n                        if (!existe) {\n                            _context2.next = 14;\n                            break;\n                        }\n                        console.log('⚠️ Ya existe un resumen para este documento');\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');\n                        return _context2.abrupt(\"return\");\n                    case 14:\n                        console.log('✅ No existe resumen previo, continuando...');\n                        // Usar el sistema de tareas en segundo plano\n                        _context2.next = 17;\n                        return generateResumen({\n                            documento: documento,\n                            instrucciones: data.instrucciones,\n                            onComplete: function() {\n                                var _onComplete = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee(resumenContent) {\n                                    var resumenId;\n                                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                                        while(1)switch(_context.prev = _context.next){\n                                            case 0:\n                                                console.log('✅ Resumen generado, guardando en Supabase...');\n                                                // Guardar en Supabase\n                                                _context.next = 3;\n                                                return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.guardarResumen)(documento.id, \"Resumen: \".concat(documento.titulo), resumenContent, data.instrucciones);\n                                            case 3:\n                                                resumenId = _context.sent;\n                                                if (resumenId) {\n                                                    console.log('✅ Resumen guardado exitosamente con ID:', resumenId);\n                                                    onSummaryGenerated === null || onSummaryGenerated === void 0 || onSummaryGenerated(resumenId);\n                                                    reset();\n                                                } else {\n                                                    console.error('❌ Error al guardar el resumen - no se recibió ID');\n                                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al guardar el resumen en la base de datos');\n                                                }\n                                            case 5:\n                                            case \"end\":\n                                                return _context.stop();\n                                        }\n                                    }, _callee);\n                                }));\n                                function onComplete(_x2) {\n                                    return _onComplete.apply(this, arguments);\n                                }\n                                return onComplete;\n                            }(),\n                            onError: function onError(error) {\n                                console.error('❌ Error en generación de resumen:', error);\n                                setTiempoEstimado(0);\n                            }\n                        });\n                    case 17:\n                        _context2.next = 25;\n                        break;\n                    case 19:\n                        _context2.prev = 19;\n                        _context2.t0 = _context2[\"catch\"](3);\n                        console.error('Error al iniciar generación de resumen:', _context2.t0);\n                        errorMessage = _context2.t0 instanceof Error ? _context2.t0.message : 'Error al generar el resumen';\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(errorMessage);\n                        setTiempoEstimado(0);\n                    case 25:\n                    case \"end\":\n                        return _context2.stop();\n                }\n            }, _callee2, null, [\n                [\n                    3,\n                    19\n                ]\n            ]);\n        }));\n        return function onSubmit(_x) {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCC4 Generaci\\xF3n de Res\\xFAmenes\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    !puedeGenerar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Selecci\\xF3n incorrecta\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: documentosSeleccionados.length === 0 ? 'Debes seleccionar exactamente un documento para generar un resumen.' : \"Tienes \".concat(documentosSeleccionados.length, \" documentos seleccionados. Solo se permite generar un resumen por tema.\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this) : !validacionDocumento.valido ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Documento no v\\xE1lido\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: validacionDocumento.error\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u2705 Documento seleccionado:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                        children: documento.titulo\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    documento.numero_tema && \" (Tema \".concat(documento.numero_tema, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-blue-600\",\n                                children: [\n                                    \"Contenido: ~\",\n                                    documento.contenido.split(/\\s+/).length,\n                                    \" palabras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            puedeGenerar && validacionDocumento.valido && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"label\", {\n                                htmlFor: \"instrucciones\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Instrucciones para el resumen:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"textarea\", _objectSpread(_objectSpread({\n                                id: \"instrucciones\"\n                            }, register('instrucciones')), {}, {\n                                disabled: isGenerating,\n                                rows: 4,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical\",\n                                placeholder: \"Describe c\\xF3mo quieres que sea el resumen...\"\n                            }), void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            errors.instrucciones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.instrucciones.message\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isGenerating,\n                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generando resumen...\",\n                                tiempoEstimado > 0 && \" (~\".concat(tiempoEstimado, \"s)\")\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generar Resumen\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                    children: \"\\u23F3 Generando resumen...\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 238,\n                                    columnNumber: 56\n                                }, this),\n                                \"La IA est\\xE1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar \",\n                                tiempoEstimado > 0 ? \"aproximadamente \".concat(tiempoEstimado, \" segundos\") : 'unos momentos',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 9\n            }, this),\n            resumenGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-3\",\n                        children: \"\\uD83D\\uDCCB Resumen Generado\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: resumenGenerado.replace(/\\n/g, '<br />')\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-2\",\n                        children: \"\\u2705 Resumen guardado exitosamente. Puedes acceder a \\xE9l desde la secci\\xF3n de res\\xFAmenes.\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(SummaryGenerator, \"/vRDUXc96KupzW4DGJlc//3RXKo=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c1 = SummaryGenerator;\n_s1(SummaryGenerator, \"PSDV3WtsHhTMSACB9kYGm0nxOZM=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SummaryGenerator;\nvar _c;\n$RefreshReg$(_c, \"SummaryGenerator\");\nvar _c1;\n$RefreshReg$(_c1, \"SummaryGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\n"));

/***/ })

});