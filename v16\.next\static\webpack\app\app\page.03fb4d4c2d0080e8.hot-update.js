"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TokenStatsModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenStatsModal)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiArrowUp,FiBarChart,FiLock,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase_tokenUsageService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/tokenUsageService */ \"(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts\");\n/* harmony import */ var _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePlanLimits */ \"(app-pages-browser)/./src/hooks/usePlanLimits.ts\");\n/* harmony import */ var _TokenProgressBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TokenProgressBar */ \"(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\");\n/* harmony import */ var _TokenPurchaseButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TokenPurchaseButton */ \"(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenStatsModal.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TokenStatsModal(_ref) {\n    _s();\n    _s1();\n    var _this = this;\n    var isOpen = _ref.isOpen, onClose = _ref.onClose, _ref$shouldRefreshOnO = _ref.shouldRefreshOnOpen, shouldRefreshOnOpen = _ref$shouldRefreshOnO === void 0 ? false : _ref$shouldRefreshOnO;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), stats = _useState[0], setStats = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), loading = _useState2[0], setLoading = _useState2[1];\n    var planLimits = (0,_hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits)();\n    // Refrescar datos cuando se abre el modal si es necesario\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && shouldRefreshOnOpen) {\n                planLimits.refresh();\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        shouldRefreshOnOpen,\n        planLimits\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && planLimits.userPlan && planLimits.userPlan !== 'free') {\n                loadStats();\n            } else if (isOpen && planLimits.userPlan === 'free') {\n                setStats(null);\n                setLoading(false);\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        planLimits.userPlan\n    ]);\n    var loadStats = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {\n            var currentStats;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        setLoading(true);\n                        _context.next = 4;\n                        return (0,_lib_supabase_tokenUsageService__WEBPACK_IMPORTED_MODULE_4__.getUserTokenStats)();\n                    case 4:\n                        currentStats = _context.sent;\n                        setStats(currentStats);\n                        _context.next = 11;\n                        break;\n                    case 8:\n                        _context.prev = 8;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error al cargar estadísticas:', _context.t0);\n                    case 11:\n                        _context.prev = 11;\n                        setLoading(false);\n                        return _context.finish(11);\n                    case 14:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    8,\n                    11,\n                    14\n                ]\n            ]);\n        }));\n        return function loadStats() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    if (!isOpen) return null;\n    var formatTokens = function formatTokens(tokens) {\n        return tokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Estad\\xEDsticas de Uso de IA\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiX, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: planLimits.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Verificando plan...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this) : planLimits.userPlan === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiLock, {\n                                className: \"mx-auto h-16 w-16 text-gray-400 mb-6\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Estad\\xEDsticas de Tokens no disponibles\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                            children: \"Plan Gratuito:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Las estad\\xEDsticas avanzadas y la compra de tokens est\\xE1n disponibles solo para usuarios con planes de pago.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Actualiza tu plan para acceder a:\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-700 space-y-2 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                                        className: \"w-4 h-4 text-blue-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Estad\\xEDsticas detalladas de uso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity, {\n                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"An\\xE1lisis por actividad y modelo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiArrowUp, {\n                                                        className: \"w-4 h-4 text-purple-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seguimiento de progreso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiArrowUp, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ver Planes Disponibles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this) : loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Cargando estad\\xEDsticas...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, this) : stats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                        children: [\n                            planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_TokenProgressBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    used: planLimits.tokenUsage.current || 0,\n                                    limit: planLimits.tokenUsage.limit || 0,\n                                    percentage: planLimits.tokenUsage.percentage || 0,\n                                    remaining: planLimits.tokenUsage.remaining || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 128,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 127,\n                                columnNumber: 17\n                            }, this),\n                            planLimits.tokenUsage && planLimits.tokenUsage.percentage >= 80 && planLimits.userPlan && planLimits.userPlan !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_TokenPurchaseButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    userPlan: planLimits.userPlan,\n                                    currentTokens: planLimits.tokenUsage.current,\n                                    tokenLimit: planLimits.tokenUsage.limit\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 140,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 139,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity, {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 152,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-blue-900\",\n                                                        children: \"Total Sesiones\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900 mt-1\",\n                                                children: stats.totalSessions\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-900\",\n                                                        children: \"Tokens Consumidos (Hist\\xF3rico)\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900 mt-1\",\n                                                children: formatTokens(stats.totalTokens)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Actividad\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Actividad\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byActivity).map(function(_ref3) {\n                                                        var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, 2), activity = _ref4[0], data = _ref4[1];\n                                                        var activityData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                    children: activity\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: activityData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(activityData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, activity, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Modelo\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Modelo\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byModel).map(function(_ref5) {\n                                                        var _ref6 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref5, 2), model = _ref6[0], data = _ref6[1];\n                                                        var modelData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900 font-mono\",\n                                                                    children: model\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: modelData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(modelData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, model, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                            children: \"Nota:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Los datos de uso de tokens se almacenan en Supabase. Los tokens de entrada y salida se registran autom\\xE1ticamente para cada actividad de IA.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 229,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No hay datos de uso disponibles.\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenStatsModal, \"WYDFYwW2tqIbkxDwNiPAn5eB8Ws=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits\n    ];\n});\n_c1 = TokenStatsModal;\n_s1(TokenStatsModal, \"1F00vAQqTrEahcnC0EvNidQjiF0=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits\n    ];\n});\n_c = TokenStatsModal;\nvar _c;\n$RefreshReg$(_c, \"TokenStatsModal\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenStatsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\n"));

/***/ })

});