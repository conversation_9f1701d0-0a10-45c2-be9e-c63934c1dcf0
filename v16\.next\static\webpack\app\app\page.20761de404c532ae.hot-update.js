"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/TokenPurchaseButton.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenPurchaseButton)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCreditCard,FiShoppingCart,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenPurchaseButton.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction TokenPurchaseButton(_ref) {\n    _s();\n    _s1();\n    var userPlan = _ref.userPlan, currentTokens = _ref.currentTokens, tokenLimit = _ref.tokenLimit;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), showModal = _useState[0], setShowModal = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), loading = _useState2[0], setLoading = _useState2[1];\n    // No mostrar para usuarios gratuitos\n    if (userPlan === 'free') {\n        return null;\n    }\n    var handlePurchase = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee() {\n            var response, data;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        setLoading(true);\n                        _context.next = 4;\n                        return fetch('/api/tokens/purchase', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                tokenAmount: 1000000,\n                                // 1 millón de tokens\n                                price: 10.00 // 10€\n                            })\n                        });\n                    case 4:\n                        response = _context.sent;\n                        _context.next = 7;\n                        return response.json();\n                    case 7:\n                        data = _context.sent;\n                        if (response.ok && data.url) {\n                            // Redirigir a Stripe Checkout\n                            window.location.href = data.url;\n                        } else {\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(data.error || 'Error al procesar la compra');\n                        }\n                        _context.next = 15;\n                        break;\n                    case 11:\n                        _context.prev = 11;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error en compra de tokens:', _context.t0);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Error al procesar la compra');\n                    case 15:\n                        _context.prev = 15;\n                        setLoading(false);\n                        return _context.finish(15);\n                    case 18:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    11,\n                    15,\n                    18\n                ]\n            ]);\n        }));\n        return function handlePurchase() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var formatTokens = function formatTokens(tokens) {\n        var validTokens = tokens || 0;\n        if (validTokens >= 1000000) {\n            return \"\".concat((validTokens / 1000000).toFixed(1), \"M\");\n        }\n        if (validTokens >= 1000) {\n            return \"\".concat((validTokens / 1000).toFixed(1), \"K\");\n        }\n        return validTokens.toLocaleString();\n    };\n    var usagePercentage = currentTokens / tokenLimit * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                onClick: function onClick() {\n                    return setShowModal(true);\n                },\n                className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors \".concat(usagePercentage >= 80 ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShoppingCart, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                        children: \"Comprar m\\xE1s tokens\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-xl max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCreditCard, {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Comprar Tokens Adicionales\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                                    onClick: function onClick() {\n                                        return setShowModal(false);\n                                    },\n                                    className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                        className: \"w-5 h-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-900 mb-2\",\n                                            children: \"Paquete de Tokens Adicionales\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Tokens incluidos:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"1,000,000 tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Precio:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"10,00\\u20AC (IVA incluido)\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Tu estado actual\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Tokens usados:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: formatTokens(currentTokens)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"L\\xEDmite actual:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: formatTokens(tokenLimit)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Nuevo l\\xEDmite:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatTokens(tokenLimit + 1000000)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-yellow-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"strong\", {\n                                                children: \"Nota:\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Los tokens adicionales se a\\xF1adir\\xE1n a tu l\\xEDmite mensual actual y estar\\xE1n disponibles inmediatamente despu\\xE9s del pago.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 p-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                                    onClick: function onClick() {\n                                        return setShowModal(false);\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                                    onClick: handlePurchase,\n                                    disabled: loading,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 169,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                children: \"Procesando...\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCreditCard, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                children: \"Comprar por 10,00\\u20AC\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TokenPurchaseButton, \"7qG96+1P/lMw9kHgPV3rHMBOAxg=\");\n_c1 = TokenPurchaseButton;\n_s1(TokenPurchaseButton, \"uLG0UpugcsSeHijU9qaylZ9u1IA=\");\n_c = TokenPurchaseButton;\nvar _c;\n$RefreshReg$(_c, \"TokenPurchaseButton\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenPurchaseButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx\n"));

/***/ })

});