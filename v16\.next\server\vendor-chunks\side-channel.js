"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel";
exports.ids = ["vendor-chunks/side-channel"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel/index.js":
/*!********************************************!*\
  !*** ./node_modules/side-channel/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelList = __webpack_require__(/*! side-channel-list */ \"(rsc)/./node_modules/side-channel-list/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar getSideChannelWeakMap = __webpack_require__(/*! side-channel-weakmap */ \"(rsc)/./node_modules/side-channel-weakmap/index.js\");\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannel() {\n  /** @typedef {ReturnType<typeof getSideChannel>} Channel */\n\n  /** @type {Channel | undefined} */var $channelData;\n\n  /** @type {Channel} */\n  var channel = {\n    assert: function (key) {\n      if (!channel.has(key)) {\n        throw new $TypeError('Side channel does not contain ' + inspect(key));\n      }\n    },\n    'delete': function (key) {\n      return !!$channelData && $channelData['delete'](key);\n    },\n    get: function (key) {\n      return $channelData && $channelData.get(key);\n    },\n    has: function (key) {\n      return !!$channelData && $channelData.has(key);\n    },\n    set: function (key, value) {\n      if (!$channelData) {\n        $channelData = makeChannel();\n      }\n      $channelData.set(key, value);\n    }\n  };\n  // @ts-expect-error TODO: figure out why this is erroring\n  return channel;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc2lkZS1jaGFubmVsL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUlBLFVBQVUsR0FBR0MsbUJBQU8sQ0FBQyw4REFBZ0IsQ0FBQztBQUMxQyxJQUFJQyxPQUFPLEdBQUdELG1CQUFPLENBQUMsb0VBQWdCLENBQUM7QUFDdkMsSUFBSUUsa0JBQWtCLEdBQUdGLG1CQUFPLENBQUMsMEVBQW1CLENBQUM7QUFDckQsSUFBSUcsaUJBQWlCLEdBQUdILG1CQUFPLENBQUMsd0VBQWtCLENBQUM7QUFDbkQsSUFBSUkscUJBQXFCLEdBQUdKLG1CQUFPLENBQUMsZ0ZBQXNCLENBQUM7QUFFM0QsSUFBSUssV0FBVyxHQUFHRCxxQkFBcUIsSUFBSUQsaUJBQWlCLElBQUlELGtCQUFrQjs7QUFFbEY7QUFDQUksTUFBTSxDQUFDQyxPQUFPLEdBQUcsU0FBU0MsY0FBY0EsQ0FBQSxFQUFHO0VBQzFDOztFQUVBLGtDQUFtQyxJQUFJQyxZQUFZOztFQUVuRDtFQUNBLElBQUlDLE9BQU8sR0FBRztJQUNiQyxNQUFNLEVBQUUsU0FBQUEsQ0FBVUMsR0FBRyxFQUFFO01BQ3RCLElBQUksQ0FBQ0YsT0FBTyxDQUFDRyxHQUFHLENBQUNELEdBQUcsQ0FBQyxFQUFFO1FBQ3RCLE1BQU0sSUFBSWIsVUFBVSxDQUFDLGdDQUFnQyxHQUFHRSxPQUFPLENBQUNXLEdBQUcsQ0FBQyxDQUFDO01BQ3RFO0lBQ0QsQ0FBQztJQUNELFFBQVEsRUFBRSxTQUFBRSxDQUFVRixHQUFHLEVBQUU7TUFDeEIsT0FBTyxDQUFDLENBQUNILFlBQVksSUFBSUEsWUFBWSxDQUFDLFFBQVEsQ0FBQyxDQUFDRyxHQUFHLENBQUM7SUFDckQsQ0FBQztJQUNERyxHQUFHLEVBQUUsU0FBQUEsQ0FBVUgsR0FBRyxFQUFFO01BQ25CLE9BQU9ILFlBQVksSUFBSUEsWUFBWSxDQUFDTSxHQUFHLENBQUNILEdBQUcsQ0FBQztJQUM3QyxDQUFDO0lBQ0RDLEdBQUcsRUFBRSxTQUFBQSxDQUFVRCxHQUFHLEVBQUU7TUFDbkIsT0FBTyxDQUFDLENBQUNILFlBQVksSUFBSUEsWUFBWSxDQUFDSSxHQUFHLENBQUNELEdBQUcsQ0FBQztJQUMvQyxDQUFDO0lBQ0RJLEdBQUcsRUFBRSxTQUFBQSxDQUFVSixHQUFHLEVBQUVLLEtBQUssRUFBRTtNQUMxQixJQUFJLENBQUNSLFlBQVksRUFBRTtRQUNsQkEsWUFBWSxHQUFHSixXQUFXLENBQUMsQ0FBQztNQUM3QjtNQUVBSSxZQUFZLENBQUNPLEdBQUcsQ0FBQ0osR0FBRyxFQUFFSyxLQUFLLENBQUM7SUFDN0I7RUFDRCxDQUFDO0VBQ0Q7RUFDQSxPQUFPUCxPQUFPO0FBQ2YsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxub2RlX21vZHVsZXNcXHNpZGUtY2hhbm5lbFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG52YXIgJFR5cGVFcnJvciA9IHJlcXVpcmUoJ2VzLWVycm9ycy90eXBlJyk7XG52YXIgaW5zcGVjdCA9IHJlcXVpcmUoJ29iamVjdC1pbnNwZWN0Jyk7XG52YXIgZ2V0U2lkZUNoYW5uZWxMaXN0ID0gcmVxdWlyZSgnc2lkZS1jaGFubmVsLWxpc3QnKTtcbnZhciBnZXRTaWRlQ2hhbm5lbE1hcCA9IHJlcXVpcmUoJ3NpZGUtY2hhbm5lbC1tYXAnKTtcbnZhciBnZXRTaWRlQ2hhbm5lbFdlYWtNYXAgPSByZXF1aXJlKCdzaWRlLWNoYW5uZWwtd2Vha21hcCcpO1xuXG52YXIgbWFrZUNoYW5uZWwgPSBnZXRTaWRlQ2hhbm5lbFdlYWtNYXAgfHwgZ2V0U2lkZUNoYW5uZWxNYXAgfHwgZ2V0U2lkZUNoYW5uZWxMaXN0O1xuXG4vKiogQHR5cGUge2ltcG9ydCgnLicpfSAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBnZXRTaWRlQ2hhbm5lbCgpIHtcblx0LyoqIEB0eXBlZGVmIHtSZXR1cm5UeXBlPHR5cGVvZiBnZXRTaWRlQ2hhbm5lbD59IENoYW5uZWwgKi9cblxuXHQvKiogQHR5cGUge0NoYW5uZWwgfCB1bmRlZmluZWR9ICovIHZhciAkY2hhbm5lbERhdGE7XG5cblx0LyoqIEB0eXBlIHtDaGFubmVsfSAqL1xuXHR2YXIgY2hhbm5lbCA9IHtcblx0XHRhc3NlcnQ6IGZ1bmN0aW9uIChrZXkpIHtcblx0XHRcdGlmICghY2hhbm5lbC5oYXMoa2V5KSkge1xuXHRcdFx0XHR0aHJvdyBuZXcgJFR5cGVFcnJvcignU2lkZSBjaGFubmVsIGRvZXMgbm90IGNvbnRhaW4gJyArIGluc3BlY3Qoa2V5KSk7XG5cdFx0XHR9XG5cdFx0fSxcblx0XHQnZGVsZXRlJzogZnVuY3Rpb24gKGtleSkge1xuXHRcdFx0cmV0dXJuICEhJGNoYW5uZWxEYXRhICYmICRjaGFubmVsRGF0YVsnZGVsZXRlJ10oa2V5KTtcblx0XHR9LFxuXHRcdGdldDogZnVuY3Rpb24gKGtleSkge1xuXHRcdFx0cmV0dXJuICRjaGFubmVsRGF0YSAmJiAkY2hhbm5lbERhdGEuZ2V0KGtleSk7XG5cdFx0fSxcblx0XHRoYXM6IGZ1bmN0aW9uIChrZXkpIHtcblx0XHRcdHJldHVybiAhISRjaGFubmVsRGF0YSAmJiAkY2hhbm5lbERhdGEuaGFzKGtleSk7XG5cdFx0fSxcblx0XHRzZXQ6IGZ1bmN0aW9uIChrZXksIHZhbHVlKSB7XG5cdFx0XHRpZiAoISRjaGFubmVsRGF0YSkge1xuXHRcdFx0XHQkY2hhbm5lbERhdGEgPSBtYWtlQ2hhbm5lbCgpO1xuXHRcdFx0fVxuXG5cdFx0XHQkY2hhbm5lbERhdGEuc2V0KGtleSwgdmFsdWUpO1xuXHRcdH1cblx0fTtcblx0Ly8gQHRzLWV4cGVjdC1lcnJvciBUT0RPOiBmaWd1cmUgb3V0IHdoeSB0aGlzIGlzIGVycm9yaW5nXG5cdHJldHVybiBjaGFubmVsO1xufTtcbiJdLCJuYW1lcyI6WyIkVHlwZUVycm9yIiwicmVxdWlyZSIsImluc3BlY3QiLCJnZXRTaWRlQ2hhbm5lbExpc3QiLCJnZXRTaWRlQ2hhbm5lbE1hcCIsImdldFNpZGVDaGFubmVsV2Vha01hcCIsIm1ha2VDaGFubmVsIiwibW9kdWxlIiwiZXhwb3J0cyIsImdldFNpZGVDaGFubmVsIiwiJGNoYW5uZWxEYXRhIiwiY2hhbm5lbCIsImFzc2VydCIsImtleSIsImhhcyIsImRlbGV0ZSIsImdldCIsInNldCIsInZhbHVlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel/index.js\n");

/***/ })

};
;