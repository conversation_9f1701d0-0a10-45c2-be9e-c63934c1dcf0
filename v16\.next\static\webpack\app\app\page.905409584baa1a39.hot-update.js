"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TokenStatsModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenStatsModal)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/usePlanLimits */ \"(app-pages-browser)/./src/hooks/usePlanLimits.ts\");\n/* harmony import */ var _TokenProgressBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./TokenProgressBar */ \"(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenStatsModal.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction TokenStatsModal(_ref) {\n    _s();\n    _s1();\n    var isOpen = _ref.isOpen, onClose = _ref.onClose, _ref$shouldRefreshOnO = _ref.shouldRefreshOnOpen, shouldRefreshOnOpen = _ref$shouldRefreshOnO === void 0 ? false : _ref$shouldRefreshOnO;\n    var planLimits = (0,_hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_1__.usePlanLimits)();\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiBarChart, {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Estad\\xEDsticas de Uso de IA\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: planLimits.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 41,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Verificando plan...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 42,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 40,\n                        columnNumber: 13\n                    }, this) : planLimits.userPlan === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Estad\\xEDsticas de Tokens no disponibles\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 46,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: \"Las estad\\xEDsticas detalladas est\\xE1n disponibles para planes de pago.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 49,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\",\n                                    children: \"Ver Planes Disponibles\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 53,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 52,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 45,\n                        columnNumber: 13\n                    }, this) : planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_TokenProgressBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            used: planLimits.tokenUsage.current || 0,\n                            limit: planLimits.tokenUsage.limit || 0,\n                            percentage: planLimits.tokenUsage.percentage || 0,\n                            remaining: planLimits.tokenUsage.remaining || 0\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 63,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No hay datos de uso disponibles.\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 72,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenStatsModal, \"3T7lKQz+yklv2M+OFaxFVArNDzI=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_1__.usePlanLimits\n    ];\n});\n_c1 = TokenStatsModal;\n_s1(TokenStatsModal, \"3T7lKQz+yklv2M+OFaxFVArNDzI=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_1__.usePlanLimits\n    ];\n});\n_c = TokenStatsModal;\nvar _c;\n$RefreshReg$(_c, \"TokenStatsModal\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenStatsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\n"));

/***/ })

});