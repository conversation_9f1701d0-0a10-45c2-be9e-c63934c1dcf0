"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/TokenProgressBar.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenProgressBar)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenProgressBar.tsx\";\n\n\nfunction TokenProgressBar(_ref) {\n    var used = _ref.used, limit = _ref.limit, percentage = _ref.percentage, remaining = _ref.remaining;\n    // Determinar color según el porcentaje de uso\n    var getProgressColor = function getProgressColor(percentage) {\n        if (percentage < 50) return 'bg-green-500';\n        if (percentage < 80) return 'bg-yellow-500';\n        return 'bg-red-500';\n    };\n    var getProgressBgColor = function getProgressBgColor(percentage) {\n        if (percentage < 50) return 'bg-green-100';\n        if (percentage < 80) return 'bg-yellow-100';\n        return 'bg-red-100';\n    };\n    var formatTokens = function formatTokens(tokens) {\n        if (tokens >= 1000000) {\n            return \"\".concat((tokens / 1000000).toFixed(1), \"M\");\n        }\n        if (tokens >= 1000) {\n            return \"\".concat((tokens / 1000).toFixed(1), \"K\");\n        }\n        return tokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Uso de Tokens\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-semibold \".concat(percentage < 50 ? 'text-green-600' : percentage < 80 ? 'text-yellow-600' : 'text-red-600'),\n                        children: [\n                            percentage,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"w-full \".concat(getProgressBgColor(percentage), \" rounded-full h-3\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"h-3 rounded-full transition-all duration-300 \".concat(getProgressColor(percentage)),\n                    style: {\n                        width: \"\".concat(Math.min(percentage, 100), \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-xs text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"strong\", {\n                                children: formatTokens(used)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            \" usados\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"strong\", {\n                                children: formatTokens(remaining)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            \" restantes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"L\\xEDmite mensual: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"strong\", {\n                            children: formatTokens(limit)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 71,\n                            columnNumber: 27\n                        }, this),\n                        \" tokens\"\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            percentage >= 80 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"p-2 rounded-lg text-xs \".concat(percentage >= 95 ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-yellow-50 text-yellow-700 border border-yellow-200'),\n                children: percentage >= 95 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                    children: \"\\u26A0\\uFE0F L\\xEDmite casi alcanzado. Considera comprar m\\xE1s tokens.\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 82,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                    children: \"\\u26A0\\uFE0F Te est\\xE1s acercando al l\\xEDmite mensual de tokens.\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 84,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_c1 = TokenProgressBar;\n_c = TokenProgressBar;\nvar _c;\n$RefreshReg$(_c, \"TokenProgressBar\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenProgressBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\n"));

/***/ })

});