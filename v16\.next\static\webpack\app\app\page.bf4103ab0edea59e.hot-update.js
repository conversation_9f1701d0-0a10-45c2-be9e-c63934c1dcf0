"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx":
/*!***************************************************!*\
  !*** ./src/components/ui/TokenPurchaseButton.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenPurchaseButton)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiCreditCard,FiShoppingCart,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenPurchaseButton.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction TokenPurchaseButton(_ref) {\n    _s();\n    _s1();\n    var userPlan = _ref.userPlan, currentTokens = _ref.currentTokens, tokenLimit = _ref.tokenLimit;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), showModal = _useState[0], setShowModal = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), loading = _useState2[0], setLoading = _useState2[1];\n    // No mostrar para usuarios gratuitos\n    if (userPlan === 'free') {\n        return null;\n    }\n    var handlePurchase = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee() {\n            var response, data;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        setLoading(true);\n                        _context.next = 4;\n                        return fetch('/api/tokens/purchase', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                tokenAmount: 1000000,\n                                // 1 millón de tokens\n                                price: 10.00 // 10€\n                            })\n                        });\n                    case 4:\n                        response = _context.sent;\n                        _context.next = 7;\n                        return response.json();\n                    case 7:\n                        data = _context.sent;\n                        if (response.ok && data.url) {\n                            // Redirigir a Stripe Checkout\n                            window.location.href = data.url;\n                        } else {\n                            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error(data.error || 'Error al procesar la compra');\n                        }\n                        _context.next = 15;\n                        break;\n                    case 11:\n                        _context.prev = 11;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error en compra de tokens:', _context.t0);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"].error('Error al procesar la compra');\n                    case 15:\n                        _context.prev = 15;\n                        setLoading(false);\n                        return _context.finish(15);\n                    case 18:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    11,\n                    15,\n                    18\n                ]\n            ]);\n        }));\n        return function handlePurchase() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    var formatTokens = function formatTokens(tokens) {\n        var validTokens = tokens || 0;\n        if (validTokens >= 1000000) {\n            return \"\".concat((validTokens / 1000000).toFixed(1), \"M\");\n        }\n        if (validTokens >= 1000) {\n            return \"\".concat((validTokens / 1000).toFixed(1), \"K\");\n        }\n        return validTokens.toLocaleString();\n    };\n    var usagePercentage = tokenLimit > 0 ? currentTokens / tokenLimit * 100 : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                onClick: function onClick() {\n                    return setShowModal(true);\n                },\n                className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors \".concat(usagePercentage >= 80 ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiShoppingCart, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                        children: \"Comprar m\\xE1s tokens\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            showModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-xl max-w-md w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCreditCard, {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Comprar Tokens Adicionales\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 91,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                                    onClick: function onClick() {\n                                        return setShowModal(false);\n                                    },\n                                    className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                        className: \"w-5 h-5 text-gray-500\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-blue-900 mb-2\",\n                                            children: \"Paquete de Tokens Adicionales\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-sm text-blue-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Tokens incluidos:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 114,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"1,000,000 tokens\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Precio:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 118,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold\",\n                                                            children: \"10,00\\u20AC (IVA incluido)\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-gray-900 mb-2\",\n                                            children: \"Tu estado actual\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1 text-sm text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Tokens usados:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 129,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: formatTokens(currentTokens)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 130,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"L\\xEDmite actual:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 133,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: formatTokens(tokenLimit)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 132,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            children: \"Nuevo l\\xEDmite:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600\",\n                                                            children: formatTokens(tokenLimit + 1000000)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-yellow-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"strong\", {\n                                                children: \"Nota:\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Los tokens adicionales se a\\xF1adir\\xE1n a tu l\\xEDmite mensual actual y estar\\xE1n disponibles inmediatamente despu\\xE9s del pago.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 p-6 border-t border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                                    onClick: function onClick() {\n                                        return setShowModal(false);\n                                    },\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 156,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"button\", {\n                                    onClick: handlePurchase,\n                                    disabled: loading,\n                                    className: \"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2\",\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 169,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                children: \"Procesando...\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 170,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(_barrel_optimize_names_FiCreditCard_FiShoppingCart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCreditCard, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxDEV)(\"span\", {\n                                                children: \"Comprar por 10,00\\u20AC\"\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 162,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 155,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(TokenPurchaseButton, \"7qG96+1P/lMw9kHgPV3rHMBOAxg=\");\n_c1 = TokenPurchaseButton;\n_s1(TokenPurchaseButton, \"uLG0UpugcsSeHijU9qaylZ9u1IA=\");\n_c = TokenPurchaseButton;\nvar _c;\n$RefreshReg$(_c, \"TokenPurchaseButton\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenPurchaseButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx\n"));

/***/ })

});