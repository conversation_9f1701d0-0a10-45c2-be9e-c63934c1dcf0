"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx":
/*!****************************************************************!*\
  !*** ./src/features/summaries/components/SummaryGenerator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryGenerator)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/resumenesService */ \"(app-pages-browser)/./src/lib/supabase/resumenesService.ts\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\summaries\\\\components\\\\SummaryGenerator.tsx\", _s1 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\n\nvar summarySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    instrucciones: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres')\n});\nfunction SummaryGenerator(_ref) {\n    _s();\n    _s1();\n    var documentosSeleccionados = _ref.documentosSeleccionados, onSummaryGenerated = _ref.onSummaryGenerated;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), resumenGenerado = _useState[0], setResumenGenerado = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), tiempoEstimado = _useState2[0], setTiempoEstimado = _useState2[1];\n    var _useBackgroundGenerat = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration)(), generateResumen = _useBackgroundGenerat.generateResumen, isGenerating = _useBackgroundGenerat.isGenerating, getActiveTask = _useBackgroundGenerat.getActiveTask;\n    var _useForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(summarySchema),\n        defaultValues: {\n            instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'\n        }\n    }), register = _useForm.register, handleSubmit = _useForm.handleSubmit, errors = _useForm.formState.errors, reset = _useForm.reset;\n    // Funciones de validación\n    var validarDocumentoParaResumen = function validarDocumentoParaResumen(documento) {\n        if (!documento) {\n            return {\n                valido: false,\n                error: 'No se ha proporcionado ningún documento'\n            };\n        }\n        if (!documento.titulo || documento.titulo.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener un título'\n            };\n        }\n        if (!documento.contenido || documento.contenido.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener contenido'\n            };\n        }\n        if (documento.contenido.trim().length < 50) {\n            return {\n                valido: false,\n                error: 'El contenido del documento es demasiado corto para generar un resumen útil'\n            };\n        }\n        return {\n            valido: true\n        };\n    };\n    var estimarTiempoGeneracion = function estimarTiempoGeneracion(documento) {\n        if (!documento || !documento.contenido) {\n            return 30; // 30 segundos por defecto\n        }\n        var palabras = documento.contenido.split(/\\s+/).length;\n        // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos\n        var tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));\n        return tiempoEstimado;\n    };\n    // Validaciones\n    var puedeGenerar = documentosSeleccionados.length === 1;\n    var documento = documentosSeleccionados[0];\n    var validacionDocumento = documento ? validarDocumentoParaResumen(documento) : {\n        valido: false,\n        error: 'No hay documento seleccionado'\n    };\n    // Estado de la tarea en segundo plano\n    var activeTask = getActiveTask('resumen');\n    var isCurrentlyGenerating = isGenerating('resumen');\n    // Observar estado de la tarea para mostrar resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SummaryGenerator.useEffect\": function() {\n            if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'completed' && activeTask.result) {\n                setResumenGenerado(activeTask.result);\n                setTiempoEstimado(0);\n            } else if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'error') {\n                setTiempoEstimado(0);\n            }\n        }\n    }[\"SummaryGenerator.useEffect\"], [\n        activeTask\n    ]);\n    var onSubmit = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee(data) {\n            var _documento$contenido, _requestData$contexto, _result$result, _result$result2, existe, requestData, response, responseText, result, resumenContent, resumenId, errorMessage, errorObj;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        if (!(!puedeGenerar || !documento || !validacionDocumento.valido)) {\n                            _context.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');\n                        return _context.abrupt(\"return\");\n                    case 3:\n                        _context.prev = 3;\n                        console.log('🚀 Iniciando generación de resumen...');\n                        setIsGenerating(true);\n                        setTiempoEstimado(estimarTiempoGeneracion(documento));\n                        console.log('📄 Documento seleccionado:', {\n                            id: documento.id,\n                            titulo: documento.titulo,\n                            categoria: documento.categoria,\n                            numero_tema: documento.numero_tema,\n                            contenidoLength: ((_documento$contenido = documento.contenido) === null || _documento$contenido === void 0 ? void 0 : _documento$contenido.length) || 0\n                        });\n                        // Verificar si ya existe un resumen para este documento\n                        console.log('🔍 Verificando si ya existe resumen...');\n                        _context.next = 11;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.existeResumenParaDocumento)(documento.id);\n                    case 11:\n                        existe = _context.sent;\n                        if (!existe) {\n                            _context.next = 17;\n                            break;\n                        }\n                        console.log('⚠️ Ya existe un resumen para este documento');\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');\n                        setIsGenerating(false);\n                        return _context.abrupt(\"return\");\n                    case 17:\n                        console.log('✅ No existe resumen previo, continuando...');\n                        // Preparar datos para la API\n                        requestData = {\n                            action: 'generarResumen',\n                            peticion: \"\".concat(documento.titulo, \"|\").concat(documento.categoria || '', \"|\").concat(documento.numero_tema || '', \"|\").concat(data.instrucciones),\n                            contextos: [\n                                documento.contenido\n                            ]\n                        };\n                        console.log('📡 Enviando petición a la API:', {\n                            action: requestData.action,\n                            peticion: requestData.peticion,\n                            contextosLength: requestData.contextos.length,\n                            primerContextoLength: ((_requestData$contexto = requestData.contextos[0]) === null || _requestData$contexto === void 0 ? void 0 : _requestData$contexto.length) || 0\n                        });\n                        // Generar el resumen usando la API\n                        _context.next = 22;\n                        return fetch('/api/ai', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify(requestData)\n                        });\n                    case 22:\n                        response = _context.sent;\n                        console.log(\"\\uD83D\\uDCE8 Respuesta recibida - Status: \".concat(response.status, \", OK: \").concat(response.ok));\n                        // Capturar respuesta como texto primero para depuración\n                        _context.next = 26;\n                        return response.text();\n                    case 26:\n                        responseText = _context.sent;\n                        console.log('📄 Respuesta como texto (primeros 200 chars):', responseText.substring(0, 200));\n                        // Intentar parsear como JSON\n                        _context.prev = 28;\n                        result = JSON.parse(responseText);\n                        console.log('✅ JSON parseado exitosamente');\n                        _context.next = 38;\n                        break;\n                    case 33:\n                        _context.prev = 33;\n                        _context.t0 = _context[\"catch\"](28);\n                        console.error('❌ Error al parsear respuesta JSON:', _context.t0);\n                        console.error('📄 Respuesta completa:', responseText);\n                        throw new Error(\"Error en formato de respuesta: \".concat(responseText.substring(0, 100), \"...\"));\n                    case 38:\n                        if (response.ok) {\n                            _context.next = 41;\n                            break;\n                        }\n                        console.error('❌ Error en respuesta de la API:', result);\n                        throw new Error(result.detalles || result.error || 'Error al generar el resumen');\n                    case 41:\n                        console.log('📋 Resultado de la API:', {\n                            hasResult: !!result.result,\n                            resultLength: ((_result$result = result.result) === null || _result$result === void 0 ? void 0 : _result$result.length) || 0,\n                            resultPreview: ((_result$result2 = result.result) === null || _result$result2 === void 0 ? void 0 : _result$result2.substring(0, 100)) || 'Sin contenido'\n                        });\n                        resumenContent = result.result;\n                        if (resumenContent) {\n                            _context.next = 46;\n                            break;\n                        }\n                        console.error('❌ No se recibió contenido del resumen');\n                        throw new Error('No se recibió contenido del resumen');\n                    case 46:\n                        console.log('✅ Contenido del resumen recibido, longitud:', resumenContent.length);\n                        setResumenGenerado(resumenContent);\n                        // Guardar en Supabase\n                        console.log('💾 Guardando resumen en Supabase...');\n                        _context.next = 51;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.guardarResumen)(documento.id, \"Resumen: \".concat(documento.titulo), resumenContent, data.instrucciones);\n                    case 51:\n                        resumenId = _context.sent;\n                        if (!resumenId) {\n                            _context.next = 59;\n                            break;\n                        }\n                        console.log('✅ Resumen guardado exitosamente con ID:', resumenId);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success('Resumen generado y guardado exitosamente');\n                        onSummaryGenerated === null || onSummaryGenerated === void 0 || onSummaryGenerated(resumenId);\n                        reset();\n                        _context.next = 61;\n                        break;\n                    case 59:\n                        console.error('❌ Error al guardar el resumen - no se recibió ID');\n                        throw new Error('Error al guardar el resumen en la base de datos');\n                    case 61:\n                        _context.next = 70;\n                        break;\n                    case 63:\n                        _context.prev = 63;\n                        _context.t1 = _context[\"catch\"](3);\n                        console.error('Error completo al generar resumen:', _context.t1);\n                        // Manejo mejorado de errores con más detalles\n                        errorMessage = 'Error al generar el resumen';\n                        if (_context.t1 instanceof Error) {\n                            errorMessage = _context.t1.message;\n                        } else if (typeof _context.t1 === 'object' && _context.t1 !== null) {\n                            // Si es un objeto, intentar extraer información útil\n                            errorObj = _context.t1;\n                            if (errorObj.message) {\n                                errorMessage = errorObj.message;\n                            } else if (errorObj.error) {\n                                errorMessage = errorObj.error;\n                            } else if (errorObj.detalles) {\n                                errorMessage = errorObj.detalles;\n                            } else {\n                                errorMessage = \"Error del servidor: \".concat(JSON.stringify(_context.t1));\n                            }\n                        } else if (typeof _context.t1 === 'string') {\n                            errorMessage = _context.t1;\n                        }\n                        console.error('Mensaje de error procesado:', errorMessage);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(errorMessage);\n                    case 70:\n                        _context.prev = 70;\n                        setIsGenerating(false);\n                        setTiempoEstimado(0);\n                        return _context.finish(70);\n                    case 74:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    3,\n                    63,\n                    70,\n                    74\n                ],\n                [\n                    28,\n                    33\n                ]\n            ]);\n        }));\n        return function onSubmit(_x) {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCC4 Generaci\\xF3n de Res\\xFAmenes\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    !puedeGenerar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Selecci\\xF3n incorrecta\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: documentosSeleccionados.length === 0 ? 'Debes seleccionar exactamente un documento para generar un resumen.' : \"Tienes \".concat(documentosSeleccionados.length, \" documentos seleccionados. Solo se permite generar un resumen por tema.\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, this) : !validacionDocumento.valido ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Documento no v\\xE1lido\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 254,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: validacionDocumento.error\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u2705 Documento seleccionado:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 259,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                        children: documento.titulo\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    documento.numero_tema && \" (Tema \".concat(documento.numero_tema, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 260,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-blue-600\",\n                                children: [\n                                    \"Contenido: ~\",\n                                    documento.contenido.split(/\\s+/).length,\n                                    \" palabras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            puedeGenerar && validacionDocumento.valido && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"label\", {\n                                htmlFor: \"instrucciones\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Instrucciones para el resumen:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"textarea\", _objectSpread(_objectSpread({\n                                id: \"instrucciones\"\n                            }, register('instrucciones')), {}, {\n                                disabled: isGenerating,\n                                rows: 4,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical\",\n                                placeholder: \"Describe c\\xF3mo quieres que sea el resumen...\"\n                            }), void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this),\n                            errors.instrucciones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.instrucciones.message\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isGenerating,\n                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generando resumen...\",\n                                tiempoEstimado > 0 && \" (~\".concat(tiempoEstimado, \"s)\")\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 311,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 312,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 310,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generar Resumen\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this),\n                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                    children: \"\\u23F3 Generando resumen...\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 322,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 322,\n                                    columnNumber: 56\n                                }, this),\n                                \"La IA est\\xE1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar \",\n                                tiempoEstimado > 0 ? \"aproximadamente \".concat(tiempoEstimado, \" segundos\") : 'unos momentos',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 321,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 9\n            }, this),\n            resumenGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-3\",\n                        children: \"\\uD83D\\uDCCB Resumen Generado\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: resumenGenerado.replace(/\\n/g, '<br />')\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 336,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-2\",\n                        children: \"\\u2705 Resumen guardado exitosamente. Puedes acceder a \\xE9l desde la secci\\xF3n de res\\xFAmenes.\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(SummaryGenerator, \"/vRDUXc96KupzW4DGJlc//3RXKo=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c1 = SummaryGenerator;\n_s1(SummaryGenerator, \"PSDV3WtsHhTMSACB9kYGm0nxOZM=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SummaryGenerator;\nvar _c;\n$RefreshReg$(_c, \"SummaryGenerator\");\nvar _c1;\n$RefreshReg$(_c1, \"SummaryGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\n"));

/***/ })

});