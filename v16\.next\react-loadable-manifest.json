{"..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": 49539, "files": []}, "..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch": {"id": 49539, "files": []}, "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch": {"id": 49539, "files": []}, "..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> ws": {"id": 72715, "files": ["static/chunks/2715.db73043d342e9266.js"]}, "..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": 49539, "files": []}, "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": 43465, "files": ["static/chunks/3465.ffc7a364a4396367.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_app": {"id": 15601, "files": ["static/chunks/5601.31c1dea1c6e18b31.js"]}, "..\\node_modules\\next\\dist\\client\\index.js -> ../pages/_error": {"id": 73708, "files": ["static/chunks/1327.d88f7cf0c8ef8706.js"]}, "lib\\services\\limitHandler.ts -> @/lib/supabase/admin": {"id": 72024, "files": ["static/chunks/2024.bc7066f9d71fba2d.js"]}, "lib\\services\\permissionService.ts -> @/lib/supabase/admin": {"id": 72024, "files": ["static/chunks/2024.bc7066f9d71fba2d.js"]}, "lib\\utils\\webhookLogger.ts -> @/lib/supabase/admin": {"id": 72024, "files": ["static/chunks/2024.bc7066f9d71fba2d.js"]}}