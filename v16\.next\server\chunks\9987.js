"use strict";exports.id=9987,exports.ids=[9987],exports.modules={5089:(e,r,t)=>{t.d(r,{w:()=>c,x:()=>l});var a=t(72267),o=t(99409),n=t(26564),i=t(7950),s=t(55622);async function l(){try{let{user:e}=await (0,o.iF)();if(!e)throw Error("Usuario no autenticado");let{data:r}=await a.N.from("documentos").select("id").eq("user_id",e.id),t=await (0,n.oE)(),l=await (0,i.Lx)(),c=await (0,i.oC)(),d=0,u=0,f=0,m=0,p=0,h=(await Promise.all(t.map(async e=>{let r=await (0,s.yV)(e.id);return d+=r.total,u+=r.paraHoy,f+=r.nuevas,m+=r.aprendiendo,p+=r.repasando,{id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,paraHoy:r.paraHoy}}))).sort((e,r)=>new Date(r.fechaCreacion).getTime()-new Date(e.fechaCreacion).getTime()).slice(0,5),g=l.map(e=>({id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,numeroPreguntas:e.numero_preguntas||0})).slice(0,5);return{totalDocumentos:r?.length||0,totalColeccionesFlashcards:t.length,totalTests:l.length,totalFlashcards:d,flashcardsParaHoy:u,flashcardsNuevas:f,flashcardsAprendiendo:m,flashcardsRepasando:p,testsRealizados:c.totalTests,porcentajeAcierto:c.porcentajeAcierto,coleccionesRecientes:h,testsRecientes:g}}catch(e){return console.error("Error al obtener estad\xedsticas del dashboard:",e),{totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]}}}async function c(e=10){try{let{user:r}=await (0,o.iF)();if(!r)return[];let t=await (0,n.oE)();if(0===t.length)return[];let i=new Date;i.setHours(23,59,59,999);let{data:s,error:l}=await a.N.from("progreso_flashcards").select("flashcard_id, proxima_revision, estado").lte("proxima_revision",i.toISOString()).order("proxima_revision",{ascending:!0}).limit(e);if(l)return console.error("Error al obtener progreso de flashcards:",l),[];if(!s||0===s.length)return[];let c=s.map(e=>e.flashcard_id),{data:d,error:u}=await a.N.from("flashcards").select("id, pregunta, coleccion_id").in("id",c);if(u)return console.error("Error al obtener flashcards:",u),[];let f=[];for(let e of s){let r=d?.find(r=>r.id===e.flashcard_id);if(r){let a=t.find(e=>e.id===r.coleccion_id);a&&f.push({id:r.id,pregunta:r.pregunta,coleccionTitulo:a.titulo,coleccionId:a.id,proximaRevision:e.proxima_revision,estado:e.estado||"nuevo"})}}return f}catch(e){return console.error("Error al obtener pr\xf3ximas flashcards:",e),[]}}},7950:(e,r,t)=>{t.d(r,{Gl:()=>d,Kj:()=>l,Lx:()=>i,OA:()=>c,_4:()=>n,dd:()=>f,hg:()=>s,oC:()=>u});var a=t(72267),o=t(99409);async function n(e,r,t){try{console.log("\uD83D\uDCDD Creando nuevo test:",e);let{user:n}=await (0,o.iF)();if(!n)return console.error("❌ No hay usuario autenticado para crear test"),null;console.log("\uD83D\uDC64 Usuario autenticado:",n.id);let{data:i,error:s}=await a.N.from("tests").insert([{titulo:e,descripcion:r,documentos_ids:t,user_id:n.id}]).select();if(s)return console.error("❌ Error al crear test:",s),null;return console.log("✅ Test creado exitosamente:",i?.[0]?.id),i?.[0]?.id||null}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al crear test:",e),null}}async function i(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:t}=await a.N.from("tests").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(t)return console.error("Error al obtener tests:",t),[];return r||[]}catch(e){return console.error("Error al obtener tests:",e),[]}}async function s(e){let{data:r,error:t}=await a.N.from("preguntas_test").select("*").eq("test_id",e);return t?(console.error("Error al obtener preguntas de test:",t),[]):r||[]}async function l(e){let{count:r,error:t}=await a.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",e);return t?(console.error("Error al obtener conteo de preguntas:",t),0):r||0}async function c(e){let{error:r}=await a.N.from("preguntas_test").insert(e);return!r||(console.error("Error al guardar preguntas de test:",r),!1)}async function d(e,r,t,o){let{error:n}=await a.N.from("estadisticas_test").insert([{test_id:e,pregunta_id:r,respuesta_seleccionada:t,es_correcta:o,fecha_respuesta:new Date().toISOString()}]);return!n||(console.error("Error al registrar respuesta de test:",n),!1)}async function u(){let{data:e,error:r}=await a.N.from("estadisticas_test").select("*");if(r)return console.error("Error al obtener estad\xedsticas de tests:",r),{totalTests:0,totalPreguntas:0,totalRespuestasCorrectas:0,totalRespuestasIncorrectas:0,porcentajeAcierto:0};let t=new Set(e?.map(e=>e.test_id)||[]),o=new Set(e?.map(e=>e.pregunta_id)||[]),n=e?.filter(e=>e.es_correcta).length||0,i=(e?.length||0)-n,s=e&&e.length>0?Math.round(n/e.length*100):0;return{totalTests:t.size,totalPreguntas:o.size,totalRespuestasCorrectas:n,totalRespuestasIncorrectas:i,porcentajeAcierto:s}}async function f(e){let{data:r,error:t}=await a.N.from("estadisticas_test").select("*").eq("test_id",e);if(t)return console.error("Error al obtener estad\xedsticas del test:",t),{testId:e,totalPreguntas:0,totalCorrectas:0,totalIncorrectas:0,porcentajeAcierto:0,fechasRealizacion:[],preguntasMasFalladas:[]};let{data:o}=await a.N.from("preguntas_test").select("*").eq("test_id",e),n=r?.filter(e=>e.es_correcta).length||0,i=(r?.length||0)-n,s=r&&r.length>0?Math.round(n/r.length*100):0,l=new Set;r?.forEach(e=>{let r=new Date(e.fecha_respuesta);l.add(`${r.getDate()}/${r.getMonth()+1}/${r.getFullYear()}`)});let c=Array.from(l),d=new Map;r?.forEach(e=>{let r=d.get(e.pregunta_id)||{fallos:0,aciertos:0};e.es_correcta?r.aciertos++:r.fallos++,d.set(e.pregunta_id,r)});let u=Array.from(d.entries()).map(([e,r])=>({preguntaId:e,totalFallos:r.fallos,totalAciertos:r.aciertos,pregunta:o?.find(r=>r.id===e)?.pregunta||"Desconocida"})).sort((e,r)=>r.totalFallos-e.totalFallos).slice(0,5);return{testId:e,totalPreguntas:o?.length||0,totalCorrectas:n,totalIncorrectas:i,porcentajeAcierto:s,fechasRealizacion:c,preguntasMasFalladas:u}}},11820:(e,r,t)=>{t.d(r,{iF:()=>o});var a=t(48921);async function o(){try{let{data:{user:e},error:r}=await a.N.auth.getUser();if(r){if("Auth session missing!"===r.message)return{user:null,error:null};return{user:null,error:r.message}}return{user:e,error:null}}catch(e){return{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"}}}},21232:(e,r,t)=>{t.d(r,{$S:()=>i,d7:()=>s,fF:()=>n});var a=t(48921),o=t(11820);async function n(e){try{let{user:r,error:t}=await (0,o.iF)();if(!r||t)return null;let{data:n,error:i}=await a.N.from("temarios").select("id").eq("id",e).eq("user_id",r.id).single();if(i){if("PGRST116"===i.code)return console.log("Temario no encontrado:",e),null;return console.error("Error al verificar temario:",i),null}if(!n)return console.log("Temario no encontrado:",e),null;let{data:s,error:l}=await a.N.from("planes_estudios").select("*").eq("user_id",r.id).eq("temario_id",e).eq("activo",!0).single();if(l){if("PGRST116"===l.code)return null;return console.error("Error al obtener plan activo:",l),null}return s}catch(e){return console.error("Error al obtener plan activo:",e),null}}async function i(e){try{let{user:r,error:t}=await (0,o.iF)();if(!r||t)return[];let{data:n,error:i}=await a.N.from("progreso_plan_estudios").select("*").eq("plan_id",e).eq("user_id",r.id).order("semana_numero",{ascending:!0}).order("creado_en",{ascending:!0});if(i)return console.error("Error al obtener progreso del plan:",i),[];return n||[]}catch(e){return console.error("Error al obtener progreso del plan:",e),[]}}async function s(e,r,t,n,i,s,l,c,d){try{let{user:u,error:f}=await (0,o.iF)();if(!u||f)return!1;let{data:m}=await a.N.from("progreso_plan_estudios").select("id").eq("plan_id",e).eq("user_id",u.id).eq("semana_numero",r).eq("dia_nombre",t).eq("tarea_titulo",n).single();if(m){let{error:e}=await a.N.from("progreso_plan_estudios").update({completado:s,fecha_completado:s?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d,actualizado_en:new Date().toISOString()}).eq("id",m.id);if(e)return console.error("Error al actualizar progreso:",e),!1}else{let{error:o}=await a.N.from("progreso_plan_estudios").insert([{plan_id:e,user_id:u.id,semana_numero:r,dia_nombre:t,tarea_titulo:n,tarea_tipo:i,completado:s,fecha_completado:s?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:c,calificacion:d}]);if(o)return console.error("Error al crear progreso:",o),!1}return!0}catch(e){return console.error("Error al guardar progreso de tarea:",e),!1}}},25580:(e,r,t)=>{t.d(r,{Pk:()=>c,u9:()=>l,vD:()=>s});var a=t(48921),o=t(11820);function n(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function i(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach(function(r){var a,o,n;a=e,o=r,n=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function s(e){try{let{user:r,error:t}=await (0,o.iF)();if(!r||t)return console.log("No hay usuario autenticado o error:",t),!1;let{data:n,error:i}=await a.N.from("temarios").select("id").eq("id",e).eq("user_id",r.id).single();if(i){if("PGRST116"===i.code)return!1;return console.error("Error al verificar temario:",i),!1}if(!n)return!1;let{data:s,error:l}=await a.N.from("planificacion_usuario").select("id").eq("user_id",r.id).eq("temario_id",e).eq("completado",!0).limit(1);if(l)return console.error("Error al verificar planificaci\xf3n:",l),!1;return s&&s.length>0}catch(e){return console.error("Error al verificar planificaci\xf3n:",e),!1}}async function l(e){try{let{user:r,error:t}=await (0,o.iF)();if(!r||t)return null;let{data:n,error:i}=await a.N.from("planificacion_usuario").select("*").eq("user_id",r.id).eq("temario_id",e).single();if(i){if("PGRST116"===i.code)return null;return console.error("Error al obtener planificaci\xf3n:",i),null}return n}catch(e){return console.error("Error al obtener planificaci\xf3n:",e),null}}async function c(e,r){try{let{user:t,error:n}=await (0,o.iF)();if(!t||n)return console.error("No hay usuario autenticado"),null;let s=await l(e);if(s){let{data:e,error:t}=await a.N.from("planificacion_usuario").update(i(i({},r),{},{completado:!0,actualizado_en:new Date().toISOString()})).eq("id",s.id).select().single();if(t)return console.error("Error al actualizar planificaci\xf3n:",t),null;return e.id}{let{data:o,error:n}=await a.N.from("planificacion_usuario").insert([i(i({user_id:t.id,temario_id:e},r),{},{completado:!0})]).select().single();if(n)return console.error("Error al crear planificaci\xf3n:",n),null;return o.id}}catch(e){return console.error("Error al guardar planificaci\xf3n:",e),null}}},26564:(e,r,t)=>{t.d(r,{Iv:()=>h,Og:()=>u,Q1:()=>c,QU:()=>b,_W:()=>p,_p:()=>m,as:()=>v,kO:()=>f,oE:()=>l,qJ:()=>s,xq:()=>y,yK:()=>d,yf:()=>g});var a=t(72267),o=t(99409);function n(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function i(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach(function(r){var a,o,n;a=e,o=r,n=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function s(e,r){try{let{user:t}=await (0,o.iF)();if(!t)return console.error("No hay usuario autenticado"),null;let{data:n,error:i}=await a.N.from("colecciones_flashcards").insert([{titulo:e,descripcion:r,user_id:t.id}]).select();if(i)return console.error("Error al crear colecci\xf3n de flashcards:",i),null;return n?.[0]?.id||null}catch(e){return console.error("Error al crear colecci\xf3n de flashcards:",e),null}}async function l(){try{let{user:e,error:r}=await (0,o.iF)();if(r)return console.error("Error al obtener usuario:",r),[];if(!e)return console.error("No hay usuario autenticado"),[];let{data:t,error:n}=await a.N.from("colecciones_flashcards").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});if(n)return console.error("Error al obtener colecciones de flashcards:",n),[];if(!t||0===t.length)return[];return await Promise.all(t.map(async e=>{try{let{data:r,error:t}=await a.N.from("flashcards").select("id").eq("coleccion_id",e.id);if(t)return console.error("Error al contar flashcards para colecci\xf3n",e.id,":",t),i(i({},e),{},{numero_flashcards:0,pendientes_hoy:0});let{data:o,error:n}=await a.N.from("flashcards").select(`
              id,
              progreso_flashcards!inner(
                proxima_revision,
                estado
              )
            `).eq("coleccion_id",e.id).lte("progreso_flashcards.proxima_revision",new Date().toISOString()),s=n?0:o?.length||0;return i(i({},e),{},{numero_flashcards:r?.length||0,pendientes_hoy:s})}catch(r){return console.error("Error al procesar colecci\xf3n",e.id,":",r),i(i({},e),{},{numero_flashcards:0,pendientes_hoy:0})}}))}catch(e){return console.error("Error general al obtener colecciones de flashcards:",e),[]}}async function c(e){let{data:r,error:t}=await a.N.from("flashcards").select("*").eq("coleccion_id",e).order("creado_en",{ascending:!0});return t?(console.error("Error al obtener flashcards:",t),[]):r||[]}async function d(e){let{data:r,error:t}=await a.N.from("flashcards").insert(e).select();return t?(console.error("Error al guardar flashcards:",t),null):r?.map(e=>e.id)||null}async function u(e){let r=await c(e),{data:t,error:o}=await a.N.from("progreso_flashcards").select("*").in("flashcard_id",r.map(e=>e.id));if(o)return console.error("Error al obtener progreso de flashcards:",o),[];let n=new Date,s=new Date(n.getFullYear(),n.getMonth(),n.getDate());return r.map(e=>{let r=t?.find(r=>r.flashcard_id===e.id);if(!r)return i(i({},e),{},{debeEstudiar:!0});let a=new Date(r.proxima_revision),o=new Date(a.getFullYear(),a.getMonth(),a.getDate());return i(i({},e),{},{debeEstudiar:o<=s,progreso:{factor_facilidad:r.factor_facilidad,intervalo:r.intervalo,repeticiones:r.repeticiones,estado:r.estado,proxima_revision:r.proxima_revision}})})}async function f(e,r=10){try{let t=await u(e),o=t.map(e=>e.id),{data:n,error:s}=await a.N.from("historial_revisiones").select("flashcard_id, dificultad").in("flashcard_id",o);if(s)return console.error("Error al obtener historial de revisiones:",s),t.slice(0,r);let l=new Map;return n?.forEach(e=>{let r=l.get(e.flashcard_id)||{dificil:0,total:0};r.total++,"dificil"===e.dificultad&&r.dificil++,l.set(e.flashcard_id,r)}),t.map(e=>{let r=l.get(e.id),t=r?r.dificil/r.total:0;return i(i({},e),{},{ratioDificultad:t})}).sort((e,r)=>r.ratioDificultad-e.ratioDificultad).slice(0,r)}catch(e){return console.error("Error al obtener flashcards m\xe1s dif\xedciles:",e),[]}}async function m(e,r=10){try{return[...await u(e)].sort(()=>Math.random()-.5).slice(0,r)}catch(e){return console.error("Error al obtener flashcards aleatorias:",e),[]}}async function p(e,r=10){try{let t=await u(e),o=t.map(e=>e.id),{data:n,error:s}=await a.N.from("historial_revisiones").select("flashcard_id, fecha").in("flashcard_id",o).order("fecha",{ascending:!1});if(s)return console.error("Error al obtener \xfaltimas revisiones:",s),t.slice(0,r);let l=new Map;return n?.forEach(e=>{l.has(e.flashcard_id)||l.set(e.flashcard_id,e.fecha)}),t.map(e=>{let r=l.get(e.id);return i(i({},e),{},{ultimaRevision:new Date(r||0)})}).sort((e,r)=>e.ultimaRevision.getTime()-r.ultimaRevision.getTime()).slice(0,r)}catch(e){return console.error("Error al obtener flashcards no recientes:",e),[]}}async function h(e,r,t=10){try{return(await u(e)).filter(e=>e.progreso?e.progreso.estado===r:"nuevo"===r).slice(0,t)}catch(e){return console.error("Error al obtener flashcards por estado:",e),[]}}async function g(e,r){try{let t=2.5,o=1,n=0,i="nuevo",s=!1,{data:l,error:c}=await a.N.from("progreso_flashcards").select("factor_facilidad, intervalo, repeticiones, estado").eq("flashcard_id",e).single();!c&&l&&(t=l.factor_facilidad||2.5,o=l.intervalo||1,n=l.repeticiones||0,i=l.estado||"nuevo",s=!0);let d=t,u=o,f=n,m=i;"dificil"===r?(d=Math.max(1.3,t-.3),f=0,u=1,m="aprendiendo"):(f++,"normal"===r?d=t-.15:"facil"===r&&(d=t+.1),d=Math.max(1.3,Math.min(2.5,d)),1===f?(u=1,m="aprendiendo"):2===f?(u=6,m="repasando"):m=(u=Math.round(o*d))>30?"aprendido":"repasando");let p=new Date,h=new Date(p);h.setDate(h.getDate()+u);let g=null;if(s){let{error:r}=await a.N.from("progreso_flashcards").update({factor_facilidad:d,intervalo:u,repeticiones:f,estado:m,ultima_revision:p.toISOString(),proxima_revision:h.toISOString()}).eq("flashcard_id",e);g=r}else{let{error:r}=await a.N.from("progreso_flashcards").insert({flashcard_id:e,factor_facilidad:d,intervalo:u,repeticiones:f,estado:m,ultima_revision:p.toISOString(),proxima_revision:h.toISOString()});g=r}if(g)return console.error("Error al guardar progreso:",g),!1;let{error:y}=await a.N.from("historial_revisiones").insert({flashcard_id:e,dificultad:r,factor_facilidad:d,intervalo:u,repeticiones:f,fecha:p.toISOString()});return!0}catch(e){return!1}}async function y(e,r,t){try{let{error:o}=await a.N.from("flashcards").update({pregunta:r,respuesta:t,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return!1;return!0}catch(e){return!1}}async function b(e){try{let{error:r}=await a.N.from("progreso_flashcards").delete().eq("flashcard_id",e);if(r)return!1;let{error:t}=await a.N.from("historial_revisiones").delete().eq("flashcard_id",e);if(t)return!1;let{error:o,count:n}=await a.N.from("flashcards").delete({count:"exact"}).eq("id",e);if(o||0===n)return!1;return!0}catch(e){return!1}}async function v(e){try{let{user:r}=await (0,o.iF)();if(!r)return!1;let{data:t,error:n}=await a.N.from("flashcards").select("id").eq("coleccion_id",e);if(n)return!1;let i=t?.map(e=>e.id)||[];if(i.length>0){let{error:r}=await a.N.from("progreso_flashcards").delete().in("flashcard_id",i);if(r)return!1;let{error:t}=await a.N.from("historial_revisiones").delete().in("flashcard_id",i);if(t)return!1;let{error:o}=await a.N.from("flashcards").delete().eq("coleccion_id",e);if(o)return!1}let{error:s,count:l}=await a.N.from("colecciones_flashcards").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(s||0===l)return!1;return!0}catch(e){return!1}}},29399:(e,r,t)=>{t.d(r,{A:()=>d});var a=t(96554),o=t(93823),n=t(47463),i=t(99631);function s(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?s(Object(t),!0).forEach(function(r){var a,o,n;a=e,o=r,n=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let c=(0,a.forwardRef)(({onSelectionChange:e},r)=>{let{0:t,1:s}=(0,a.useState)([]),{0:c,1:d}=(0,a.useState)([]),{0:u,1:f}=(0,a.useState)(!0),m=async()=>{f(!0);try{let e=await (0,n.R1)();s(e)}catch(e){console.error("Error al cargar documentos:",e)}finally{f(!1)}};(0,a.useEffect)(()=>{m()},[]),(0,a.useImperativeHandle)(r,()=>({recargarDocumentos:m}));let p=t.map(e=>({value:e.id,label:`${e.numero_tema?`Tema ${e.numero_tema}: `:""}${e.titulo} ${e.categoria?`(${e.categoria})`:""}`}));return(0,i.jsxs)("div",{className:"mb-3",children:[(0,i.jsx)("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Selecciona los documentos para consultar:"}),(0,i.jsx)(o.Ay,{instanceId:"document-selector",isMulti:!0,isLoading:u,options:p,className:"basic-multi-select",classNamePrefix:"select",placeholder:"Selecciona uno o m\xe1s documentos...",noOptionsMessage:()=>"No hay documentos disponibles",onChange:r=>{d(r||[]),e(r.map(e=>t.find(r=>r.id===e.value)).filter(Boolean))},value:c,styles:{control:e=>l(l({},e),{},{minHeight:"36px",fontSize:"14px"}),multiValue:e=>l(l({},e),{},{fontSize:"12px"}),placeholder:e=>l(l({},e),{},{fontSize:"14px"})}}),0===c.length&&(0,i.jsx)("p",{className:"text-red-500 text-xs italic mt-0.5",children:"Debes seleccionar al menos un documento"})]})});c.displayName="DocumentSelector";let d=c},47463:(e,r,t)=>{t.d(r,{vW:()=>d.vW,fW:()=>d.fW,xq:()=>u.xq,qJ:()=>u.qJ,Yp:()=>d.Yp,_4:()=>m._4,CM:()=>d.CM,sq:()=>d.sq,Q3:()=>c,hE:()=>l,yK:()=>u.yK,QE:()=>d.QE,OA:()=>m.OA,oE:()=>u.oE,Sl:()=>d.Sl,sj:()=>d.sj,R1:()=>s,yV:()=>f.yV,wU:()=>f.wU,oC:()=>m.oC,dd:()=>m.dd,C9:()=>d.C9,hg:()=>m.hg,Kj:()=>m.Kj,Lx:()=>m.Lx,Gl:()=>m.Gl});var a=t(72267),o=t(99409);function n(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function i(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach(function(r){var a,o,n;a=e,o=r,n=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function s(){try{let{user:e}=await (0,o.iF)();if(!e)return console.error("No hay usuario autenticado"),[];let{data:r,error:t}=await a.N.from("documentos").select("*").eq("user_id",e.id).order("numero_tema",{ascending:!0});if(t)return console.error("Error al obtener documentos:",t),[];return r||[]}catch(e){return console.error("Error al obtener documentos:",e),[]}}async function l(e){try{let{user:r}=await (0,o.iF)();if(!r)return console.error("No hay usuario autenticado"),null;let t=i(i({},e),{},{user_id:r.id,tipo_original:e.tipo_original}),{data:n,error:s}=await a.N.from("documentos").insert([t]).select();if(s)return console.error("Error al guardar documento:",s),null;return n?.[0]?.id||null}catch(e){return console.error("Error al guardar documento:",e),null}}async function c(e){try{console.log("\uD83D\uDDD1️ Iniciando eliminaci\xf3n de documento:",e);let{user:r}=await (0,o.iF)();if(!r)return console.error("❌ No hay usuario autenticado para eliminar documento"),!1;console.log("\uD83D\uDC64 Usuario autenticado:",r.id),console.log("\uD83D\uDCC4 Eliminando documento ID:",e);let{error:t,count:n}=await a.N.from("documentos").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(t)return console.error("❌ Error al eliminar documento de Supabase:",t),!1;if(console.log("✅ Documento eliminado exitosamente. Filas afectadas:",n),0===n)return console.warn("⚠️ No se elimin\xf3 ning\xfan documento. Posibles causas: documento no existe o no pertenece al usuario"),!1;return!0}catch(e){return console.error("\uD83D\uDCA5 Error inesperado al eliminar documento:",e),!1}}var d=t(53867),u=t(26564),f=t(55622),m=t(7950);t(5089)},53766:(e,r,t)=>{t.d(r,{B$:()=>p,Il:()=>g,Se:()=>f,cN:()=>h,cm:()=>d,jg:()=>l,oS:()=>m,r5:()=>c,sW:()=>u,xv:()=>y,yr:()=>s});var a=t(48921),o=t(11820);function n(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function i(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?n(Object(t),!0).forEach(function(r){var a,o,n;a=e,o=r,n=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):n(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}async function s(){try{let{user:e,error:r}=await (0,o.iF)();if(!e||r)return!1;let{data:t,error:n}=await a.N.from("temarios").select("id").eq("user_id",e.id).limit(1);if(n){if("PGRST116"===n.code||n.message?.includes("relation")||n.message?.includes("does not exist"))return!1;return console.error("Error al verificar temario en Supabase:",n),!1}return t&&t.length>0}catch(e){return console.error("Error general al verificar temario:",e),!1}}async function l(){try{let{user:e,error:r}=await (0,o.iF)();if(!e||r)return null;let{data:t,error:n}=await a.N.from("temarios").select("*").eq("user_id",e.id).single();if(n){if("PGRST116"===n.code)return null;return console.error("Error al obtener temario en Supabase:",n),null}return t}catch(e){return console.error("Error general al obtener temario:",e),null}}async function c(e,r,t){try{let{user:n,error:i}=await (0,o.iF)();if(!n||i)return console.error("No hay usuario autenticado o error:",i),null;let{data:s,error:l}=await a.N.from("temarios").insert([{titulo:e,descripcion:r,tipo:t,user_id:n.id}]).select().single();if(l)return console.error("Error al crear temario:",l),null;return s.id}catch(e){return console.error("Error al crear temario:",e),null}}async function d(e){try{let{data:r,error:t}=await a.N.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});if(t)return console.error("Error al obtener temas:",t),[];return r||[]}catch(e){return console.error("Error al obtener temas:",e),[]}}async function u(e,r){try{let t=r.map(r=>i(i({},r),{},{temario_id:e})),{error:o}=await a.N.from("temas").insert(t);if(o)return console.error("Error al crear temas:",o),!1;return!0}catch(e){return console.error("Error al crear temas:",e),!1}}async function f(e,r,t){try{let{error:o}=await a.N.from("temarios").update({titulo:r,descripcion:t,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar temario:",o),!1;return!0}catch(e){return console.error("Error al actualizar temario:",e),!1}}async function m(e,r,t){try{let{error:o}=await a.N.from("temas").update({titulo:r,descripcion:t,actualizado_en:new Date().toISOString()}).eq("id",e);if(o)return console.error("Error al actualizar tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar tema:",e),!1}}async function p(e){try{let{error:r}=await a.N.from("temas").delete().eq("id",e);if(r)return console.error("Error al eliminar tema:",r),!1;return!0}catch(e){return console.error("Error al eliminar tema:",e),!1}}async function h(e,r){try{let t={completado:r,actualizado_en:new Date().toISOString()};r?t.fecha_completado=new Date().toISOString():t.fecha_completado=null;let{error:o}=await a.N.from("temas").update(t).eq("id",e);if(o)return console.error("Error al actualizar estado del tema:",o),!1;return!0}catch(e){return console.error("Error al actualizar estado del tema:",e),!1}}async function g(e){try{let{data:r,error:t}=await a.N.from("temas").select("completado").eq("temario_id",e);if(t)return console.error("Error al obtener estad\xedsticas del temario:",t),null;let o=r.length,n=r.filter(e=>e.completado).length;return{totalTemas:o,temasCompletados:n,porcentajeCompletado:o>0?n/o*100:0}}catch(e){return console.error("Error al obtener estad\xedsticas del temario:",e),null}}async function y(e){try{let{error:r}=await a.N.from("temarios").delete().eq("id",e);if(r)return console.error("Error al eliminar temario:",r),!1;return!0}catch(e){return console.error("Error al eliminar temario:",e),!1}}},53867:(e,r,t)=>{t.d(r,{C9:()=>u,CM:()=>l,QE:()=>d,Sl:()=>c,Yp:()=>o,fW:()=>i,sj:()=>n,sq:()=>f,vW:()=>s});var a=t(72267);async function o(e,r=!1){try{let{data:{user:t}}=await a.N.auth.getUser();if(!t)return console.error("No hay usuario autenticado para crear conversaci\xf3n"),null;r&&await l();let{data:o,error:n}=await a.N.from("conversaciones").insert([{titulo:e,activa:r,user_id:t.id}]).select();if(n)return console.error("Error al crear conversaci\xf3n:",n),null;return o?.[0]?.id||null}catch(e){return console.error("Error inesperado al crear conversaci\xf3n:",e),null}}async function n(){try{let{data:{user:e}}=await a.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para obtener conversaciones"),[];let{data:r,error:t}=await a.N.from("conversaciones").select("*").eq("user_id",e.id).order("actualizado_en",{ascending:!1});if(t)return console.error("Error al obtener conversaciones:",t),[];return r||[]}catch(e){return console.error("Error inesperado al obtener conversaciones:",e),[]}}async function i(e,r){let{error:t}=await a.N.from("conversaciones").update({titulo:r,actualizado_en:new Date().toISOString()}).eq("id",e);return!t||(console.error("Error al actualizar conversaci\xf3n:",t),!1)}async function s(e){try{await l();let{error:r}=await a.N.from("conversaciones").update({activa:!0,actualizado_en:new Date().toISOString()}).eq("id",e);if(r)return console.error("Error al activar conversaci\xf3n:",r),!1;return!0}catch(e){return console.error("Error inesperado al activar conversaci\xf3n:",e),!1}}async function l(){try{let{data:{user:e}}=await a.N.auth.getUser();if(!e)return console.error("No hay usuario autenticado para desactivar conversaciones"),!1;let{error:r}=await a.N.from("conversaciones").update({activa:!1}).eq("user_id",e.id).eq("activa",!0);if(r)return console.error("Error al desactivar todas las conversaciones:",r),!1;return!0}catch(e){return console.error("Error inesperado al desactivar conversaciones:",e),!1}}async function c(){try{let{data:{user:e}}=await a.N.auth.getUser();if(!e)return console.warn("No hay usuario autenticado para obtener conversaci\xf3n activa"),null;let{data:r,error:t}=await a.N.from("conversaciones").select("*").eq("user_id",e.id);t&&console.error("Error al obtener todas las conversaciones:",t);let{data:o,error:n}=await a.N.from("conversaciones").select("*").eq("user_id",e.id).eq("activa",!0).limit(1);if(n){if("406"===n.code||n.message.includes("406"))return null;return console.error("Error al obtener conversaci\xf3n activa:",n),null}return o&&o.length>0?o[0]:null}catch(e){return console.error("Error inesperado al obtener conversaci\xf3n activa:",e),null}}async function d(e){try{let{data:r,error:t}=await a.N.from("conversaciones").select("id").eq("id",e.conversacion_id).single();if(t)return console.error("Error al verificar la conversaci\xf3n:",t),null;let{data:o,error:n}=await a.N.from("mensajes").insert([e]).select();if(n)return console.error("Error al guardar mensaje:",n),null;let{error:i}=await a.N.from("conversaciones").update({actualizado_en:new Date().toISOString()}).eq("id",e.conversacion_id);return i&&console.error("Error al actualizar la fecha de la conversaci\xf3n:",i),o?.[0]?.id||null}catch(e){return console.error("Error inesperado al guardar mensaje:",e),null}}async function u(e){let{data:r,error:t}=await a.N.from("mensajes").select("*").eq("conversacion_id",e).order("timestamp",{ascending:!0});return t?(console.error("Error al obtener mensajes:",t),[]):r||[]}async function f(e){try{let{data:{user:r}}=await a.N.auth.getUser();if(!r)return console.error("No hay usuario autenticado para eliminar conversaci\xf3n"),!1;let{error:t}=await a.N.from("mensajes").delete().eq("conversacion_id",e);if(t)return console.error("Error al eliminar mensajes de la conversaci\xf3n:",t),!1;let{error:o,count:n}=await a.N.from("conversaciones").delete({count:"exact"}).eq("id",e).eq("user_id",r.id);if(o)return console.error("Error al eliminar conversaci\xf3n:",o),!1;if(0===n)return!1;return!0}catch(e){return console.error("Error inesperado al eliminar conversaci\xf3n:",e),!1}}},55622:(e,r,t)=>{t.d(r,{wU:()=>i,yV:()=>n});var a=t(72267),o=t(26564);async function n(e){let r=await (0,o.Q1)(e);if(0===r.length)return{total:0,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0};let{data:t,error:n}=await a.N.from("progreso_flashcards").select("*").in("flashcard_id",r.map(e=>e.id));if(n)return console.error("Error al obtener progreso de flashcards:",n),{total:r.length,nuevas:r.length,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:r.length};let i={total:r.length,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0},s=new Date;return r.forEach(e=>{let r=t?.find(r=>r.flashcard_id===e.id);if(r){switch(r.estado){case"nuevo":i.nuevas++;break;case"aprendiendo":i.aprendiendo++;break;case"repasando":i.repasando++;break;case"aprendido":i.aprendidas++}let e=new Date(r.proxima_revision);new Date(e.getFullYear(),e.getMonth(),e.getDate())<=new Date(s.getFullYear(),s.getMonth(),s.getDate())&&i.paraHoy++}else i.nuevas++,i.paraHoy++}),i}async function i(e){try{let r=await (0,o.Q1)(e);if(0===r.length)return{totalSesiones:0,totalRevisiones:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};let t=r.map(e=>e.id),{data:n,error:i}=await a.N.from("historial_revisiones").select("*").in("flashcard_id",t).order("fecha",{ascending:!0});if(i)return console.error("Error al obtener revisiones:",i),null;let{data:s,error:l}=await a.N.from("progreso_flashcards").select("*").in("flashcard_id",t);l&&console.error("Error al obtener progreso:",l);let c={totalSesiones:0,totalRevisiones:n?n.length:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]};if(n&&n.length>0){n.forEach(e=>{"dificil"===e.dificultad?c.distribucionDificultad.dificil++:"normal"===e.dificultad?c.distribucionDificultad.normal++:"facil"===e.dificultad&&c.distribucionDificultad.facil++});let e=new Set;n.forEach(r=>{let t=new Date(r.fecha).toISOString().split("T")[0];e.add(t)}),c.totalSesiones=e.size;let t=new Map;r.forEach(e=>{t.set(e.id,{dificil:0,normal:0,facil:0,total:0})}),n.forEach(e=>{let r=t.get(e.flashcard_id);r&&("dificil"===e.dificultad?r.dificil++:"normal"===e.dificultad?r.normal++:"facil"===e.dificultad&&r.facil++,r.total++)}),c.tarjetasMasDificiles=r.map(e=>{let r=t.get(e.id)||{dificil:0,normal:0,facil:0,total:0};return{id:e.id,pregunta:e.pregunta,dificil:r.dificil,normal:r.normal,facil:r.facil,totalRevisiones:r.total}}).filter(e=>e.totalRevisiones>0).sort((e,r)=>{let t=e.totalRevisiones>0?e.dificil/e.totalRevisiones:0;return(r.totalRevisiones>0?r.dificil/r.totalRevisiones:0)-t}).slice(0,10)}return c}catch(e){return console.error("Error al calcular estad\xedsticas detalladas:",e),null}}},63490:(e,r,t)=>{t.d(r,{A:()=>d});var a=t(96554),o=t(81815),n=t(21232),i=t(29959),s=t(99631);function l(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,a)}return t}function c(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?l(Object(t),!0).forEach(function(r){var a,o,n;a=e,o=r,n=t[r],(o=function(e){var r=function(e,r){if("object"!=typeof e||null===e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var a=t.call(e,r||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:String(r)}(o))in a?Object.defineProperty(a,o,{value:n,enumerable:!0,configurable:!0,writable:!0}):a[o]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}let d=({plan:e,temarioId:r})=>{let{0:t,1:l}=(0,a.useState)([]),{0:d,1:u}=(0,a.useState)(null),{0:f,1:m}=(0,a.useState)(!0);(0,a.useEffect)(()=>{p()},[r]);let p=async()=>{try{let e=await (0,n.fF)(r);if(!e){console.warn("No se encontr\xf3 plan activo para el temario:",r),u(null),l([]),m(!1);return}u(e.id);let t=await (0,n.$S)(e.id);l(t)}catch(e){console.error("Error al cargar progreso:",e),u(null),l([])}finally{m(!1)}},h=async(e,r,a)=>{if(!d)return void i.oR.error("No se pudo identificar el plan de estudios");try{let o=t.find(t=>t.semana_numero===r&&t.dia_nombre===a&&t.tarea_titulo===e.titulo),s=!o?.completado;await (0,n.d7)(d,r,a,e.titulo,e.tipo,s)?(l(t=>{let o=t.findIndex(t=>t.semana_numero===r&&t.dia_nombre===a&&t.tarea_titulo===e.titulo);if(!(o>=0))return[...t,{id:`temp-${Date.now()}`,plan_id:d,user_id:"",semana_numero:r,dia_nombre:a,tarea_titulo:e.titulo,tarea_tipo:e.tipo,completado:s,fecha_completado:s?new Date().toISOString():void 0,creado_en:new Date().toISOString(),actualizado_en:new Date().toISOString()}];{let e=[...t];return e[o]=c(c({},e[o]),{},{completado:s,fecha_completado:s?new Date().toISOString():void 0}),e}}),i.oR.success(s?"Tarea completada":"Tarea marcada como pendiente")):i.oR.error("Error al actualizar el progreso")}catch(e){console.error("Error al actualizar tarea:",e),i.oR.error("Error al actualizar el progreso")}},g=(e,r,a)=>t.some(t=>t.semana_numero===r&&t.dia_nombre===a&&t.tarea_titulo===e.titulo&&t.completado),y=(()=>{if(!e||!e.semanas||!Array.isArray(e.semanas))return{completadas:0,total:0,porcentaje:0};let r=e.semanas.reduce((e,r)=>r&&r.dias&&Array.isArray(r.dias)?e+r.dias.reduce((e,r)=>r&&r.tareas&&Array.isArray(r.tareas)?e+r.tareas.length:e,0):e,0),a=t.filter(e=>e.completado).length;return{completadas:a,total:r,porcentaje:r>0?Math.round(a/r*100):0}})();return f?(0,s.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,s.jsx)("span",{className:"ml-3 text-gray-600",children:"Cargando progreso..."})]}):e?r&&""!==r.trim()?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Introducci\xf3n"}),(0,s.jsx)("p",{className:"text-blue-800",children:e.introduccion||"Introducci\xf3n no disponible"})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("h3",{className:"font-semibold text-gray-900",children:"Progreso General"}),(0,s.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[y.porcentaje,"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,s.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:`${y.porcentaje}%`}})}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[y.completadas," de ",y.total," tareas completadas"]})]}),e.resumen&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.Ohp,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Total"}),(0,s.jsx)("p",{className:"font-semibold",children:e.resumen.tiempoTotalEstudio||"No disponible"})]})]})}),(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.H9b,{className:"w-5 h-5 text-green-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Temas"}),(0,s.jsx)("p",{className:"font-semibold",children:e.resumen.numeroTemas||"No disponible"})]})]})}),(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.x_j,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Estudio Nuevo"}),(0,s.jsx)("p",{className:"font-semibold",children:e.resumen.duracionEstudioNuevo||"No disponible"})]})]})}),(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.jTZ,{className:"w-5 h-5 text-orange-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Repaso Final"}),(0,s.jsx)("p",{className:"font-semibold",children:e.resumen.duracionRepasoFinal||"No disponible"})]})]})})]}),e.semanas&&e.semanas.length>0&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,s.jsx)(o.wIk,{className:"w-5 h-5 mr-2"}),"Cronograma Semanal"]}),e.semanas.map((e,r)=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,s.jsxs)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h4",{className:"text-lg font-semibold text-gray-900",children:["Semana ",e?.numero||"N/A"]}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[e?.fechaInicio||"N/A"," - ",e?.fechaFin||"N/A"]})]}),(0,s.jsx)("p",{className:"text-gray-700 mt-2",children:e?.objetivoPrincipal||"Objetivo no especificado"})]}),(0,s.jsx)("div",{className:"p-6 space-y-4",children:e.dias&&Array.isArray(e.dias)?e.dias.map((r,t)=>(0,s.jsxs)("div",{className:"border border-gray-100 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,s.jsx)("h5",{className:"font-semibold text-gray-900",children:r?.dia||"D\xeda no especificado"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded",children:[r?.horas||0,"h"]})]}),(0,s.jsx)("div",{className:"space-y-2",children:r.tareas&&Array.isArray(r.tareas)?r.tareas.map((t,a)=>{let n=g(t,e.numero,r.dia);return(0,s.jsxs)("div",{className:`flex items-start p-3 rounded-lg border transition-all cursor-pointer ${n?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-blue-300"}`,onClick:()=>h(t,e.numero,r.dia),children:[(0,s.jsx)("div",{className:`flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ${n?"bg-green-500 border-green-500":"border-gray-300 hover:border-blue-400"}`,children:n&&(0,s.jsx)(o.YrT,{className:"w-3 h-3 text-white"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h6",{className:`font-medium ${n?"text-green-800 line-through":"text-gray-900"}`,children:t?.titulo||"Tarea sin t\xedtulo"}),t?.descripcion&&(0,s.jsx)("p",{className:`text-sm mt-1 ${n?"text-green-700":"text-gray-600"}`,children:t.descripcion}),(0,s.jsxs)("div",{className:"flex items-center mt-2 space-x-3",children:[(0,s.jsx)("span",{className:`text-xs px-2 py-1 rounded ${t?.tipo==="estudio"?"bg-blue-100 text-blue-800":t?.tipo==="repaso"?"bg-yellow-100 text-yellow-800":t?.tipo==="practica"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:t?.tipo||"general"}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:t?.duracionEstimada||"No especificado"})]})]})]},a)}):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay tareas disponibles"})})]},t)):(0,s.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay d\xedas disponibles"})})]},r))]}),(0,s.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"Estrategia de Repasos"}),(0,s.jsx)("p",{className:"text-yellow-800",children:"string"==typeof e.estrategiaRepasos?e.estrategiaRepasos:e.estrategiaRepasos&&"object"==typeof e.estrategiaRepasos&&e.estrategiaRepasos.descripcion||"Estrategia de repasos no disponible"})]}),(0,s.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"Pr\xf3ximos Pasos y Consejos"}),(0,s.jsx)("p",{className:"text-purple-800",children:"string"==typeof e.proximosPasos?e.proximosPasos:e.proximosPasos&&"object"==typeof e.proximosPasos&&e.proximosPasos.descripcion||"Pr\xf3ximos pasos no disponibles"})]})]}):(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"ID de temario no v\xe1lido"})})}):(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"No se pudo cargar el plan de estudios"})})})}},68941:(e,r,t)=>{t.d(r,{A:()=>s}),t(96554);var a=t(97334),o=t.n(a),n=t(81815),i=t(99631);function s({feature:e,featureDescription:r,benefits:t=[],className:a=""}){let s=t.length>0?t:["Acceso ilimitado a todas las funcionalidades","Generaci\xf3n de contenido sin l\xedmites","Soporte prioritario","Nuevas funcionalidades en primicia"],l={ai_tutor_chat:{name:"Chat con IA",description:"Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas."},study_planning:{name:"Planificaci\xf3n de Estudios",description:"Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos."},summary_a1_a2:{name:"Res\xfamenes Avanzados",description:"Genera res\xfamenes inteligentes y estructurados de tus documentos de estudio."}}[e]||{name:e,description:r||"Esta funcionalidad avanzada te ayudar\xe1 a mejorar tu preparaci\xf3n."};return(0,i.jsx)("div",{className:`min-h-screen bg-gray-50 flex items-center justify-center p-4 ${a}`,children:(0,i.jsx)("div",{className:"max-w-2xl w-full",children:(0,i.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white",children:[(0,i.jsx)("div",{className:"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,i.jsx)(n.F5$,{className:"w-10 h-10"})}),(0,i.jsx)("h1",{className:"text-3xl font-bold mb-4",children:l.name}),(0,i.jsx)("p",{className:"text-blue-100 text-lg leading-relaxed",children:l.description})]}),(0,i.jsxs)("div",{className:"px-8 py-8",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6",children:[(0,i.jsx)(n.usP,{className:"w-4 h-4 mr-2"}),"Funcionalidad Premium"]}),(0,i.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Actualiza tu plan para acceder"}),(0,i.jsx)("p",{className:"text-gray-600 text-lg",children:"Esta funcionalidad est\xe1 disponible para usuarios con planes de pago. Actualiza ahora y desbloquea todo el potencial de OposiAI."})]}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"\xbfQu\xe9 obtienes al actualizar?"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.map((e,r)=>(0,i.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,i.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,i.jsx)(n.YrT,{className:"w-4 h-4 text-green-600"})}),(0,i.jsx)("span",{className:"text-gray-700",children:e})]},r))})]}),(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,i.jsxs)(o(),{href:"/upgrade-plan",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,i.jsx)(n.ei4,{className:"w-5 h-5 mr-2"}),"Actualizar Plan"]}),(0,i.jsx)(o(),{href:"/app",className:"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors",children:"Volver al Dashboard"})]}),(0,i.jsx)("div",{className:"mt-8 text-center",children:(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfTienes preguntas? ",(0,i.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Cont\xe1ctanos"})]})})]})]})})})}},91212:(e,r,t)=>{t.d(r,{IE:()=>o,qk:()=>i,qo:()=>a});let a={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function o(e){return a[e]||null}function n(e,r){let t=o(e);return!(!t||t.restrictedFeatures.includes(r))&&t.features.includes(r)}async function i(e){try{let r=await fetch("/api/user/plan");if(!r.ok)return console.error("Error obteniendo plan del usuario"),n("free",e);let{plan:t}=await r.json();return n(t||"free",e)}catch(r){return console.error("Error verificando acceso a caracter\xedstica:",r),n("free",e)}}}};