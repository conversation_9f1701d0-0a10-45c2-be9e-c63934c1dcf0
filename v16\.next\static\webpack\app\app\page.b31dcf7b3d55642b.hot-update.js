"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/lib/services/permissionService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/permissionService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PermissionService: () => (/* binding */ PermissionService)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/classCallCheck.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/createClass.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n/* harmony import */ var _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/webhookLogger */ \"(app-pages-browser)/./src/lib/utils/webhookLogger.ts\");\n\n\n\n\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _createForOfIteratorHelper(o, allowArrayLike) {\n    var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"];\n    if (!it) {\n        if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n            if (it) o = it;\n            var i = 0;\n            var F = function F() {};\n            return {\n                s: F,\n                n: function n() {\n                    if (i >= o.length) return {\n                        done: true\n                    };\n                    return {\n                        done: false,\n                        value: o[i++]\n                    };\n                },\n                e: function e(_e) {\n                    throw _e;\n                },\n                f: F\n            };\n        }\n        throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true, didErr = false, err;\n    return {\n        s: function s() {\n            it = it.call(o);\n        },\n        n: function n() {\n            var step = it.next();\n            normalCompletion = step.done;\n            return step;\n        },\n        e: function e(_e2) {\n            didErr = true;\n            err = _e2;\n        },\n        f: function f() {\n            try {\n                if (!normalCompletion && it[\"return\"] != null) it[\"return\"]();\n            } finally{\n                if (didErr) throw err;\n            }\n        }\n    };\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];\n    return arr2;\n}\n\n// src/lib/services/permissionService.ts\n// Sistema centralizado de verificación de permisos\n\n\n\nvar PermissionService = /*#__PURE__*/ function() {\n    function PermissionService() {\n        (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this, PermissionService);\n    }\n    (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(PermissionService, null, [\n        {\n            key: \"checkPermission\",\n            value: /**\n     * Verificar permisos completos para una acción\n     */ function() {\n                var _checkPermission = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee(userId, permission) {\n                    var _yield$import, SupabaseAdminService, profile, planConfig, requiredPlans, tokenCheck, tokenInfo;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee$(_context) {\n                        while(1)switch(_context.prev = _context.next){\n                            case 0:\n                                _context.prev = 0;\n                                _context.next = 3;\n                                return __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_admin_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/admin */ \"(app-pages-browser)/./src/lib/supabase/admin.ts\"));\n                            case 3:\n                                _yield$import = _context.sent;\n                                SupabaseAdminService = _yield$import.SupabaseAdminService;\n                                _context.next = 7;\n                                return SupabaseAdminService.getUserProfile(userId);\n                            case 7:\n                                profile = _context.sent;\n                                if (profile) {\n                                    _context.next = 10;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Perfil de usuario no encontrado'\n                                });\n                            case 10:\n                                if (!(permission.requiresPayment !== false && profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                                    _context.next = 12;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Pago no verificado',\n                                    userPlan: profile.subscription_plan,\n                                    upgradeRequired: false\n                                });\n                            case 12:\n                                if (!(permission.minimumPlan && permission.minimumPlan.length > 0)) {\n                                    _context.next = 15;\n                                    break;\n                                }\n                                if (permission.minimumPlan.includes(profile.subscription_plan)) {\n                                    _context.next = 15;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"Esta funci\\xF3n requiere plan \".concat(permission.minimumPlan.join(' o ')),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: permission.minimumPlan,\n                                    upgradeRequired: true,\n                                    suggestedPlan: this.getSuggestedPlan(profile.subscription_plan, permission.minimumPlan)\n                                });\n                            case 15:\n                                if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.hasFeatureAccess)(profile.subscription_plan, permission.feature)) {\n                                    _context.next = 19;\n                                    break;\n                                }\n                                planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)(profile.subscription_plan);\n                                requiredPlans = this.getPlansWithFeature(permission.feature);\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"La funci\\xF3n \".concat(permission.feature, \" no est\\xE1 disponible en \").concat((planConfig === null || planConfig === void 0 ? void 0 : planConfig.name) || profile.subscription_plan),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: requiredPlans,\n                                    upgradeRequired: true,\n                                    suggestedPlan: requiredPlans[0]\n                                });\n                            case 19:\n                                if (!(permission.tokensRequired && permission.tokensRequired > 0)) {\n                                    _context.next = 25;\n                                    break;\n                                }\n                                _context.next = 22;\n                                return this.checkTokenLimits(profile, permission.tokensRequired);\n                            case 22:\n                                tokenCheck = _context.sent;\n                                if (tokenCheck.allowed) {\n                                    _context.next = 25;\n                                    break;\n                                }\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: tokenCheck.reason,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenCheck.tokenInfo,\n                                    upgradeRequired: tokenCheck.upgradeRequired,\n                                    suggestedPlan: tokenCheck.suggestedPlan\n                                });\n                            case 25:\n                                // Si llegamos aquí, el permiso está concedido\n                                tokenInfo = this.getTokenInfo(profile); // Log del acceso concedido\n                                _context.next = 28;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, permission.feature, true, profile.subscription_plan, permission.tokensRequired || 0);\n                            case 28:\n                                return _context.abrupt(\"return\", {\n                                    granted: true,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenInfo\n                                });\n                            case 31:\n                                _context.prev = 31;\n                                _context.t0 = _context[\"catch\"](0);\n                                console.error('Error checking permission:', _context.t0);\n                                // Log del error\n                                _context.next = 36;\n                                return _lib_utils_webhookLogger__WEBPACK_IMPORTED_MODULE_7__.WebhookLogger.logFeatureAccess(userId, permission.feature, false, 'error', 0, 'Internal permission check error');\n                            case 36:\n                                return _context.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Error interno de verificación de permisos'\n                                });\n                            case 37:\n                            case \"end\":\n                                return _context.stop();\n                        }\n                    }, _callee, this, [\n                        [\n                            0,\n                            31\n                        ]\n                    ]);\n                }));\n                function checkPermission(_x, _x2) {\n                    return _checkPermission.apply(this, arguments);\n                }\n                return checkPermission;\n            }()\n        },\n        {\n            key: \"checkMultiplePermissions\",\n            value: function() {\n                var _checkMultiplePermissions = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee2(userId, permissions) {\n                    var results, _iterator, _step, permission;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee2$(_context2) {\n                        while(1)switch(_context2.prev = _context2.next){\n                            case 0:\n                                results = {};\n                                _iterator = _createForOfIteratorHelper(permissions);\n                                _context2.prev = 2;\n                                _iterator.s();\n                            case 4:\n                                if ((_step = _iterator.n()).done) {\n                                    _context2.next = 11;\n                                    break;\n                                }\n                                permission = _step.value;\n                                _context2.next = 8;\n                                return this.checkPermission(userId, permission);\n                            case 8:\n                                results[permission.feature] = _context2.sent;\n                            case 9:\n                                _context2.next = 4;\n                                break;\n                            case 11:\n                                _context2.next = 16;\n                                break;\n                            case 13:\n                                _context2.prev = 13;\n                                _context2.t0 = _context2[\"catch\"](2);\n                                _iterator.e(_context2.t0);\n                            case 16:\n                                _context2.prev = 16;\n                                _iterator.f();\n                                return _context2.finish(16);\n                            case 19:\n                                return _context2.abrupt(\"return\", results);\n                            case 20:\n                            case \"end\":\n                                return _context2.stop();\n                        }\n                    }, _callee2, this, [\n                        [\n                            2,\n                            13,\n                            16,\n                            19\n                        ]\n                    ]);\n                }));\n                function checkMultiplePermissions(_x3, _x4) {\n                    return _checkMultiplePermissions.apply(this, arguments);\n                }\n                return checkMultiplePermissions;\n            }()\n        },\n        {\n            key: \"checkTokenLimits\",\n            value: function() {\n                var _checkTokenLimits = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee3(profile, tokensRequired) {\n                    var currentMonth, currentTokens, tokenInfo, suggestedPlan;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee3$(_context3) {\n                        while(1)switch(_context3.prev = _context3.next){\n                            case 0:\n                                currentMonth = new Date().toISOString().slice(0, 7) + '-01'; // Reset si es nuevo mes\n                                currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                                tokenInfo = {\n                                    current: currentTokens || 0,\n                                    limit: profile.monthly_token_limit || 0,\n                                    remaining: Math.max(0, (profile.monthly_token_limit || 0) - (currentTokens || 0)),\n                                    percentage: (profile.monthly_token_limit || 0) > 0 ? Math.round((currentTokens || 0) / profile.monthly_token_limit * 100) : 0\n                                }; // Verificar si tiene tokens suficientes\n                                if (!(currentTokens + tokensRequired > profile.monthly_token_limit)) {\n                                    _context3.next = 6;\n                                    break;\n                                }\n                                suggestedPlan = profile.subscription_plan === 'free' ? 'usuario' : 'pro';\n                                return _context3.abrupt(\"return\", {\n                                    allowed: false,\n                                    reason: \"L\\xEDmite mensual de tokens alcanzado. Necesitas \".concat(tokensRequired, \" tokens pero solo tienes \").concat(tokenInfo.remaining, \" disponibles.\"),\n                                    tokenInfo: tokenInfo,\n                                    upgradeRequired: true,\n                                    suggestedPlan: suggestedPlan\n                                });\n                            case 6:\n                                return _context3.abrupt(\"return\", {\n                                    allowed: true,\n                                    tokenInfo: tokenInfo\n                                });\n                            case 7:\n                            case \"end\":\n                                return _context3.stop();\n                        }\n                    }, _callee3);\n                }));\n                function checkTokenLimits(_x5, _x6) {\n                    return _checkTokenLimits.apply(this, arguments);\n                }\n                return checkTokenLimits;\n            }()\n        },\n        {\n            key: \"getTokenInfo\",\n            value: function getTokenInfo(profile) {\n                var currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                var currentTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;\n                return {\n                    current: currentTokens,\n                    limit: profile.monthly_token_limit,\n                    remaining: profile.monthly_token_limit - currentTokens,\n                    percentage: Math.round(currentTokens / profile.monthly_token_limit * 100)\n                };\n            }\n        },\n        {\n            key: \"getSuggestedPlan\",\n            value: function getSuggestedPlan(currentPlan, requiredPlans) {\n                // Si el usuario tiene plan gratuito, sugerir el plan más bajo requerido\n                if (currentPlan === 'free') {\n                    return requiredPlans[0];\n                }\n                // Si el usuario tiene plan usuario y se requiere pro, sugerir pro\n                if (currentPlan === 'usuario' && requiredPlans.includes('pro')) {\n                    return 'pro';\n                }\n                // Por defecto, sugerir el plan más alto\n                return requiredPlans[requiredPlans.length - 1];\n            }\n        },\n        {\n            key: \"getPlansWithFeature\",\n            value: function getPlansWithFeature(feature) {\n                var plans = [\n                    'free',\n                    'usuario',\n                    'pro'\n                ];\n                var plansWithFeature = [];\n                for(var _i = 0, _plans = plans; _i < _plans.length; _i++){\n                    var plan = _plans[_i];\n                    if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.hasFeatureAccess)(plan, feature)) {\n                        plansWithFeature.push(plan);\n                    }\n                }\n                return plansWithFeature;\n            }\n        },\n        {\n            key: \"checkClientPermission\",\n            value: function() {\n                var _checkClientPermission = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().mark(function _callee4(permission) {\n                    var supabase, _yield$supabase$auth$, user, authError, _yield$supabase$from$, profile, profileError, planConfig, requiredPlans, tokenCheck, tokenInfo;\n                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_4___default().wrap(function _callee4$(_context4) {\n                        while(1)switch(_context4.prev = _context4.next){\n                            case 0:\n                                _context4.prev = 0;\n                                supabase = (0,_lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_5__.createClient)();\n                                _context4.next = 4;\n                                return supabase.auth.getUser();\n                            case 4:\n                                _yield$supabase$auth$ = _context4.sent;\n                                user = _yield$supabase$auth$.data.user;\n                                authError = _yield$supabase$auth$.error;\n                                if (!(authError || !user)) {\n                                    _context4.next = 9;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Usuario no autenticado'\n                                });\n                            case 9:\n                                _context4.next = 11;\n                                return supabase.from('user_profiles').select('subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month').eq('user_id', user.id).single();\n                            case 11:\n                                _yield$supabase$from$ = _context4.sent;\n                                profile = _yield$supabase$from$.data;\n                                profileError = _yield$supabase$from$.error;\n                                if (!(profileError && profileError.code !== 'PGRST116')) {\n                                    _context4.next = 17;\n                                    break;\n                                }\n                                console.error(\"Error fetching profile for permission check:\", profileError);\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Error al obtener perfil de usuario'\n                                });\n                            case 17:\n                                if (profile) {\n                                    _context4.next = 19;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Perfil de usuario no encontrado'\n                                });\n                            case 19:\n                                if (!(permission.requiresPayment !== false && profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                                    _context4.next = 21;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Pago no verificado',\n                                    userPlan: profile.subscription_plan,\n                                    upgradeRequired: false\n                                });\n                            case 21:\n                                if (!(permission.minimumPlan && permission.minimumPlan.length > 0)) {\n                                    _context4.next = 24;\n                                    break;\n                                }\n                                if (permission.minimumPlan.includes(profile.subscription_plan)) {\n                                    _context4.next = 24;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"Esta funci\\xF3n requiere plan \".concat(permission.minimumPlan.join(' o ')),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: permission.minimumPlan,\n                                    upgradeRequired: true,\n                                    suggestedPlan: this.getSuggestedPlan(profile.subscription_plan, permission.minimumPlan)\n                                });\n                            case 24:\n                                if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.hasFeatureAccess)(profile.subscription_plan, permission.feature)) {\n                                    _context4.next = 28;\n                                    break;\n                                }\n                                planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_6__.getPlanConfiguration)(profile.subscription_plan);\n                                requiredPlans = this.getPlansWithFeature(permission.feature);\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: \"La funci\\xF3n \".concat(permission.feature, \" no est\\xE1 disponible en \").concat((planConfig === null || planConfig === void 0 ? void 0 : planConfig.name) || profile.subscription_plan),\n                                    userPlan: profile.subscription_plan,\n                                    requiredPlan: requiredPlans,\n                                    upgradeRequired: true,\n                                    suggestedPlan: requiredPlans[0]\n                                });\n                            case 28:\n                                if (!(permission.tokensRequired && permission.tokensRequired > 0)) {\n                                    _context4.next = 34;\n                                    break;\n                                }\n                                _context4.next = 31;\n                                return this.checkTokenLimits(profile, permission.tokensRequired);\n                            case 31:\n                                tokenCheck = _context4.sent;\n                                if (tokenCheck.allowed) {\n                                    _context4.next = 34;\n                                    break;\n                                }\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: tokenCheck.reason,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenCheck.tokenInfo,\n                                    upgradeRequired: tokenCheck.upgradeRequired,\n                                    suggestedPlan: tokenCheck.suggestedPlan\n                                });\n                            case 34:\n                                // Si llegamos aquí, el permiso está concedido\n                                tokenInfo = this.getTokenInfo(profile);\n                                return _context4.abrupt(\"return\", {\n                                    granted: true,\n                                    userPlan: profile.subscription_plan,\n                                    tokenInfo: tokenInfo\n                                });\n                            case 38:\n                                _context4.prev = 38;\n                                _context4.t0 = _context4[\"catch\"](0);\n                                console.error('Error checking client permission:', _context4.t0);\n                                return _context4.abrupt(\"return\", {\n                                    granted: false,\n                                    reason: 'Error de verificación'\n                                });\n                            case 42:\n                            case \"end\":\n                                return _context4.stop();\n                        }\n                    }, _callee4, this, [\n                        [\n                            0,\n                            38\n                        ]\n                    ]);\n                }));\n                function checkClientPermission(_x7) {\n                    return _checkClientPermission.apply(this, arguments);\n                }\n                return checkClientPermission;\n            }()\n        },\n        {\n            key: \"createFeaturePermission\",\n            value: function createFeaturePermission(feature) {\n                var tokensRequired = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n                var featureRequirements = {\n                    'test_generation': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'flashcard_generation': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'mind_map_generation': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'ai_tutor_chat': {\n                        minimumPlan: [\n                            'usuario',\n                            'pro'\n                        ]\n                    },\n                    'study_planning': {\n                        minimumPlan: [\n                            'pro'\n                        ]\n                    },\n                    'summary_a1_a2': {\n                        minimumPlan: [\n                            'pro'\n                        ]\n                    },\n                    'document_upload': {\n                        minimumPlan: [\n                            'free',\n                            'usuario',\n                            'pro'\n                        ]\n                    }\n                };\n                var requirements = featureRequirements[feature] || {};\n                return _objectSpread({\n                    feature: feature,\n                    tokensRequired: tokensRequired,\n                    requiresPayment: true\n                }, requirements);\n            }\n        }\n    ]);\n    return PermissionService;\n}();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/permissionService.ts\n"));

/***/ })

});