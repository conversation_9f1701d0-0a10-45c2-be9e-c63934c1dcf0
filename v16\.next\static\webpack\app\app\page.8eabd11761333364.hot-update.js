"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/TokenProgressBar.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenProgressBar)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenProgressBar.tsx\";\n\n\nfunction TokenProgressBar(_ref) {\n    var used = _ref.used, limit = _ref.limit, percentage = _ref.percentage, remaining = _ref.remaining;\n    // Validaciones defensivas para evitar errores con valores null/undefined\n    var safeUsed = used || 0;\n    var safeLimit = limit || 0;\n    var safePercentage = percentage || 0;\n    var safeRemaining = remaining || 0;\n    // Determinar color según el porcentaje de uso\n    var getProgressColor = function getProgressColor(percentage) {\n        if (percentage < 50) return 'bg-green-500';\n        if (percentage < 80) return 'bg-yellow-500';\n        return 'bg-red-500';\n    };\n    var getProgressBgColor = function getProgressBgColor(percentage) {\n        if (percentage < 50) return 'bg-green-100';\n        if (percentage < 80) return 'bg-yellow-100';\n        return 'bg-red-100';\n    };\n    var formatTokens = function formatTokens(tokens) {\n        // Validación defensiva\n        var validTokens = tokens || 0;\n        if (validTokens >= 1000000) {\n            return \"\".concat((validTokens / 1000000).toFixed(1), \"M\");\n        }\n        if (validTokens >= 1000) {\n            return \"\".concat((validTokens / 1000).toFixed(1), \"K\");\n        }\n        return validTokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"space-y-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-gray-700\",\n                        children: \"Uso de Tokens\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-semibold \".concat(safePercentage < 50 ? 'text-green-600' : safePercentage < 80 ? 'text-yellow-600' : 'text-red-600'),\n                        children: [\n                            safePercentage,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"w-full \".concat(getProgressBgColor(safePercentage), \" rounded-full h-3\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"h-3 rounded-full transition-all duration-300 \".concat(getProgressColor(safePercentage)),\n                    style: {\n                        width: \"\".concat(Math.min(safePercentage, 100), \"%\")\n                    }\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between text-xs text-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"strong\", {\n                                children: formatTokens(safeUsed)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            \" usados\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"strong\", {\n                                children: formatTokens(safeRemaining)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            \" restantes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                    className: \"text-xs text-gray-500\",\n                    children: [\n                        \"L\\xEDmite mensual: \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"strong\", {\n                            children: formatTokens(safeLimit)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 80,\n                            columnNumber: 27\n                        }, this),\n                        \" tokens\"\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            safePercentage >= 80 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                className: \"p-2 rounded-lg text-xs \".concat(safePercentage >= 95 ? 'bg-red-50 text-red-700 border border-red-200' : 'bg-yellow-50 text-yellow-700 border border-yellow-200'),\n                children: safePercentage >= 95 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                    children: \"\\u26A0\\uFE0F L\\xEDmite casi alcanzado. Considera comprar m\\xE1s tokens.\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 13\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                    children: \"\\u26A0\\uFE0F Te est\\xE1s acercando al l\\xEDmite mensual de tokens.\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_c1 = TokenProgressBar;\n_c = TokenProgressBar;\nvar _c;\n$RefreshReg$(_c, \"TokenProgressBar\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenProgressBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\n"));

/***/ })

});