"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts":
/*!**********************************************!*\
  !*** ./src/hooks/useBackgroundGeneration.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBackgroundGeneration: () => (/* binding */ useBackgroundGeneration)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(app-pages-browser)/./src/contexts/BackgroundTasksContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useBackgroundGeneration auto */ \nvar _s = $RefreshSig$();\n\n\n\nvar useBackgroundGeneration = function useBackgroundGeneration() {\n    _s();\n    var _useBackgroundTasks = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.useBackgroundTasks)(), addTask = _useBackgroundTasks.addTask, updateTask = _useBackgroundTasks.updateTask, getTasksByType = _useBackgroundTasks.getTasksByType;\n    var executeGeneration = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ ({\n        \"useBackgroundGeneration.useCallback[executeGeneration]\": function() {\n            var _ref = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(type, action, options) {\n                var peticion, contextos, cantidad, onComplete, onError, taskId, response, errorText, responseData, _result, errorMessage;\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n                    while(1)switch(_context.prev = _context.next){\n                        case 0:\n                            peticion = options.peticion, contextos = options.contextos, cantidad = options.cantidad, onComplete = options.onComplete, onError = options.onError; // Crear la tarea en segundo plano\n                            taskId = addTask({\n                                type: type,\n                                title: peticion.length > 50 ? \"\".concat(peticion.substring(0, 50), \"...\") : peticion\n                            });\n                            _context.prev = 2;\n                            // Marcar como procesando\n                            updateTask(taskId, {\n                                status: 'processing'\n                            });\n                            console.log(\"\\uD83D\\uDE80 Iniciando generaci\\xF3n de \".concat(type, \":\"), {\n                                action: action,\n                                peticion: peticion,\n                                contextos: contextos === null || contextos === void 0 ? void 0 : contextos.length,\n                                cantidad: cantidad\n                            });\n                            // Realizar la petición\n                            _context.next = 7;\n                            return fetch('/api/ai', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    action: action,\n                                    peticion: peticion,\n                                    contextos: contextos,\n                                    cantidad: cantidad\n                                })\n                            });\n                        case 7:\n                            response = _context.sent;\n                            console.log(\"\\uD83D\\uDCE1 Respuesta recibida para \".concat(type, \":\"), response.status, response.ok);\n                            if (response.ok) {\n                                _context.next = 15;\n                                break;\n                            }\n                            _context.next = 12;\n                            return response.text();\n                        case 12:\n                            errorText = _context.sent;\n                            console.error(\"\\u274C Error en la API para \".concat(type, \":\"), response.status, errorText);\n                            throw new Error(\"Error en la API: \".concat(response.status, \" - \").concat(errorText));\n                        case 15:\n                            _context.next = 17;\n                            return response.json();\n                        case 17:\n                            responseData = _context.sent;\n                            console.log(\"\\u2705 Resultado obtenido para \".concat(type, \":\"), responseData);\n                            _result = responseData.result; // Marcar como completado\n                            updateTask(taskId, {\n                                status: 'completed',\n                                result: _result,\n                                progress: 100\n                            });\n                            // Ejecutar callback de éxito de forma asíncrona para evitar problemas de renderizado\n                            if (onComplete) {\n                                setTimeout({\n                                    \"useBackgroundGeneration.useCallback[executeGeneration]._ref._callee._callee$\": function() {\n                                        return onComplete(_result);\n                                    }\n                                }[\"useBackgroundGeneration.useCallback[executeGeneration]._ref._callee._callee$\"], 0);\n                            }\n                            return _context.abrupt(\"return\", taskId);\n                        case 25:\n                            _context.prev = 25;\n                            _context.t0 = _context[\"catch\"](2);\n                            errorMessage = _context.t0 instanceof Error ? _context.t0.message : 'Error desconocido'; // Marcar como error\n                            updateTask(taskId, {\n                                status: 'error',\n                                error: errorMessage\n                            });\n                            // Ejecutar callback de error de forma asíncrona para evitar problemas de renderizado\n                            if (onError) {\n                                setTimeout({\n                                    \"useBackgroundGeneration.useCallback[executeGeneration]._ref._callee._callee$\": function() {\n                                        return onError(errorMessage);\n                                    }\n                                }[\"useBackgroundGeneration.useCallback[executeGeneration]._ref._callee._callee$\"], 0);\n                            }\n                            throw _context.t0;\n                        case 31:\n                        case \"end\":\n                            return _context.stop();\n                    }\n                }, _callee, null, [\n                    [\n                        2,\n                        25\n                    ]\n                ]);\n            }));\n            return ({\n                \"useBackgroundGeneration.useCallback[executeGeneration]\": function(_x, _x2, _x3) {\n                    return _ref.apply(this, arguments);\n                }\n            })[\"useBackgroundGeneration.useCallback[executeGeneration]\"];\n        }\n    })[\"useBackgroundGeneration.useCallback[executeGeneration]\"](), [\n        addTask,\n        updateTask\n    ]);\n    var generateMapaMental = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ ({\n        \"useBackgroundGeneration.useCallback[generateMapaMental]\": function() {\n            var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(options) {\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee2$(_context2) {\n                    while(1)switch(_context2.prev = _context2.next){\n                        case 0:\n                            return _context2.abrupt(\"return\", executeGeneration('mapa-mental', 'generarMapaMental', options));\n                        case 1:\n                        case \"end\":\n                            return _context2.stop();\n                    }\n                }, _callee2);\n            }));\n            return ({\n                \"useBackgroundGeneration.useCallback[generateMapaMental]\": function(_x4) {\n                    return _ref2.apply(this, arguments);\n                }\n            })[\"useBackgroundGeneration.useCallback[generateMapaMental]\"];\n        }\n    })[\"useBackgroundGeneration.useCallback[generateMapaMental]\"](), [\n        executeGeneration\n    ]);\n    var generateTest = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ ({\n        \"useBackgroundGeneration.useCallback[generateTest]\": function() {\n            var _ref3 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee3(options) {\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee3$(_context3) {\n                    while(1)switch(_context3.prev = _context3.next){\n                        case 0:\n                            return _context3.abrupt(\"return\", executeGeneration('test', 'generarTest', options));\n                        case 1:\n                        case \"end\":\n                            return _context3.stop();\n                    }\n                }, _callee3);\n            }));\n            return ({\n                \"useBackgroundGeneration.useCallback[generateTest]\": function(_x5) {\n                    return _ref3.apply(this, arguments);\n                }\n            })[\"useBackgroundGeneration.useCallback[generateTest]\"];\n        }\n    })[\"useBackgroundGeneration.useCallback[generateTest]\"](), [\n        executeGeneration\n    ]);\n    var generateFlashcards = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ ({\n        \"useBackgroundGeneration.useCallback[generateFlashcards]\": function() {\n            var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee4(options) {\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee4$(_context4) {\n                    while(1)switch(_context4.prev = _context4.next){\n                        case 0:\n                            return _context4.abrupt(\"return\", executeGeneration('flashcards', 'generarFlashcards', options));\n                        case 1:\n                        case \"end\":\n                            return _context4.stop();\n                    }\n                }, _callee4);\n            }));\n            return ({\n                \"useBackgroundGeneration.useCallback[generateFlashcards]\": function(_x6) {\n                    return _ref4.apply(this, arguments);\n                }\n            })[\"useBackgroundGeneration.useCallback[generateFlashcards]\"];\n        }\n    })[\"useBackgroundGeneration.useCallback[generateFlashcards]\"](), [\n        executeGeneration\n    ]);\n    var generateResumen = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ ({\n        \"useBackgroundGeneration.useCallback[generateResumen]\": function() {\n            var _ref5 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee5(options) {\n                var documento, instrucciones, onComplete, onError, requestData;\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee5$(_context5) {\n                    while(1)switch(_context5.prev = _context5.next){\n                        case 0:\n                            documento = options.documento, instrucciones = options.instrucciones, onComplete = options.onComplete, onError = options.onError; // Preparar datos específicos para resúmenes\n                            requestData = {\n                                action: 'generarResumen',\n                                peticion: \"\".concat(documento.titulo, \"|\").concat(documento.categoria || '', \"|\").concat(documento.numero_tema || '', \"|\").concat(instrucciones),\n                                contextos: [\n                                    documento.contenido\n                                ]\n                            };\n                            return _context5.abrupt(\"return\", executeGeneration('resumen', 'generarResumen', {\n                                peticion: requestData.peticion,\n                                contextos: requestData.contextos,\n                                onComplete: onComplete,\n                                onError: onError\n                            }));\n                        case 3:\n                        case \"end\":\n                            return _context5.stop();\n                    }\n                }, _callee5);\n            }));\n            return ({\n                \"useBackgroundGeneration.useCallback[generateResumen]\": function(_x7) {\n                    return _ref5.apply(this, arguments);\n                }\n            })[\"useBackgroundGeneration.useCallback[generateResumen]\"];\n        }\n    })[\"useBackgroundGeneration.useCallback[generateResumen]\"](), [\n        executeGeneration\n    ]);\n    var generatePlanEstudios = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ ({\n        \"useBackgroundGeneration.useCallback[generatePlanEstudios]\": function() {\n            var _ref6 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee6(options) {\n                var temarioId, onComplete, onError, taskId, response, _yield$response$json, _result2, errorMessage;\n                return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee6$(_context6) {\n                    while(1)switch(_context6.prev = _context6.next){\n                        case 0:\n                            temarioId = options.temarioId, onComplete = options.onComplete, onError = options.onError; // Crear la tarea en segundo plano\n                            taskId = addTask({\n                                type: 'plan-estudios',\n                                title: 'Generando plan de estudios personalizado'\n                            });\n                            _context6.prev = 2;\n                            // Marcar como procesando\n                            updateTask(taskId, {\n                                status: 'processing'\n                            });\n                            // Realizar la petición usando la interfaz estándar\n                            _context6.next = 6;\n                            return fetch('/api/ai', {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify({\n                                    action: 'generarPlanEstudios',\n                                    peticion: temarioId,\n                                    // Usar peticion en lugar de temarioId para consistencia\n                                    contextos: [] // Contextos vacíos para mantener la interfaz estándar\n                                })\n                            });\n                        case 6:\n                            response = _context6.sent;\n                            if (response.ok) {\n                                _context6.next = 9;\n                                break;\n                            }\n                            throw new Error(\"Error en la API: \".concat(response.status));\n                        case 9:\n                            _context6.next = 11;\n                            return response.json();\n                        case 11:\n                            _yield$response$json = _context6.sent;\n                            _result2 = _yield$response$json.result;\n                            // Marcar como completado\n                            updateTask(taskId, {\n                                status: 'completed',\n                                result: _result2,\n                                progress: 100\n                            });\n                            // Ejecutar callback de éxito de forma asíncrona para evitar problemas de renderizado\n                            if (onComplete) {\n                                setTimeout({\n                                    \"useBackgroundGeneration.useCallback[generatePlanEstudios]._ref6._callee6._callee6$\": function() {\n                                        return onComplete(_result2);\n                                    }\n                                }[\"useBackgroundGeneration.useCallback[generatePlanEstudios]._ref6._callee6._callee6$\"], 0);\n                            }\n                            return _context6.abrupt(\"return\", taskId);\n                        case 18:\n                            _context6.prev = 18;\n                            _context6.t0 = _context6[\"catch\"](2);\n                            errorMessage = _context6.t0 instanceof Error ? _context6.t0.message : 'Error desconocido'; // Marcar como error\n                            updateTask(taskId, {\n                                status: 'error',\n                                error: errorMessage\n                            });\n                            // Ejecutar callback de error de forma asíncrona para evitar problemas de renderizado\n                            if (onError) {\n                                setTimeout({\n                                    \"useBackgroundGeneration.useCallback[generatePlanEstudios]._ref6._callee6._callee6$\": function() {\n                                        return onError(errorMessage);\n                                    }\n                                }[\"useBackgroundGeneration.useCallback[generatePlanEstudios]._ref6._callee6._callee6$\"], 0);\n                            }\n                            throw _context6.t0;\n                        case 24:\n                        case \"end\":\n                            return _context6.stop();\n                    }\n                }, _callee6, null, [\n                    [\n                        2,\n                        18\n                    ]\n                ]);\n            }));\n            return ({\n                \"useBackgroundGeneration.useCallback[generatePlanEstudios]\": function(_x8) {\n                    return _ref6.apply(this, arguments);\n                }\n            })[\"useBackgroundGeneration.useCallback[generatePlanEstudios]\"];\n        }\n    })[\"useBackgroundGeneration.useCallback[generatePlanEstudios]\"](), [\n        addTask,\n        updateTask\n    ]);\n    var isGenerating = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useBackgroundGeneration.useCallback[isGenerating]\": function(type) {\n            var tasks = getTasksByType(type);\n            return tasks.some({\n                \"useBackgroundGeneration.useCallback[isGenerating]\": function(task) {\n                    return task.status === 'pending' || task.status === 'processing';\n                }\n            }[\"useBackgroundGeneration.useCallback[isGenerating]\"]);\n        }\n    }[\"useBackgroundGeneration.useCallback[isGenerating]\"], [\n        getTasksByType\n    ]);\n    var getActiveTask = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)({\n        \"useBackgroundGeneration.useCallback[getActiveTask]\": function(type) {\n            var tasks = getTasksByType(type);\n            return tasks.find({\n                \"useBackgroundGeneration.useCallback[getActiveTask]\": function(task) {\n                    return task.status === 'pending' || task.status === 'processing';\n                }\n            }[\"useBackgroundGeneration.useCallback[getActiveTask]\"]);\n        }\n    }[\"useBackgroundGeneration.useCallback[getActiveTask]\"], [\n        getTasksByType\n    ]);\n    return {\n        generateMapaMental: generateMapaMental,\n        generateTest: generateTest,\n        generateFlashcards: generateFlashcards,\n        generatePlanEstudios: generatePlanEstudios,\n        isGenerating: isGenerating,\n        getActiveTask: getActiveTask\n    };\n};\n_s(useBackgroundGeneration, \"K2bXBX3UXLCiJmQ2D5eNAa+Wixk=\", false, function() {\n    return [\n        _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_3__.useBackgroundTasks\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\n"));

/***/ })

});