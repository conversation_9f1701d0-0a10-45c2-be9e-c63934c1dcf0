/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/stripe/webhook/route";
exports.ids = ["app/api/stripe/webhook/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_stripe_webhook_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/stripe/webhook/route.ts */ \"(rsc)/./src/app/api/stripe/webhook/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/stripe/webhook/route\",\n        pathname: \"/api/stripe/webhook\",\n        filename: \"route\",\n        bundlePath: \"app/api/stripe/webhook/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\stripe\\\\webhook\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_stripe_webhook_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/stripe/webhook/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/stripe/webhook/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stripe/config */ \"(rsc)/./src/lib/stripe/config.ts\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/stripeWebhookHandlers */ \"(rsc)/./src/lib/services/stripeWebhookHandlers.ts\");\n// src/app/api/stripe/webhook/route.ts\n\n\n\n\nasync function POST(request) {\n    const startTime = Date.now();\n    try {\n        const body = await request.text();\n        const headersList = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.headers)();\n        const signature = headersList.get('stripe-signature');\n        if (!signature) {\n            console.error('❌ No Stripe signature found');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No signature found'\n            }, {\n                status: 400\n            });\n        }\n        const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n        if (!webhookSecret) {\n            console.error('❌ No webhook secret configured');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Webhook secret not configured'\n            }, {\n                status: 500\n            });\n        }\n        if (!_lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe) {\n            console.error('❌ Stripe not initialized');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Stripe not configured'\n            }, {\n                status: 500\n            });\n        }\n        // Verificar el evento de webhook\n        let event;\n        try {\n            event = _lib_stripe_config__WEBPACK_IMPORTED_MODULE_1__.stripe.webhooks.constructEvent(body, signature, webhookSecret);\n        } catch (err) {\n            console.error('❌ Webhook signature verification failed:', err);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid signature'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🎯 Webhook event received:', event.type, 'ID:', event.id);\n        // Manejar diferentes tipos de eventos con manejadores específicos\n        let result;\n        switch(event.type){\n            case 'checkout.session.completed':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleCheckoutSessionCompleted(event.data.object);\n                break;\n            case 'payment_intent.succeeded':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handlePaymentIntentSucceeded(event.data.object);\n                break;\n            case 'invoice.payment_succeeded':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleInvoicePaymentSucceeded(event.data.object);\n                break;\n            case 'customer.subscription.created':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleSubscriptionCreated(event.data.object);\n                break;\n            case 'customer.subscription.updated':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleSubscriptionUpdated(event.data.object);\n                break;\n            case 'customer.subscription.deleted':\n                result = await _lib_services_stripeWebhookHandlers__WEBPACK_IMPORTED_MODULE_3__.StripeWebhookHandlers.handleSubscriptionDeleted(event.data.object);\n                break;\n            default:\n                console.log('⚠️ Unhandled event type:', event.type);\n                result = {\n                    success: true,\n                    message: `Event type ${event.type} not handled`\n                };\n        }\n        // Log del resultado\n        const processingTime = Date.now() - startTime;\n        console.log(`✅ Webhook processed in ${processingTime}ms:`, {\n            eventType: event.type,\n            eventId: event.id,\n            success: result.success,\n            message: result.message\n        });\n        // Responder según el resultado\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                received: true,\n                processed: true,\n                eventType: event.type,\n                message: result.message,\n                data: result.data,\n                processingTime\n            });\n        } else {\n            console.error('❌ Webhook processing failed:', result.error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                received: true,\n                processed: false,\n                eventType: event.type,\n                error: result.message,\n                details: result.error\n            }, {\n                status: 422\n            }); // Unprocessable Entity\n        }\n    } catch (error) {\n        const processingTime = Date.now() - startTime;\n        console.error('❌ Critical webhook error:', {\n            error: error instanceof Error ? error.message : 'Unknown error',\n            stack: error instanceof Error ? error.stack : undefined,\n            processingTime\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Webhook handler failed',\n            details: error instanceof Error ? error.message : 'Unknown error',\n            processingTime\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/stripe/webhook/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailAnalytics.ts":
/*!**************************************************!*\
  !*** ./src/lib/services/email/emailAnalytics.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailAnalytics: () => (/* binding */ EmailAnalytics)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/lib/services/email/emailAnalytics.ts\n// Análisis y estadísticas de notificaciones por email\n\nclass EmailAnalytics {\n    /**\n   * Obtener estadísticas de notificaciones por tipo\n   */ static async getNotificationStats(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            const byType = (notifications || []).reduce((acc, notif)=>{\n                acc[notif.type] = (acc[notif.type] || 0) + 1;\n                return acc;\n            }, {});\n            const byStatus = (notifications || []).reduce((acc, notif)=>{\n                acc[notif.status] = (acc[notif.status] || 0) + 1;\n                return acc;\n            }, {});\n            const recentNotifications = (notifications || []).sort((a, b)=>new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime()).slice(0, 10);\n            return {\n                byType,\n                byStatus,\n                total: notifications?.length || 0,\n                recentNotifications\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas de notificaciones:', error);\n            return {\n                byType: {},\n                byStatus: {},\n                total: 0,\n                recentNotifications: []\n            };\n        }\n    }\n    /**\n   * Obtener estadísticas de fallos y errores\n   */ static async getFailureStats(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('status', 'failed');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: failures, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Obtener total de notificaciones para calcular tasa de fallo\n            let totalQuery = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*', {\n                count: 'exact',\n                head: true\n            });\n            if (startDate) {\n                totalQuery = totalQuery.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                totalQuery = totalQuery.lte('sent_at', endDate);\n            }\n            const { count: totalCount } = await totalQuery;\n            // Agrupar errores por tipo\n            const errorsByType = (failures || []).reduce((acc, failure)=>{\n                const errorMessage = failure.metadata?.error_message || 'Unknown error';\n                const errorType = this.categorizeError(errorMessage);\n                acc[errorType] = (acc[errorType] || 0) + 1;\n                return acc;\n            }, {});\n            const totalFailures = failures?.length || 0;\n            const failureRate = totalCount && totalCount > 0 ? totalFailures / totalCount * 100 : 0;\n            return {\n                totalFailures,\n                failureRate: Math.round(failureRate * 100) / 100,\n                // Redondear a 2 decimales\n                errorsByType,\n                recentFailures: (failures || []).sort((a, b)=>new Date(b.sent_at).getTime() - new Date(a.sent_at).getTime()).slice(0, 10).map((failure)=>({\n                        id: failure.id,\n                        type: failure.type,\n                        recipient: failure.recipient_email,\n                        error: failure.metadata?.error_message || 'Unknown error',\n                        failedAt: failure.metadata?.failed_at || failure.sent_at\n                    }))\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas de fallos:', error);\n            return {\n                totalFailures: 0,\n                failureRate: 0,\n                errorsByType: {},\n                recentFailures: []\n            };\n        }\n    }\n    /**\n   * Categorizar errores para estadísticas\n   */ static categorizeError(errorMessage) {\n        const message = errorMessage.toLowerCase();\n        if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {\n            return 'Network Error';\n        }\n        if (message.includes('invalid') || message.includes('malformed') || message.includes('email')) {\n            return 'Invalid Email';\n        }\n        if (message.includes('rate limit') || message.includes('quota') || message.includes('limit')) {\n            return 'Rate Limit';\n        }\n        if (message.includes('auth') || message.includes('key') || message.includes('permission')) {\n            return 'Authentication Error';\n        }\n        if (message.includes('bounce') || message.includes('reject')) {\n            return 'Email Bounced';\n        }\n        return 'Other Error';\n    }\n    /**\n   * Obtener métricas de rendimiento por período\n   */ static async getPerformanceMetrics(startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*');\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            const totalSent = notifications?.length || 0;\n            const successful = notifications?.filter((n)=>n.status === 'sent').length || 0;\n            const successRate = totalSent > 0 ? successful / totalSent * 100 : 0;\n            // Agrupar por hora del día para encontrar picos\n            const peakHours = (notifications || []).reduce((acc, notif)=>{\n                const hour = new Date(notif.sent_at).getHours();\n                acc[hour] = (acc[hour] || 0) + 1;\n                return acc;\n            }, {});\n            // Agrupar por día\n            const dailyVolume = (notifications || []).reduce((acc, notif)=>{\n                const day = notif.sent_at.split('T')[0];\n                acc[day] = (acc[day] || 0) + 1;\n                return acc;\n            }, {});\n            return {\n                totalSent,\n                successRate: Math.round(successRate * 100) / 100,\n                avgResponseTime: 0,\n                // TODO: Implementar si se trackea tiempo de respuesta\n                peakHours,\n                dailyVolume\n            };\n        } catch (error) {\n            console.error('Error obteniendo métricas de rendimiento:', error);\n            return {\n                totalSent: 0,\n                successRate: 0,\n                avgResponseTime: 0,\n                peakHours: {},\n                dailyVolume: {}\n            };\n        }\n    }\n    /**\n   * Obtener top usuarios por volumen de notificaciones\n   */ static async getTopUsersByVolume(limit = 10, startDate, endDate) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('user_id, recipient_email, sent_at').not('user_id', 'is', null);\n            if (startDate) {\n                query = query.gte('sent_at', startDate);\n            }\n            if (endDate) {\n                query = query.lte('sent_at', endDate);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Agrupar por usuario\n            const userStats = (notifications || []).reduce((acc, notif)=>{\n                const userId = notif.user_id;\n                if (!acc[userId]) {\n                    acc[userId] = {\n                        userId,\n                        email: notif.recipient_email,\n                        count: 0,\n                        lastNotification: notif.sent_at\n                    };\n                }\n                acc[userId].count++;\n                if (new Date(notif.sent_at) > new Date(acc[userId].lastNotification)) {\n                    acc[userId].lastNotification = notif.sent_at;\n                }\n                return acc;\n            }, {});\n            // Convertir a array y ordenar por count\n            return Object.values(userStats).sort((a, b)=>b.count - a.count).slice(0, limit);\n        } catch (error) {\n            console.error('Error obteniendo top usuarios:', error);\n            return [];\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailAnalytics.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailLogger.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/email/emailLogger.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailLogger: () => (/* binding */ EmailLogger)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n// src/lib/services/email/emailLogger.ts\n// Logging y tracking de notificaciones en base de datos\n\nclass EmailLogger {\n    /**\n   * Registrar notificación en base de datos para tracking\n   */ static async logEmailNotification(notification, status = 'sent') {\n        try {\n            const insertData = {\n                recipient_email: notification.to,\n                subject: notification.subject,\n                type: notification.type,\n                sent_at: new Date().toISOString(),\n                status: status\n            };\n            // Agregar user_id si está disponible\n            if (notification.userId) {\n                insertData.user_id = notification.userId;\n            }\n            // Agregar metadata si está disponible\n            if (notification.metadata) {\n                insertData.metadata = notification.metadata;\n            }\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').insert(insertData).select('id').single();\n            if (error) {\n                throw error;\n            }\n            console.log('📝 Email notification logged:', {\n                id: data.id,\n                type: notification.type,\n                recipient: notification.to,\n                status: status,\n                userId: notification.userId || 'N/A'\n            });\n            return data.id;\n        } catch (error) {\n            console.error('Error logging email notification:', error);\n            // No lanzar error, es solo para tracking\n            return null;\n        }\n    }\n    /**\n   * Actualizar estado de una notificación existente\n   */ static async updateEmailNotificationStatus(notificationId, status, errorMessage) {\n        try {\n            const updateData = {\n                status: status,\n                updated_at: new Date().toISOString()\n            };\n            // Si es un fallo, agregar el mensaje de error a metadata\n            if (status === 'failed' && errorMessage) {\n                updateData.metadata = {\n                    error_message: errorMessage,\n                    failed_at: new Date().toISOString()\n                };\n            }\n            // Si es exitoso, marcar como entregado\n            if (status === 'sent') {\n                updateData.delivered_at = new Date().toISOString();\n            }\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').update(updateData).eq('id', notificationId);\n            if (error) {\n                throw error;\n            }\n            console.log('📝 Email notification status updated:', {\n                id: notificationId,\n                status: status,\n                error: errorMessage || 'N/A'\n            });\n        } catch (error) {\n            console.error('Error updating email notification status:', error);\n        // No lanzar error, es solo para tracking\n        }\n    }\n    /**\n   * Obtener historial de notificaciones de un usuario\n   */ static async getUserNotifications(userId, limit = 50, type) {\n        try {\n            let query = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('user_id', userId).order('sent_at', {\n                ascending: false\n            });\n            if (type) {\n                query = query.eq('type', type);\n            }\n            if (limit) {\n                query = query.limit(limit);\n            }\n            const { data: notifications, error } = await query;\n            if (error) {\n                throw error;\n            }\n            // Obtener total de notificaciones\n            let countQuery = _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*', {\n                count: 'exact',\n                head: true\n            }).eq('user_id', userId);\n            if (type) {\n                countQuery = countQuery.eq('type', type);\n            }\n            const { count } = await countQuery;\n            return {\n                notifications: notifications || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error('Error obteniendo notificaciones del usuario:', error);\n            return {\n                notifications: [],\n                total: 0\n            };\n        }\n    }\n    /**\n   * Obtener notificaciones fallidas para reintentos\n   */ static async getFailedNotifications(maxAge = 24, // Máximo 24 horas de antigüedad\n    limit = 10 // Máximo 10 por consulta\n    ) {\n        try {\n            const cutoffDate = new Date(Date.now() - maxAge * 60 * 60 * 1000).toISOString();\n            const { data: failedNotifications, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').select('*').eq('status', 'failed').gte('sent_at', cutoffDate).limit(limit);\n            if (error) {\n                throw error;\n            }\n            return failedNotifications || [];\n        } catch (error) {\n            console.error('Error obteniendo notificaciones fallidas:', error);\n            return [];\n        }\n    }\n    /**\n   * Marcar notificación como reintentada\n   */ static async markAsRetried(originalNotificationId, success, errorMessage) {\n        try {\n            const status = success ? 'retried_successfully' : 'failed';\n            const message = success ? 'Successfully retried' : errorMessage || 'Retry failed';\n            await this.updateEmailNotificationStatus(originalNotificationId, status, message);\n        } catch (error) {\n            console.error('Error marcando notificación como reintentada:', error);\n        }\n    }\n    /**\n   * Limpiar notificaciones antiguas (para mantenimiento)\n   */ static async cleanupOldNotifications(daysToKeep = 90) {\n        try {\n            const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000).toISOString();\n            console.log(`🧹 Limpiando notificaciones anteriores a: ${cutoffDate}`);\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('email_notifications').delete().lt('sent_at', cutoffDate).select('id');\n            if (error) {\n                throw error;\n            }\n            const deletedCount = data?.length || 0;\n            console.log(`✅ Limpieza completada: ${deletedCount} notificaciones eliminadas`);\n            return {\n                deleted: deletedCount\n            };\n        } catch (error) {\n            console.error('Error en limpieza de notificaciones:', error);\n            return {\n                deleted: 0,\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailLogger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailNotificationService.ts":
/*!************************************************************!*\
  !*** ./src/lib/services/email/emailNotificationService.ts ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailNotificationService: () => (/* binding */ EmailNotificationService)\n/* harmony export */ });\n/* harmony import */ var _emailTemplates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./emailTemplates */ \"(rsc)/./src/lib/services/email/emailTemplates.ts\");\n/* harmony import */ var _emailSender__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./emailSender */ \"(rsc)/./src/lib/services/email/emailSender.ts\");\n/* harmony import */ var _emailLogger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./emailLogger */ \"(rsc)/./src/lib/services/email/emailLogger.ts\");\n/* harmony import */ var _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./emailAnalytics */ \"(rsc)/./src/lib/services/email/emailAnalytics.ts\");\n// src/lib/services/email/emailNotificationService.ts\n// Servicio principal de notificaciones por email (API pública)\n\n\n\n\nclass EmailNotificationService {\n    /**\n   * Enviar notificación de cancelación de suscripción\n   */ static async sendSubscriptionCancelledNotification(userEmail, userName, planName, gracePeriodEnd, userId) {\n        try {\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateSubscriptionCancelledEmail(userName, planName, gracePeriodEnd);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'subscription_cancelled',\n                userId,\n                metadata: {\n                    planName,\n                    gracePeriodEnd,\n                    userName,\n                    daysRemaining: Math.ceil((new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando notificación de cancelación:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar recordatorio de que el período de gracia está por terminar\n   */ static async sendGracePeriodEndingNotification(userEmail, userName, planName, gracePeriodEnd, userId) {\n        try {\n            // Generar contenido del email usando template\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateGracePeriodEndingEmail(userName, planName, gracePeriodEnd);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type: 'grace_period_ending',\n                userId,\n                metadata: {\n                    planName,\n                    gracePeriodEnd,\n                    userName,\n                    hoursRemaining: Math.ceil((new Date(gracePeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60))\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando recordatorio de período de gracia:', error);\n            return false;\n        }\n    }\n    /**\n   * Enviar notificación genérica\n   */ static async sendGenericNotification(userEmail, userName, title, message, type = 'other', userId, ctaText, ctaUrl) {\n        try {\n            // Generar contenido del email usando template genérico\n            const template = _emailTemplates__WEBPACK_IMPORTED_MODULE_0__.EmailTemplates.generateGenericEmail(userName, title, message, ctaText, ctaUrl);\n            // Crear notificación\n            const notification = {\n                to: userEmail,\n                subject: template.subject,\n                htmlContent: template.htmlContent,\n                textContent: template.textContent,\n                type,\n                userId,\n                metadata: {\n                    userName,\n                    title,\n                    message,\n                    ctaText,\n                    ctaUrl\n                }\n            };\n            // Enviar email\n            return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendEmail(notification);\n        } catch (error) {\n            console.error('Error enviando notificación genérica:', error);\n            return false;\n        }\n    }\n    /**\n   * Obtener historial de notificaciones de un usuario\n   */ static async getUserNotifications(userId, limit = 50, type) {\n        return await _emailLogger__WEBPACK_IMPORTED_MODULE_2__.EmailLogger.getUserNotifications(userId, limit, type);\n    }\n    /**\n   * Obtener estadísticas de notificaciones por tipo\n   */ static async getNotificationStats(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getNotificationStats(startDate, endDate);\n    }\n    /**\n   * Obtener estadísticas de fallos y errores\n   */ static async getFailureStats(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getFailureStats(startDate, endDate);\n    }\n    /**\n   * Reenviar notificaciones fallidas\n   */ static async retryFailedNotifications(maxAge = 24, limit = 10) {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.retryFailedNotifications(maxAge, limit);\n    }\n    /**\n   * Obtener métricas de rendimiento\n   */ static async getPerformanceMetrics(startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getPerformanceMetrics(startDate, endDate);\n    }\n    /**\n   * Obtener top usuarios por volumen de notificaciones\n   */ static async getTopUsersByVolume(limit = 10, startDate, endDate) {\n        return await _emailAnalytics__WEBPACK_IMPORTED_MODULE_3__.EmailAnalytics.getTopUsersByVolume(limit, startDate, endDate);\n    }\n    /**\n   * Enviar email de prueba\n   */ static async sendTestEmail(to, providerConfig) {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.sendTestEmail(to, providerConfig);\n    }\n    /**\n   * Validar configuración del proveedor de email\n   */ static async validateEmailProvider() {\n        return await _emailSender__WEBPACK_IMPORTED_MODULE_1__.EmailSender.validateEmailProvider();\n    }\n    /**\n   * Limpiar notificaciones antiguas\n   */ static async cleanupOldNotifications(daysToKeep = 90) {\n        return await _emailLogger__WEBPACK_IMPORTED_MODULE_2__.EmailLogger.cleanupOldNotifications(daysToKeep);\n    }\n    /**\n   * Obtener resumen del sistema de notificaciones\n   */ static async getSystemSummary() {\n        try {\n            const [providerStatus, recentStats, failureStats, performanceMetrics] = await Promise.all([\n                this.validateEmailProvider(),\n                this.getNotificationStats(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // Últimos 7 días\n                new Date().toISOString()),\n                this.getFailureStats(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString()),\n                this.getPerformanceMetrics(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), new Date().toISOString())\n            ]);\n            return {\n                providerStatus,\n                recentStats,\n                failureStats,\n                performanceMetrics\n            };\n        } catch (error) {\n            console.error('Error obteniendo resumen del sistema:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailNotificationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailSender.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/email/emailSender.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailSender: () => (/* binding */ EmailSender)\n/* harmony export */ });\n/* harmony import */ var _emailLogger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./emailLogger */ \"(rsc)/./src/lib/services/email/emailLogger.ts\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/services/email/emailSender.ts\n// Lógica de envío de emails con reintentos y manejo de errores\n\nclass EmailSender {\n    /**\n   * Función base para enviar emails con reintentos y manejo de errores\n   * NOTA: Implementar con tu proveedor de email preferido (SendGrid, Resend, etc.)\n   */ static async sendEmail(notification, retryCount = 0) {\n        const maxRetries = 3;\n        const retryDelay = Math.pow(2, retryCount) * 1000; // Backoff exponencial: 1s, 2s, 4s\n        let notificationId = null;\n        try {\n            console.log(`📧 Enviando email (intento ${retryCount + 1}/${maxRetries + 1}):`, {\n                to: notification.to,\n                subject: notification.subject,\n                type: notification.type\n            });\n            // Registrar notificación como 'pending' antes del envío\n            notificationId = await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.logEmailNotification(notification, 'pending');\n            // TODO: Implementar con tu proveedor de email real\n            // Ejemplo con Resend:\n            /*\n      const resend = new Resend(process.env.RESEND_API_KEY);\n      const result = await resend.emails.send({\n        from: 'OposI <<EMAIL>>',\n        to: notification.to,\n        subject: notification.subject,\n        html: notification.htmlContent,\n        text: notification.textContent,\n      });\n      \n      if (result.error) {\n        throw new Error(`Resend API error: ${result.error.message}`);\n      }\n      */ // Simulación de envío (remover cuando implementes proveedor real)\n            await new Promise((resolve)=>setTimeout(resolve, 100));\n            // Simular fallo ocasional para testing (remover en producción)\n            if (Math.random() < 0.1 && retryCount === 0) {\n                throw new Error('Simulated email provider error');\n            }\n            // Actualizar estado a 'sent' si el envío fue exitoso\n            if (notificationId) {\n                await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.updateEmailNotificationStatus(notificationId, 'sent');\n            }\n            console.log('✅ Email enviado exitosamente');\n            return true;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n            console.error(`❌ Error enviando email (intento ${retryCount + 1}):`, errorMessage);\n            // Actualizar estado a 'failed' si tenemos el ID\n            if (notificationId) {\n                await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.updateEmailNotificationStatus(notificationId, 'failed', errorMessage);\n            }\n            // Intentar reenvío si no hemos alcanzado el máximo de reintentos\n            if (retryCount < maxRetries) {\n                console.log(`🔄 Reintentando envío en ${retryDelay}ms...`);\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay));\n                return this.sendEmail(notification, retryCount + 1);\n            }\n            // Si agotamos los reintentos, registrar fallo final\n            console.error(`💥 Fallo definitivo después de ${maxRetries + 1} intentos`);\n            return false;\n        }\n    }\n    /**\n   * Reenviar notificaciones fallidas\n   */ static async retryFailedNotifications(maxAge = 24, // Máximo 24 horas de antigüedad\n    limit = 10 // Máximo 10 reintentos por ejecución\n    ) {\n        try {\n            console.log(`🔄 Buscando notificaciones fallidas para reintentar (máximo ${maxAge} horas)...`);\n            // Obtener notificaciones fallidas\n            const failedNotifications = await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.getFailedNotifications(maxAge, limit);\n            if (failedNotifications.length === 0) {\n                console.log('✅ No se encontraron notificaciones fallidas para reintentar');\n                return {\n                    attempted: 0,\n                    successful: 0,\n                    failed: 0,\n                    errors: []\n                };\n            }\n            console.log(`📋 Encontradas ${failedNotifications.length} notificaciones para reintentar`);\n            let successful = 0;\n            let failed = 0;\n            const errors = [];\n            // Reintentar cada notificación\n            for (const notification of failedNotifications){\n                try {\n                    const emailNotification = {\n                        to: notification.recipient_email,\n                        subject: notification.subject,\n                        htmlContent: '',\n                        // Necesitaríamos regenerar el contenido\n                        textContent: '',\n                        type: notification.type,\n                        userId: notification.user_id,\n                        metadata: notification.metadata\n                    };\n                    // Marcar como reintento en metadata\n                    emailNotification.metadata = _objectSpread(_objectSpread({}, emailNotification.metadata), {}, {\n                        retry_attempt: true,\n                        original_notification_id: notification.id,\n                        retry_at: new Date().toISOString()\n                    });\n                    const success = await this.sendEmail(emailNotification);\n                    if (success) {\n                        successful++;\n                        // Marcar la notificación original como reintentada exitosamente\n                        await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, true);\n                    } else {\n                        failed++;\n                        await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, false, 'Retry failed');\n                        errors.push(`Failed to retry notification ${notification.id}`);\n                    }\n                } catch (retryError) {\n                    failed++;\n                    const errorMsg = `Error retrying notification ${notification.id}: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                    await _emailLogger__WEBPACK_IMPORTED_MODULE_0__.EmailLogger.markAsRetried(notification.id, false, errorMsg);\n                }\n            }\n            console.log(`🎯 Reintentos completados: ${successful} exitosos, ${failed} fallidos`);\n            return {\n                attempted: failedNotifications.length,\n                successful,\n                failed,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en retryFailedNotifications:', error);\n            throw error;\n        }\n    }\n    /**\n   * Enviar email de prueba para verificar configuración\n   */ static async sendTestEmail(to, providerConfig) {\n        try {\n            const testNotification = {\n                to,\n                subject: 'Test Email - OposI',\n                htmlContent: `\n          <h1>Email de Prueba</h1>\n          <p>Este es un email de prueba para verificar la configuración del sistema de notificaciones.</p>\n          <p>Enviado el: ${new Date().toLocaleString('es-ES')}</p>\n        `,\n                textContent: `\n          Email de Prueba\n          \n          Este es un email de prueba para verificar la configuración del sistema de notificaciones.\n          Enviado el: ${new Date().toLocaleString('es-ES')}\n        `,\n                type: 'other',\n                metadata: {\n                    test_email: true,\n                    sent_at: new Date().toISOString()\n                }\n            };\n            const success = await this.sendEmail(testNotification);\n            return {\n                success,\n                message: success ? 'Email de prueba enviado exitosamente' : 'Fallo al enviar email de prueba',\n                details: {\n                    to,\n                    timestamp: new Date().toISOString()\n                }\n            };\n        } catch (error) {\n            console.error('Error enviando email de prueba:', error);\n            return {\n                success: false,\n                message: 'Error enviando email de prueba',\n                details: {\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                }\n            };\n        }\n    }\n    /**\n   * Validar configuración del proveedor de email\n   */ static async validateEmailProvider() {\n        try {\n            // TODO: Implementar validación específica del proveedor\n            // Ejemplo para Resend:\n            /*\n      if (!process.env.RESEND_API_KEY) {\n        return {\n          isValid: false,\n          provider: 'Resend',\n          message: 'RESEND_API_KEY no configurada'\n        };\n      }\n      \n      // Probar conexión con API\n      const resend = new Resend(process.env.RESEND_API_KEY);\n      await resend.domains.list();\n      */ // Por ahora, simulación\n            return {\n                isValid: true,\n                provider: 'Simulado',\n                message: 'Proveedor de email configurado correctamente'\n            };\n        } catch (error) {\n            return {\n                isValid: false,\n                provider: 'Unknown',\n                message: error instanceof Error ? error.message : 'Error validando proveedor'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailSender.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/email/emailTemplates.ts":
/*!**************************************************!*\
  !*** ./src/lib/services/email/emailTemplates.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EmailTemplates: () => (/* binding */ EmailTemplates)\n/* harmony export */ });\n// src/lib/services/email/emailTemplates.ts\n// Templates para diferentes tipos de notificaciones por email\nclass EmailTemplates {\n    /**\n   * Template para notificación de cancelación de suscripción\n   */ static generateSubscriptionCancelledEmail(userName, planName, gracePeriodEnd) {\n        const gracePeriodDate = new Date(gracePeriodEnd);\n        const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n        const daysRemaining = Math.ceil((gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Suscripción Cancelada - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #2563eb;\">Suscripción Cancelada</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>Hemos recibido tu solicitud de cancelación de la suscripción al <strong>Plan ${planName}</strong>.</p>\n          \n          <div style=\"background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;\">\n            <h3 style=\"margin-top: 0; color: #059669;\">📅 Período de Gracia Activo</h3>\n            <p><strong>Mantienes acceso completo hasta:</strong> ${formattedDate}</p>\n            <p><strong>Días restantes:</strong> ${daysRemaining} días</p>\n            <p>Durante este período, puedes seguir usando todas las funciones de tu plan actual.</p>\n          </div>\n          \n          <h3>¿Qué sucede después?</h3>\n          <ul>\n            <li>Tu acceso a las funciones premium finalizará el ${formattedDate}</li>\n            <li>Tu cuenta se convertirá automáticamente al Plan Gratuito</li>\n            <li>Conservarás acceso a las funciones básicas de OposI</li>\n            <li>Tus documentos y progreso se mantendrán guardados</li>\n          </ul>\n          \n          <h3>¿Cambiaste de opinión?</h3>\n          <p>Si deseas reactivar tu suscripción, puedes hacerlo en cualquier momento desde tu panel de control:</p>\n          <p style=\"text-align: center; margin: 20px 0;\">\n            <a href=\"${\"http://localhost:3000\"}/upgrade-plan\" \n               style=\"background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n              Reactivar Suscripción\n            </a>\n          </p>\n          \n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta o necesitas ayuda, no dudes en contactarnos.<br>\n            Equipo de OposI\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\nSuscripción Cancelada - OposI\n\nHola ${userName},\n\nHemos recibido tu solicitud de cancelación de la suscripción al Plan ${planName}.\n\nPERÍODO DE GRACIA ACTIVO:\n- Mantienes acceso completo hasta: ${formattedDate}\n- Días restantes: ${daysRemaining} días\n\n¿Qué sucede después?\n- Tu acceso a las funciones premium finalizará el ${formattedDate}\n- Tu cuenta se convertirá automáticamente al Plan Gratuito\n- Conservarás acceso a las funciones básicas de OposI\n- Tus documentos y progreso se mantendrán guardados\n\n¿Cambiaste de opinión?\nPuedes reactivar tu suscripción en cualquier momento desde: ${\"http://localhost:3000\"}/upgrade-plan\n\nSi tienes alguna pregunta, no dudes en contactarnos.\nEquipo de OposI\n    `;\n        const subject = `Suscripción cancelada - Acceso hasta el ${gracePeriodDate.toLocaleDateString('es-ES')}`;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template para recordatorio de que el período de gracia está por terminar\n   */ static generateGracePeriodEndingEmail(userName, planName, gracePeriodEnd) {\n        const gracePeriodDate = new Date(gracePeriodEnd);\n        const formattedDate = gracePeriodDate.toLocaleDateString('es-ES', {\n            year: 'numeric',\n            month: 'long',\n            day: 'numeric'\n        });\n        const hoursRemaining = Math.ceil((gracePeriodDate.getTime() - new Date().getTime()) / (1000 * 60 * 60));\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>Tu acceso premium termina pronto - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #dc2626;\">⏰ Tu acceso premium termina pronto</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>Te recordamos que tu acceso al <strong>Plan ${planName}</strong> terminará el <strong>${formattedDate}</strong> (en aproximadamente ${hoursRemaining} horas).</p>\n          \n          <div style=\"background-color: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b;\">\n            <h3 style=\"margin-top: 0; color: #92400e;\">¿Quieres continuar con tu plan premium?</h3>\n            <p>Reactivar tu suscripción es fácil y rápido. Mantén acceso a todas las funciones avanzadas de OposI.</p>\n          </div>\n          \n          <p style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${\"http://localhost:3000\"}/upgrade-plan\" \n               style=\"background-color: #dc2626; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;\">\n              Reactivar Mi Suscripción\n            </a>\n          </p>\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\nTu acceso premium termina pronto - OposI\n\nHola ${userName},\n\nTe recordamos que tu acceso al Plan ${planName} terminará el ${formattedDate} (en aproximadamente ${hoursRemaining} horas).\n\n¿Quieres continuar con tu plan premium?\nReactivar tu suscripción: ${\"http://localhost:3000\"}/upgrade-plan\n\nSi no reactivas tu suscripción, tu cuenta se convertirá automáticamente al Plan Gratuito el ${formattedDate}.\n\nEquipo de OposI\n    `;\n        const subject = `⏰ Tu Plan ${planName} termina en ${hoursRemaining} horas`;\n        return {\n            htmlContent,\n            textContent,\n            subject\n        };\n    }\n    /**\n   * Template base para otros tipos de notificaciones\n   */ static generateGenericEmail(userName, title, message, ctaText, ctaUrl) {\n        const htmlContent = `\n      <!DOCTYPE html>\n      <html>\n      <head>\n        <meta charset=\"utf-8\">\n        <title>${title} - OposI</title>\n      </head>\n      <body style=\"font-family: Arial, sans-serif; line-height: 1.6; color: #333;\">\n        <div style=\"max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h1 style=\"color: #2563eb;\">${title}</h1>\n          \n          <p>Hola ${userName},</p>\n          \n          <p>${message}</p>\n          \n          ${ctaText && ctaUrl ? `\n          <p style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${ctaUrl}\" \n               style=\"background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n              ${ctaText}\n            </a>\n          </p>\n          ` : ''}\n          \n          <hr style=\"margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;\">\n          \n          <p style=\"font-size: 14px; color: #6b7280;\">\n            Si tienes alguna pregunta, no dudes en contactarnos.<br>\n            Equipo de OposI\n          </p>\n        </div>\n      </body>\n      </html>\n    `;\n        const textContent = `\n${title} - OposI\n\nHola ${userName},\n\n${message}\n\n${ctaText && ctaUrl ? `${ctaText}: ${ctaUrl}` : ''}\n\nSi tienes alguna pregunta, no dudes en contactarnos.\nEquipo de OposI\n    `;\n        return {\n            htmlContent,\n            textContent,\n            subject: title\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/email/emailTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/stripeWebhookHandlers.ts":
/*!***************************************************!*\
  !*** ./src/lib/services/stripeWebhookHandlers.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StripeWebhookHandlers: () => (/* binding */ StripeWebhookHandlers)\n/* harmony export */ });\n/* harmony import */ var _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/stripe/config */ \"(rsc)/./src/lib/stripe/config.ts\");\n/* harmony import */ var _userManagement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./userManagement */ \"(rsc)/./src/lib/services/userManagement.ts\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _email_emailNotificationService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./email/emailNotificationService */ \"(rsc)/./src/lib/services/email/emailNotificationService.ts\");\n// src/lib/services/stripeWebhookHandlers.ts\n// Manejadores específicos para eventos de webhooks de Stripe\n\n\n\n\nclass StripeWebhookHandlers {\n    /**\n   * Manejar evento checkout.session.completed\n   * Se ejecuta cuando un pago se completa exitosamente\n   */ static async handleCheckoutSessionCompleted(session) {\n        try {\n            console.log('🎯 Procesando checkout.session.completed:', session.id);\n            // Validar que el pago esté completado\n            if (session.payment_status !== 'paid') {\n                return {\n                    success: false,\n                    message: 'Payment not completed',\n                    error: `Payment status: ${session.payment_status}`\n                };\n            }\n            // Verificar si es una compra de tokens\n            if (session.metadata?.type === 'token_purchase') {\n                return await this.handleTokenPurchase(session);\n            }\n            // Extraer metadata para creación de usuario\n            const { planId, customerEmail, customerName } = session.metadata || {};\n            if (!planId || !customerEmail) {\n                return {\n                    success: false,\n                    message: 'Missing required metadata',\n                    error: 'planId and customerEmail are required'\n                };\n            }\n            // Obtener el ID de la suscripción si el modo es 'subscription'\n            const subscriptionId = session.mode === 'subscription' ? session.subscription : undefined;\n            // Verificar si ya fue procesado\n            const existingTransaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getTransactionBySessionId(session.id);\n            if (existingTransaction) {\n                console.log('⚠️ Transacción ya procesada:', existingTransaction.id);\n                return {\n                    success: true,\n                    message: 'Transaction already processed',\n                    data: {\n                        transactionId: existingTransaction.id\n                    }\n                };\n            }\n            // Verificar si ya existe un usuario con este customer ID (para evitar duplicados)\n            const { data: existingProfile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan').eq('stripe_customer_id', session.customer).single();\n            if (existingProfile) {\n                console.log('⚠️ Usuario ya existe para este customer ID:', session.customer);\n                // Si es una suscripción nueva para un usuario existente, actualizar el plan\n                if (subscriptionId) {\n                    const result = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.updateUserPlan(existingProfile.user_id, planId, undefined, 'New subscription for existing customer');\n                    if (result.success) {\n                        return {\n                            success: true,\n                            message: 'Existing user plan updated with new subscription',\n                            data: {\n                                userId: existingProfile.user_id\n                            }\n                        };\n                    }\n                }\n                return {\n                    success: false,\n                    message: 'Customer already exists but plan update failed'\n                };\n            }\n            // Para suscripciones, obtener current_period_end directamente de Stripe\n            let planExpiresAt = null;\n            if (subscriptionId && _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe) {\n                try {\n                    const subscription = await _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe.subscriptions.retrieve(subscriptionId);\n                    planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null;\n                    console.log(`[handleCheckoutSessionCompleted] Plan expires at from subscription: ${planExpiresAt}`);\n                } catch (error) {\n                    console.error('[handleCheckoutSessionCompleted] Error obteniendo suscripción:', error);\n                }\n            }\n            // Verificar si hay datos de registro en los metadatos (flujo legacy)\n            const registrationData = session.metadata?.registrationData;\n            const preRegisteredUserId = session.metadata?.userId;\n            let result;\n            if (preRegisteredUserId) {\n                // NUEVO FLUJO: Usuario ya pre-registrado, activar y enviar email de confirmación\n                console.log(`🆕 Activando usuario pre-registrado después del pago: ${preRegisteredUserId}`);\n                try {\n                    // 1. Crear transacción de Stripe\n                    const transaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.createStripeTransaction({\n                        stripe_session_id: session.id,\n                        stripe_customer_id: session.customer,\n                        user_email: customerEmail,\n                        user_name: customerName,\n                        plan_id: planId,\n                        amount: session.amount_total || 0,\n                        currency: session.currency || 'eur',\n                        payment_status: 'paid',\n                        subscription_id: subscriptionId,\n                        user_id: preRegisteredUserId,\n                        metadata: {\n                            created_by: 'webhook',\n                            activation_flow: 'pre_registered_user'\n                        }\n                    });\n                    // 2. Actualizar perfil de usuario con datos de pago\n                    const { error: updateError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: true,\n                        stripe_customer_id: session.customer,\n                        stripe_subscription_id: subscriptionId,\n                        last_payment_date: new Date().toISOString(),\n                        plan_expires_at: planExpiresAt,\n                        auto_renew: subscriptionId ? true : false,\n                        updated_at: new Date().toISOString(),\n                        security_flags: {\n                            payment_completed: true,\n                            payment_date: new Date().toISOString(),\n                            stripe_session_id: session.id,\n                            subscription_id: subscriptionId,\n                            pre_registered: false,\n                            // Ya no está en estado pendiente\n                            activated: true\n                        }\n                    }).eq('user_id', preRegisteredUserId);\n                    if (updateError) {\n                        console.error('Error actualizando usuario pre-registrado:', updateError);\n                        result = {\n                            success: false,\n                            error: updateError.message\n                        };\n                    } else {\n                        // 3. Enviar email de confirmación AHORA que el pago está completado\n                        console.log('📧 Enviando email de confirmación después del pago exitoso...');\n                        const emailResult = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.sendConfirmationEmailForUser(preRegisteredUserId);\n                        if (!emailResult.success) {\n                            console.error('⚠️ Error enviando email de confirmación:', emailResult.error);\n                        // No fallar completamente, el usuario puede confirmar manualmente\n                        } else {\n                            console.log('✅ Email de confirmación enviado exitosamente después del pago');\n                        }\n                        // 5. Activar transacción\n                        await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.activateTransaction(transaction.id);\n                        result = {\n                            success: true,\n                            userId: preRegisteredUserId,\n                            transactionId: transaction.id,\n                            activated: true\n                        };\n                    }\n                } catch (activationError) {\n                    console.error('Error activando usuario pre-registrado:', activationError);\n                    result = {\n                        success: false,\n                        error: activationError instanceof Error ? activationError.message : 'Activation error'\n                    };\n                }\n            } else if (registrationData) {\n                // FLUJO LEGACY: Crear cuenta después del pago exitoso (mantener compatibilidad)\n                console.log(`🔄 Flujo legacy: Creando cuenta después del pago exitoso para: ${customerEmail}`);\n                try {\n                    const regData = JSON.parse(registrationData);\n                    // Crear usuario con plan usando los datos de registro\n                    result = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.createUserWithPlan({\n                        email: regData.email,\n                        password: regData.password,\n                        // Contraseña del formulario\n                        name: regData.customerName,\n                        planId: regData.planId,\n                        stripeSessionId: session.id,\n                        stripeCustomerId: session.customer,\n                        amount: session.amount_total || 0,\n                        currency: session.currency || 'eur',\n                        subscriptionId: subscriptionId,\n                        planExpiresAt: planExpiresAt,\n                        sendConfirmationEmail: true // ENVIAR EMAIL DESPUÉS DEL PAGO\n                    });\n                } catch (parseError) {\n                    console.error('Error parseando datos de registro:', parseError);\n                    result = {\n                        success: false,\n                        error: 'Invalid registration data format'\n                    };\n                }\n            } else {\n                // Flujo original: crear usuario con plan\n                result = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.createUserWithPlan({\n                    email: customerEmail,\n                    name: customerName,\n                    planId: planId,\n                    stripeSessionId: session.id,\n                    stripeCustomerId: session.customer,\n                    amount: session.amount_total || 0,\n                    currency: session.currency || 'eur',\n                    subscriptionId: subscriptionId,\n                    planExpiresAt: planExpiresAt // Pasar la fecha de expiración\n                });\n            }\n            if (!result.success) {\n                // Si el error es porque el email ya existe, intentar un flujo de actualización\n                const isEmailExistsError = result.error && (result.error.includes('A user with this email address has already been registered') || result.error.includes('email_exists') || result.error.includes('already been registered') || result.error.includes('User already registered'));\n                if (isEmailExistsError) {\n                    console.log(`🔁 Usuario existente detectado, actualizando plan para: ${customerEmail}`);\n                    // 1. Obtener el User ID de Supabase Auth por email\n                    const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getUserByEmail(customerEmail);\n                    if (!existingUser) {\n                        return {\n                            success: false,\n                            message: 'Failed to retrieve existing user by email.',\n                            error: 'User not found despite email_exists error'\n                        };\n                    }\n                    const existingUserId = existingUser.id;\n                    // 2. Actualizar el plan del usuario existente\n                    const updateResult = await _userManagement__WEBPACK_IMPORTED_MODULE_1__.UserManagementService.updateUserPlan(existingUserId, planId, undefined, // transactionId se manejará después\n                    'New payment for existing user email');\n                    // 3. También actualizar el stripe_customer_id y stripe_subscription_id si es necesario\n                    const { error: profileUpdateError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                        stripe_customer_id: session.customer,\n                        stripe_subscription_id: subscriptionId,\n                        payment_verified: true // Marcar como verificado tras el pago\n                    }).eq('user_id', existingUserId);\n                    if (profileUpdateError) {\n                        console.error('Error actualizando perfil con datos de Stripe:', profileUpdateError);\n                    }\n                    // 4. Actualizar la transacción\n                    const transaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getTransactionBySessionId(session.id);\n                    if (transaction) {\n                        await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.updateTransactionWithUser(transaction.id, existingUserId);\n                        await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.activateTransaction(transaction.id);\n                    }\n                    if (updateResult.success) {\n                        return {\n                            success: true,\n                            message: 'Existing user plan updated successfully',\n                            data: {\n                                userId: existingUserId\n                            }\n                        };\n                    } else {\n                        return {\n                            success: false,\n                            message: 'Failed to update plan for existing user',\n                            error: updateResult.error\n                        };\n                    }\n                }\n                // Si es otro tipo de error\n                return {\n                    success: false,\n                    message: 'Failed to create user',\n                    error: result.error\n                };\n            }\n            console.log('✅ Usuario creado exitosamente desde webhook');\n            return {\n                success: true,\n                message: 'User created successfully',\n                data: {\n                    userId: result.userId,\n                    profileId: result.profileId,\n                    transactionId: result.transactionId\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleCheckoutSessionCompleted:', error);\n            return {\n                success: false,\n                message: 'Internal error processing checkout session',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar compra de tokens adicionales\n   */ static async handleTokenPurchase(session) {\n        try {\n            console.log('🪙 Procesando compra de tokens:', session.id);\n            const { user_id, token_amount, price } = session.metadata || {};\n            if (!user_id || !token_amount || !price) {\n                return {\n                    success: false,\n                    message: 'Missing required metadata for token purchase',\n                    error: 'user_id, token_amount, and price are required'\n                };\n            }\n            // Verificar si ya fue procesado\n            const { data: existingPurchase } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('token_purchases').select('id').eq('transaction_id', session.id).single();\n            if (existingPurchase) {\n                console.log('⚠️ Compra de tokens ya procesada:', existingPurchase.id);\n                return {\n                    success: true,\n                    message: 'Token purchase already processed',\n                    data: {\n                        purchaseId: existingPurchase.id\n                    }\n                };\n            }\n            const tokenAmount = parseInt(token_amount);\n            const purchasePrice = parseFloat(price);\n            // Registrar la compra de tokens\n            const { data: purchase, error: purchaseError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('token_purchases').insert([\n                {\n                    user_id: user_id,\n                    amount: tokenAmount,\n                    price: purchasePrice,\n                    transaction_id: session.id,\n                    status: 'completed'\n                }\n            ]).select().single();\n            if (purchaseError) {\n                console.error('Error registrando compra de tokens:', purchaseError);\n                return {\n                    success: false,\n                    message: 'Error registering token purchase',\n                    error: purchaseError.message\n                };\n            }\n            // Actualizar límite de tokens del usuario\n            const { data: profile, error: profileError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('monthly_token_limit').eq('user_id', user_id).single();\n            if (profileError || !profile) {\n                console.error('Error obteniendo perfil de usuario:', profileError);\n                return {\n                    success: false,\n                    message: 'User profile not found',\n                    error: profileError?.message || 'Profile not found'\n                };\n            }\n            const newTokenLimit = profile.monthly_token_limit + tokenAmount;\n            const { error: updateError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                monthly_token_limit: newTokenLimit,\n                updated_at: new Date().toISOString()\n            }).eq('user_id', user_id);\n            if (updateError) {\n                console.error('Error actualizando límite de tokens:', updateError);\n                return {\n                    success: false,\n                    message: 'Error updating token limit',\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Compra de tokens procesada exitosamente:', {\n                userId: user_id,\n                tokensAdded: tokenAmount,\n                newLimit: newTokenLimit,\n                purchaseId: purchase.id\n            });\n            return {\n                success: true,\n                message: 'Token purchase processed successfully',\n                data: {\n                    purchaseId: purchase.id,\n                    tokensAdded: tokenAmount,\n                    newTokenLimit: newTokenLimit\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleTokenPurchase:', error);\n            return {\n                success: false,\n                message: 'Internal error processing token purchase',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento payment_intent.succeeded\n   * Para pagos únicos exitosos\n   */ static async handlePaymentIntentSucceeded(paymentIntent) {\n        try {\n            console.log('💳 Procesando payment_intent.succeeded:', paymentIntent.id);\n            // Para pagos únicos, la lógica principal está en checkout.session.completed\n            // Aquí solo registramos el evento para auditoría\n            return {\n                success: true,\n                message: 'Payment intent logged successfully',\n                data: {\n                    paymentIntentId: paymentIntent.id\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handlePaymentIntentSucceeded:', error);\n            return {\n                success: false,\n                message: 'Error processing payment intent',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento customer.subscription.created\n   * Cuando se crea una nueva suscripción\n   */ static async handleSubscriptionCreated(subscription) {\n        try {\n            console.log('🔄 Procesando customer.subscription.created:', subscription.id);\n            // Obtener información del cliente\n            const customerId = subscription.customer;\n            const planId = subscription.metadata?.planId;\n            if (!planId) {\n                return {\n                    success: false,\n                    message: 'Missing plan ID in subscription metadata'\n                };\n            }\n            // Actualizar información de suscripción en la transacción\n            const { error: transactionError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('stripe_transactions').update({\n                subscription_id: subscription.id,\n                metadata: {\n                    subscription_status: subscription.status,\n                    subscription_id: subscription.id,\n                    updated_at: new Date().toISOString()\n                }\n            }).eq('stripe_customer_id', customerId);\n            if (transactionError) {\n                console.error('Error actualizando transacción con suscripción:', transactionError);\n            }\n            // Establecer plan_expires_at inicial usando current_period_end de la suscripción\n            const planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Fallback a 30 días\n            console.log(`[handleSubscriptionCreated] Actualizando perfil para customerId: ${customerId}`);\n            console.log(`[handleSubscriptionCreated] stripe_subscription_id: ${subscription.id}, plan_expires_at: ${planExpiresAt}`);\n            // Intentar actualizar por stripe_customer_id primero\n            let { error: profileError, data: updatedProfileData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                stripe_subscription_id: subscription.id,\n                plan_expires_at: planExpiresAt,\n                auto_renew: true,\n                last_payment_date: new Date().toISOString(),\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    subscription_status: subscription.status,\n                    subscription_id: subscription.id,\n                    subscription_created_at: new Date().toISOString()\n                }\n            }).eq('stripe_customer_id', customerId).select();\n            // Si no se encontró por stripe_customer_id, buscar por email en los metadatos\n            if (!updatedProfileData || updatedProfileData.length === 0) {\n                console.log(`[handleSubscriptionCreated] No se encontró perfil por customerId, buscando por email...`);\n                const customerEmail = subscription.metadata?.customerEmail;\n                if (customerEmail) {\n                    // Obtener user_id por email\n                    const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.getUserByEmail(customerEmail);\n                    if (existingUser) {\n                        console.log(`[handleSubscriptionCreated] Actualizando perfil por user_id: ${existingUser.id}`);\n                        const updateResult = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                            stripe_customer_id: customerId,\n                            // Establecer el customer_id que faltaba\n                            stripe_subscription_id: subscription.id,\n                            plan_expires_at: planExpiresAt,\n                            auto_renew: true,\n                            last_payment_date: new Date().toISOString(),\n                            updated_at: new Date().toISOString(),\n                            security_flags: {\n                                subscription_status: subscription.status,\n                                subscription_id: subscription.id,\n                                subscription_created_at: new Date().toISOString()\n                            }\n                        }).eq('user_id', existingUser.id).select();\n                        profileError = updateResult.error;\n                        updatedProfileData = updateResult.data;\n                    }\n                }\n            }\n            if (profileError) {\n                console.error('[handleSubscriptionCreated] Error actualizando perfil con suscripción:', profileError);\n            } else {\n                console.log('[handleSubscriptionCreated] Perfil actualizado con datos de suscripción:', updatedProfileData);\n            }\n            return {\n                success: true,\n                message: 'Subscription created successfully',\n                data: {\n                    subscriptionId: subscription.id,\n                    planExpiresAt\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleSubscriptionCreated:', error);\n            return {\n                success: false,\n                message: 'Error processing subscription creation',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento customer.subscription.updated\n   * Cuando se actualiza una suscripción\n   */ static async handleSubscriptionUpdated(subscription) {\n        try {\n            console.log('🔄 Procesando customer.subscription.updated:', subscription.id);\n            // Obtener usuario por customer ID\n            const { data: profile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan').eq('stripe_customer_id', subscription.customer).single();\n            if (!profile) {\n                return {\n                    success: false,\n                    message: 'User profile not found for customer'\n                };\n            }\n            // Actualizar estado de la suscripción usando current_period_end de Stripe\n            const planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(); // Fallback a 30 días\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                plan_expires_at: planExpiresAt,\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    subscription_status: subscription.status,\n                    subscription_id: subscription.id,\n                    last_updated: new Date().toISOString()\n                }\n            }).eq('user_id', profile.user_id);\n            if (error) {\n                console.error('Error actualizando perfil de usuario:', error);\n                return {\n                    success: false,\n                    message: 'Error updating user profile'\n                };\n            }\n            return {\n                success: true,\n                message: 'Subscription updated successfully',\n                data: {\n                    subscriptionId: subscription.id\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleSubscriptionUpdated:', error);\n            return {\n                success: false,\n                message: 'Error processing subscription update',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento customer.subscription.deleted\n   * Cuando se cancela una suscripción - implementa período de gracia\n   */ static async handleSubscriptionDeleted(subscription) {\n        try {\n            console.log('❌ Procesando customer.subscription.deleted:', subscription.id);\n            // Obtener usuario por customer ID\n            const { data: profile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan, plan_expires_at').eq('stripe_customer_id', subscription.customer).single();\n            if (!profile) {\n                return {\n                    success: false,\n                    message: 'User profile not found for customer'\n                };\n            }\n            // Obtener current_period_end de la suscripción para período de gracia\n            const gracePeriodEnd = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : new Date().toISOString(); // Si no hay current_period_end, expirar inmediatamente\n            console.log(`🕐 Período de gracia hasta: ${gracePeriodEnd} para usuario: ${profile.user_id}`);\n            // En lugar de degradar inmediatamente, mantener plan actual hasta current_period_end\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update({\n                plan_expires_at: gracePeriodEnd,\n                auto_renew: false,\n                // Cancelar auto-renovación\n                stripe_subscription_id: null,\n                // Limpiar ID de suscripción\n                updated_at: new Date().toISOString(),\n                security_flags: {\n                    subscription_cancelled: true,\n                    cancellation_date: new Date().toISOString(),\n                    grace_period_until: gracePeriodEnd,\n                    cancelled_subscription_id: subscription.id,\n                    last_updated: new Date().toISOString()\n                }\n            }).eq('user_id', profile.user_id);\n            if (error) {\n                console.error('Error actualizando perfil para período de gracia:', error);\n                return {\n                    success: false,\n                    message: 'Error setting up grace period',\n                    error: error.message\n                };\n            }\n            // Registrar el cambio en el historial\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.SupabaseAdminService.logPlanChange({\n                user_id: profile.user_id,\n                old_plan: profile.subscription_plan,\n                new_plan: profile.subscription_plan,\n                // Mantener el mismo plan durante gracia\n                changed_by: 'system',\n                reason: `Subscription cancelled - Grace period until ${gracePeriodEnd}`\n            });\n            // Obtener información del usuario para enviar notificación por email\n            try {\n                const { data: userData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.auth.admin.getUserById(profile.user_id);\n                if (userData.user?.email) {\n                    const userName = userData.user.user_metadata?.name || userData.user.email.split('@')[0];\n                    const planName = profile.subscription_plan === 'usuario' ? 'Usuario' : 'Pro';\n                    // Enviar notificación de cancelación con período de gracia\n                    await _email_emailNotificationService__WEBPACK_IMPORTED_MODULE_3__.EmailNotificationService.sendSubscriptionCancelledNotification(userData.user.email, userName, planName, gracePeriodEnd, profile.user_id);\n                    console.log(`📧 Notificación de cancelación enviada a: ${userData.user.email}`);\n                }\n            } catch (emailError) {\n                console.error('Error enviando notificación de cancelación:', emailError);\n            // No fallar el webhook por error de email\n            }\n            return {\n                success: true,\n                message: `Subscription cancelled with grace period until ${gracePeriodEnd}`,\n                data: {\n                    userId: profile.user_id,\n                    gracePeriodEnd,\n                    currentPlan: profile.subscription_plan\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleSubscriptionDeleted:', error);\n            return {\n                success: false,\n                message: 'Error processing subscription deletion',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n    /**\n   * Manejar evento invoice.payment_succeeded\n   * Para renovaciones de suscripción\n   */ static async handleInvoicePaymentSucceeded(invoice) {\n        try {\n            console.log('💰 Procesando invoice.payment_succeeded:', invoice.id);\n            // Verificar si la factura está relacionada con una suscripción\n            const subscriptionId = invoice.subscription;\n            if (!subscriptionId) {\n                return {\n                    success: true,\n                    message: 'Invoice not related to subscription, skipping'\n                };\n            }\n            // Obtener información de la suscripción para actualizar plan_expires_at\n            let planExpiresAt = null;\n            try {\n                if (subscriptionId && _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe) {\n                    const subscription = await _lib_stripe_config__WEBPACK_IMPORTED_MODULE_0__.stripe.subscriptions.retrieve(subscriptionId);\n                    planExpiresAt = subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null;\n                }\n            } catch (error) {\n                console.error('Error obteniendo información de suscripción:', error);\n            }\n            // Obtener perfil del usuario para determinar si es renovación\n            const { data: profile } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan, current_month_tokens, monthly_token_limit').eq('stripe_customer_id', invoice.customer).single();\n            // Actualizar fecha de último pago y plan_expires_at\n            const updateData = {\n                last_payment_date: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            };\n            if (planExpiresAt) {\n                updateData.plan_expires_at = planExpiresAt;\n                // Para suscripciones, resetear tokens al inicio del nuevo período de facturación\n                if (profile && (profile.subscription_plan === 'usuario' || profile.subscription_plan === 'pro')) {\n                    updateData.current_month_tokens = 0;\n                    updateData.current_month = new Date().toISOString().slice(0, 7) + '-01';\n                    console.log('🔄 Reseteando tokens para renovación de suscripción:', profile.user_id);\n                }\n            }\n            const { error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_2__.supabaseAdmin.from('user_profiles').update(updateData).eq('stripe_customer_id', invoice.customer);\n            if (error) {\n                console.error('Error actualizando fecha de pago:', error);\n            }\n            return {\n                success: true,\n                message: 'Invoice payment processed successfully',\n                data: {\n                    invoiceId: invoice.id\n                }\n            };\n        } catch (error) {\n            console.error('❌ Error en handleInvoicePaymentSucceeded:', error);\n            return {\n                success: false,\n                message: 'Error processing invoice payment',\n                error: error instanceof Error ? error.message : 'Unknown error'\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/stripeWebhookHandlers.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/userManagement.ts":
/*!********************************************!*\
  !*** ./src/lib/services/userManagement.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserManagementService: () => (/* binding */ UserManagementService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/services/userManagement.ts\n// Servicio para gestión automatizada de usuarios y perfiles\n\n\nclass UserManagementService {\n    /**\n   * Crear usuario completo con perfil y transacción\n   */ static async createUserWithPlan(request) {\n        try {\n            console.log('🚀 Iniciando creación de usuario:', request.email);\n            // 1. Validar plan\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(request.planId);\n            if (!planConfig) {\n                throw new Error(`Plan inválido: ${request.planId}`);\n            }\n            // 2. Verificar si ya existe una transacción para esta sesión\n            const existingTransaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getTransactionBySessionId(request.stripeSessionId);\n            if (existingTransaction) {\n                console.log('⚠️ Transacción ya existe:', existingTransaction.id);\n                return {\n                    success: false,\n                    error: 'Transacción ya procesada'\n                };\n            }\n            // 3. Crear registro de transacción\n            const transaction = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createStripeTransaction({\n                stripe_session_id: request.stripeSessionId,\n                stripe_customer_id: request.stripeCustomerId,\n                user_email: request.email,\n                user_name: request.name,\n                plan_id: request.planId,\n                amount: request.amount,\n                currency: request.currency,\n                payment_status: 'paid',\n                subscription_id: request.subscriptionId,\n                metadata: {\n                    created_by: 'webhook',\n                    plan_name: planConfig.name\n                }\n            });\n            console.log('✅ Transacción creada:', transaction.id);\n            // 4. Crear usuario con contraseña específica o invitación\n            let createdUserId;\n            let userInvitation = null;\n            if (request.password && request.sendConfirmationEmail) {\n                // NUEVO FLUJO: Crear cuenta con contraseña específica y enviar email de confirmación\n                console.log('🆕 Creando cuenta con contraseña específica y email de confirmación');\n                try {\n                    userInvitation = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createUserWithPassword(request.email, request.password, {\n                        name: request.name,\n                        plan: request.planId,\n                        stripe_session_id: request.stripeSessionId,\n                        stripe_customer_id: request.stripeCustomerId,\n                        transaction_id: transaction.id,\n                        payment_verified: true\n                    });\n                    createdUserId = userInvitation.user.id;\n                    console.log('✅ Usuario creado con contraseña y email de confirmación:', createdUserId);\n                } catch (passwordError) {\n                    console.error('Error creando usuario con contraseña:', passwordError);\n                    throw passwordError;\n                }\n            } else {\n                // FLUJO ANTERIOR: Crear invitación de usuario\n                const userData = {\n                    name: request.name,\n                    plan: request.planId,\n                    stripe_session_id: request.stripeSessionId,\n                    stripe_customer_id: request.stripeCustomerId,\n                    transaction_id: transaction.id,\n                    payment_verified: true\n                };\n                try {\n                    userInvitation = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.createUserWithInvitation(request.email, userData);\n                    createdUserId = userInvitation.user.id;\n                    console.log('✅ Invitación de usuario creada:', createdUserId);\n                } catch (invitationError) {\n                    // Detectar diferentes variaciones del error de email existente\n                    const isEmailExistsError = invitationError.message && (invitationError.message.includes('A user with this email address has already been registered') || invitationError.message.includes('email_exists') || invitationError.message.includes('already been registered') || invitationError.message.includes('User already registered'));\n                    if (isEmailExistsError) {\n                        console.log('⚠️ Usuario ya existe, actualizando plan existente');\n                        // Obtener el usuario existente de Supabase Auth\n                        const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                        if (!existingUser) {\n                            throw new Error('Error obteniendo usuario existente.');\n                        }\n                        createdUserId = existingUser.id;\n                    } else {\n                        // Otro error durante la invitación\n                        throw invitationError;\n                    }\n                }\n            }\n            // 5. Crear perfil de usuario y registrar historial de forma atómica\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const isSubscription = !!request.subscriptionId;\n            // Verificar si ya existe un perfil para este usuario (para los security_flags)\n            const existingProfile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(createdUserId);\n            const profileData = {\n                // Preparamos el objeto JSON para la función\n                subscription_plan: request.planId,\n                monthly_token_limit: (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(request.planId),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                stripe_customer_id: request.stripeCustomerId,\n                stripe_subscription_id: request.subscriptionId,\n                last_payment_date: new Date().toISOString(),\n                auto_renew: isSubscription,\n                plan_expires_at: request.planExpiresAt || (isSubscription ? null : this.calculatePlanExpiration(request.planId)),\n                plan_features: planConfig.features,\n                security_flags: _objectSpread({\n                    created_via_webhook: true,\n                    payment_method: 'stripe',\n                    activation_date: new Date().toISOString(),\n                    subscription_type: isSubscription ? 'recurring' : 'one_time'\n                }, existingProfile?.security_flags || {})\n            };\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: createdUserId,\n                p_transaction_id: transaction.id,\n                p_profile_data: profileData\n            }).single(); // .single() es importante para obtener un único resultado\n            if (rpcError) {\n                console.error('Error al ejecutar la función create_user_profile_and_history:', rpcError);\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil y historial creados atómicamente. Profile ID:', profileId);\n            // 6. Actualizar transacción con user_id y marcar como activada\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.updateTransactionWithUser(transaction.id, createdUserId);\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.activateTransaction(transaction.id);\n            console.log('✅ Usuario procesado exitosamente:', createdUserId);\n            return {\n                success: true,\n                userId: createdUserId,\n                profileId: profileId,\n                // Usamos el ID devuelto por la función\n                transactionId: transaction.id\n            };\n        } catch (error) {\n            console.error('❌ Error creando usuario:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Actualizar plan de usuario existente\n   */ static async updateUserPlan(userId, newPlanId, transactionId, reason = 'Plan upgrade/downgrade') {\n        try {\n            console.log('🔄 Actualizando plan de usuario:', userId, 'a', newPlanId);\n            // 1. Obtener perfil actual\n            const currentProfile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!currentProfile) {\n                throw new Error('Usuario no encontrado');\n            }\n            // 2. Validar nuevo plan\n            const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)(newPlanId);\n            if (!planConfig) {\n                throw new Error(`Plan inválido: ${newPlanId}`);\n            }\n            // 3. Actualizar perfil\n            const updatedProfile = _objectSpread(_objectSpread({}, currentProfile), {}, {\n                subscription_plan: newPlanId,\n                monthly_token_limit: (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)(newPlanId),\n                last_payment_date: new Date().toISOString(),\n                payment_verified: true,\n                // Importante: marcar como verificado\n                plan_features: planConfig.features,\n                updated_at: new Date().toISOString()\n            });\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.upsertUserProfile(updatedProfile);\n            // 4. Registrar cambio de plan\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.logPlanChange({\n                user_id: userId,\n                old_plan: currentProfile.subscription_plan,\n                new_plan: newPlanId,\n                changed_by: 'system',\n                reason,\n                transaction_id: transactionId\n            });\n            console.log('✅ Plan actualizado exitosamente');\n            return {\n                success: true,\n                userId,\n                profileId: profile.id\n            };\n        } catch (error) {\n            console.error('❌ Error actualizando plan:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Verificar estado de pago de usuario\n   */ static async verifyUserPaymentStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile) {\n                return {\n                    verified: false,\n                    plan: 'none'\n                };\n            }\n            return {\n                verified: profile.payment_verified,\n                plan: profile.subscription_plan,\n                expiresAt: profile.plan_expires_at || undefined,\n                lastPayment: profile.last_payment_date || undefined\n            };\n        } catch (error) {\n            console.error('Error verificando estado de pago:', error);\n            return {\n                verified: false,\n                plan: 'error'\n            };\n        }\n    }\n    /**\n   * Obtener estadísticas de usuarios\n   */ static async getUserStats() {\n        try {\n            // Esta función requeriría consultas más complejas\n            // Por ahora retornamos estructura básica\n            return {\n                total: 0,\n                byPlan: {\n                    free: 0,\n                    usuario: 0,\n                    pro: 0\n                },\n                verified: 0,\n                unverified: 0\n            };\n        } catch (error) {\n            console.error('Error obteniendo estadísticas:', error);\n            throw error;\n        }\n    }\n    /**\n   * Calcular fecha de expiración para pagos únicos y períodos de gracia\n   */ static calculatePlanExpiration(planId, isGracePeriod = false) {\n        const now = new Date();\n        switch(planId){\n            case 'free':\n                // Plan gratuito expira en 5 días\n                now.setDate(now.getDate() + 5);\n                break;\n            case 'usuario':\n                if (isGracePeriod) {\n                    // Período de gracia de 7 días después de cancelar suscripción\n                    now.setDate(now.getDate() + 7);\n                } else {\n                    // Pago único legacy: 30 días\n                    now.setDate(now.getDate() + 30);\n                }\n                break;\n            case 'pro':\n                if (isGracePeriod) {\n                    // Período de gracia de 14 días después de cancelar suscripción\n                    now.setDate(now.getDate() + 14);\n                } else {\n                    // Pago único legacy: 30 días\n                    now.setDate(now.getDate() + 30);\n                }\n                break;\n            default:\n                // Por defecto, 30 días\n                now.setDate(now.getDate() + 30);\n                break;\n        }\n        return now.toISOString();\n    }\n    /**\n   * Manejar cancelación de suscripción con período de gracia\n   */ static async handleSubscriptionCancellation(userId, currentPlan, subscriptionEndDate, reason = 'Subscription cancelled') {\n        try {\n            console.log('🔄 Manejando cancelación de suscripción:', userId, 'plan:', currentPlan);\n            // Obtener perfil actual\n            const currentProfile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!currentProfile) {\n                throw new Error('Usuario no encontrado');\n            }\n            // Determinar si dar período de gracia o pasar inmediatamente a free\n            let newPlan = 'free';\n            let planExpiresAt;\n            if (subscriptionEndDate) {\n                // Si tenemos la fecha de fin de suscripción de Stripe, usar esa fecha\n                planExpiresAt = subscriptionEndDate;\n                // Mantener el plan actual hasta que expire\n                newPlan = currentPlan;\n            } else {\n                // Sin fecha de fin, dar período de gracia basado en el plan\n                if (currentPlan === 'usuario' || currentPlan === 'pro') {\n                    planExpiresAt = this.calculatePlanExpiration(currentPlan, true);\n                    newPlan = currentPlan; // Mantener plan durante período de gracia\n                } else {\n                    // Para plan free, expirar inmediatamente\n                    planExpiresAt = new Date().toISOString();\n                    newPlan = 'free';\n                }\n            }\n            // Actualizar perfil\n            const updatedProfile = _objectSpread(_objectSpread({}, currentProfile), {}, {\n                subscription_plan: newPlan,\n                plan_expires_at: planExpiresAt,\n                auto_renew: false,\n                // Cancelar auto-renovación\n                stripe_subscription_id: undefined,\n                // Limpiar ID de suscripción\n                updated_at: new Date().toISOString(),\n                security_flags: _objectSpread(_objectSpread({}, currentProfile.security_flags), {}, {\n                    subscription_cancelled: true,\n                    cancellation_date: new Date().toISOString(),\n                    grace_period_until: planExpiresAt\n                })\n            });\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.upsertUserProfile(updatedProfile);\n            // Registrar cambio de plan\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.logPlanChange({\n                user_id: userId,\n                old_plan: currentProfile.subscription_plan,\n                new_plan: newPlan,\n                changed_by: 'system',\n                reason: `${reason} - Grace period until ${planExpiresAt}`\n            });\n            console.log('✅ Cancelación de suscripción procesada con período de gracia');\n            return {\n                success: true,\n                userId,\n                profileId: profile.id\n            };\n        } catch (error) {\n            console.error('❌ Error manejando cancelación de suscripción:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    /**\n   * Procesar usuarios cuyo período de gracia ha expirado\n   * Esta función debe ejecutarse periódicamente (ej: cron job diario)\n   */ static async processExpiredGracePeriods() {\n        try {\n            console.log('🔍 Buscando usuarios con período de gracia expirado...');\n            const now = new Date().toISOString();\n            // Buscar usuarios con período de gracia expirado\n            const { data: expiredUsers, error: searchError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, subscription_plan, plan_expires_at, security_flags').lt('plan_expires_at', now) // plan_expires_at < now\n            .eq('auto_renew', false) // No auto-renovable (cancelado)\n            .neq('subscription_plan', 'free') // No es plan gratuito\n            .limit(100); // Procesar máximo 100 por vez\n            if (searchError) {\n                throw new Error(`Error buscando usuarios expirados: ${searchError.message}`);\n            }\n            if (!expiredUsers || expiredUsers.length === 0) {\n                console.log('✅ No se encontraron usuarios con período de gracia expirado');\n                return {\n                    processed: 0,\n                    errors: []\n                };\n            }\n            console.log(`📋 Encontrados ${expiredUsers.length} usuarios con período de gracia expirado`);\n            const errors = [];\n            let processed = 0;\n            // Procesar cada usuario expirado\n            for (const user of expiredUsers){\n                try {\n                    // Verificar si realmente está en período de gracia\n                    const isInGracePeriod = user.security_flags?.subscription_cancelled === true;\n                    if (!isInGracePeriod) {\n                        console.log(`⚠️ Usuario ${user.user_id} expirado pero no en período de gracia, omitiendo`);\n                        continue;\n                    }\n                    console.log(`⬇️ Degradando usuario ${user.user_id} de ${user.subscription_plan} a free`);\n                    // Degradar a plan gratuito\n                    const result = await this.updateUserPlan(user.user_id, 'free', undefined, `Grace period expired - was ${user.subscription_plan}`);\n                    if (result.success) {\n                        processed++;\n                        console.log(`✅ Usuario ${user.user_id} degradado exitosamente`);\n                    } else {\n                        const errorMsg = `Error degradando usuario ${user.user_id}: ${result.error}`;\n                        console.error(errorMsg);\n                        errors.push(errorMsg);\n                    }\n                } catch (userError) {\n                    const errorMsg = `Error procesando usuario ${user.user_id}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`🎯 Procesamiento completado: ${processed} usuarios degradados, ${errors.length} errores`);\n            return {\n                processed,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en processExpiredGracePeriods:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/userManagement.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/config.ts":
/*!**********************************!*\
  !*** ./src/lib/stripe/config.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_URLS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.APP_URLS),\n/* harmony export */   PLANS: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.PLANS),\n/* harmony export */   getPlanById: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.getPlanById),\n/* harmony export */   isValidPlan: () => (/* reexport safe */ _plans__WEBPACK_IMPORTED_MODULE_1__.isValidPlan),\n/* harmony export */   stripe: () => (/* binding */ stripe)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./plans */ \"(rsc)/./src/lib/stripe/plans.ts\");\n// src/lib/stripe/config.ts\n\n// Inicializar Stripe solo en el servidor\nconst stripe =  true ? new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: '2025-05-28.basil',\n    typescript: true\n}) : 0;\n// Importar configuración de planes\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N0cmlwZS9jb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBQzJCO0FBRTNCO0FBQ08sTUFBTUMsTUFBTSxHQUFHLFFBQ2xCLElBQUlELDhDQUFNLENBQUNFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxpQkFBaUIsRUFBRztJQUN6Q0MsVUFBVSxFQUFFLGtCQUFrQjtJQUM5QkMsVUFBVSxFQUFFO0FBQ2QsQ0FBQyxDQUFDLEdBQ0YsQ0FBSTtBQUVSO0FBQ21FIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzdHJpcGVcXGNvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvbGliL3N0cmlwZS9jb25maWcudHNcbmltcG9ydCBTdHJpcGUgZnJvbSAnc3RyaXBlJztcblxuLy8gSW5pY2lhbGl6YXIgU3RyaXBlIHNvbG8gZW4gZWwgc2Vydmlkb3JcbmV4cG9ydCBjb25zdCBzdHJpcGUgPSB0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJ1xuICA/IG5ldyBTdHJpcGUocHJvY2Vzcy5lbnYuU1RSSVBFX1NFQ1JFVF9LRVkhLCB7XG4gICAgICBhcGlWZXJzaW9uOiAnMjAyNS0wNS0yOC5iYXNpbCcsXG4gICAgICB0eXBlc2NyaXB0OiB0cnVlLFxuICAgIH0pXG4gIDogbnVsbDtcblxuLy8gSW1wb3J0YXIgY29uZmlndXJhY2nDs24gZGUgcGxhbmVzXG5leHBvcnQgeyBQTEFOUywgZ2V0UGxhbkJ5SWQsIGlzVmFsaWRQbGFuLCBBUFBfVVJMUyB9IGZyb20gJy4vcGxhbnMnO1xuZXhwb3J0IHR5cGUgeyBQbGFuSWQgfSBmcm9tICcuL3BsYW5zJztcbiJdLCJuYW1lcyI6WyJTdHJpcGUiLCJzdHJpcGUiLCJwcm9jZXNzIiwiZW52IiwiU1RSSVBFX1NFQ1JFVF9LRVkiLCJhcGlWZXJzaW9uIiwidHlwZXNjcmlwdCIsIlBMQU5TIiwiZ2V0UGxhbkJ5SWQiLCJpc1ZhbGlkUGxhbiIsIkFQUF9VUkxTIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/config.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe/plans.ts":
/*!*********************************!*\
  !*** ./src/lib/stripe/plans.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ADDITIONAL_PRODUCTS: () => (/* binding */ ADDITIONAL_PRODUCTS),\n/* harmony export */   APP_URLS: () => (/* binding */ APP_URLS),\n/* harmony export */   PLANS: () => (/* binding */ PLANS),\n/* harmony export */   getFullPlanConfig: () => (/* binding */ getFullPlanConfig),\n/* harmony export */   getPlanById: () => (/* binding */ getPlanById),\n/* harmony export */   isValidPlan: () => (/* binding */ isValidPlan)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\n// src/lib/stripe/plans.ts\n// Configuración de planes integrada con sistema de límites\n\nconst PLANS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        stripeProductId: null,\n        stripePriceId: null,\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma solo durante 5 días',\n            '• Subida de documentos: máximo 1 documento',\n            '• Generador de test: máximo 10 preguntas test',\n            '• Generador de flashcards: máximo 10 tarjetas flashcard',\n            '• Generador de mapas mentales: máximo 2 mapas mentales',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Habla con tu preparador IA',\n            '• Resúmenes A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.free\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // En centavos (€10.00)\n        stripeProductId: 'prod_SR65BdKdek1OXd',\n        // IMPORTANTE: Este precio debe ser recurrente (suscripción mensual) en Stripe\n        // Si actualmente es un pago único, crear un nuevo precio recurrente en Stripe Dashboard\n        stripePriceId: 'price_1Rae5807kFn3sIXhRf3adX1n',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago',\n            'No incluye:',\n            '• Planificación de estudios',\n            '• Resúmenes A2 y A1'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.usuario\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // En centavos (€15.00)\n        stripeProductId: 'prod_SR66U2G7bVJqu3',\n        stripePriceId: 'price_1Rae3U07kFn3sIXhkvSuJco1',\n        features: [\n            'Incluye:',\n            '• Uso de la plataforma durante el mes (una vez finalizado no podrá volver a acceder hasta renovación)',\n            '• Subida de documentos',\n            '• Planificación de estudios mediante IA*',\n            '• Habla con tu preparador IA *',\n            '• Generador de test *',\n            '• Generador de flashcards *',\n            '• Generador de mapas mentales *',\n            '• Generación de Resúmenes para A2 y A1',\n            '• * Para las tareas en las que se haga uso de IA, el límite mensual será de 1.000.000 de tokens, podrán ampliarse durante el mes mediante pago'\n        ],\n        limits: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro.limits,\n        planConfig: _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_0__.PLAN_CONFIGURATIONS.pro\n    }\n};\n// Función para obtener plan por ID\nfunction getPlanById(planId) {\n    return PLANS[planId] || null;\n}\n// Función para validar si un plan es válido\nfunction isValidPlan(planId) {\n    return planId in PLANS;\n}\n// Función para obtener configuración completa del plan\nfunction getFullPlanConfig(planId) {\n    const plan = getPlanById(planId);\n    return plan?.planConfig || null;\n}\n// Configuración de productos adicionales\nconst ADDITIONAL_PRODUCTS = {\n    tokens: {\n        id: 'tokens',\n        name: 'Tokens Adicionales',\n        description: '1,000,000 tokens adicionales para tu cuenta',\n        price: 1000,\n        // En centavos (€10.00)\n        tokenAmount: 1000000,\n        // Estos IDs se deben crear en Stripe Dashboard\n        stripeProductId: 'prod_tokens_additional',\n        // Placeholder - crear en Stripe\n        stripePriceId: 'price_tokens_additional' // Placeholder - crear en Stripe\n    }\n};\n// URLs de la aplicación\nconst APP_URLS = {\n    success: `${\"http://localhost:3000\"}/thank-you`,\n    cancel: `${\"http://localhost:3000\"}/upgrade-plan`,\n    webhook: `${\"http://localhost:3000\"}/api/stripe/webhook`\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                // Requerido para generateLink\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: _objectSpread(_objectSpread({}, user.user_metadata), {}, {\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                })\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/stripe","vendor-chunks/qs","vendor-chunks/object-inspect","vendor-chunks/get-intrinsic","vendor-chunks/side-channel-list","vendor-chunks/side-channel-weakmap","vendor-chunks/has-symbols","vendor-chunks/side-channel-map","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/call-bound","vendor-chunks/es-errors","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fstripe%2Fwebhook%2Froute&page=%2Fapi%2Fstripe%2Fwebhook%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstripe%2Fwebhook%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();