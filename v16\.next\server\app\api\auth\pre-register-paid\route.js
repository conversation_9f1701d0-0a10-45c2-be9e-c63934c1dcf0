/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/pre-register-paid/route";
exports.ids = ["app/api/auth/pre-register-paid/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_pre_register_paid_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/pre-register-paid/route.ts */ \"(rsc)/./src/app/api/auth/pre-register-paid/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/pre-register-paid/route\",\n        pathname: \"/api/auth/pre-register-paid\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/pre-register-paid/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\auth\\\\pre-register-paid\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_pre_register_paid_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/pre-register-paid/route.ts":
/*!*****************************************************!*\
  !*** ./src/app/api/auth/pre-register-paid/route.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(rsc)/./src/lib/utils/planLimits.ts\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/app/api/auth/pre-register-paid/route.ts\n// API para pre-registrar usuarios de planes de pago antes del checkout\n\n\n\nasync function POST(request) {\n    console.log('🚀 [PRE-REGISTER-PAID] Endpoint llamado');\n    try {\n        console.log('📥 [PRE-REGISTER-PAID] Parseando body...');\n        const body = await request.json();\n        console.log('📥 [PRE-REGISTER-PAID] Body parseado:', _objectSpread(_objectSpread({}, body), {}, {\n            password: '[HIDDEN]'\n        }));\n        const { email, password, customerName, planId } = body;\n        // Validaciones básicas\n        if (!email || !password || !planId) {\n            console.log('❌ [PRE-REGISTER-PAID] Validación básica falló');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Email, contraseña y plan son requeridos'\n            }, {\n                status: 400\n            });\n        }\n        if (password.length < 6) {\n            console.log('❌ [PRE-REGISTER-PAID] Contraseña muy corta');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'La contraseña debe tener al menos 6 caracteres'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🔍 [PRE-REGISTER-PAID] Validando plan...');\n        // Validar que el plan existe y es de pago\n        const planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_2__.getPlanConfiguration)(planId);\n        if (!planConfig) {\n            console.log('❌ [PRE-REGISTER-PAID] Plan no válido:', planId);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Plan no válido'\n            }, {\n                status: 400\n            });\n        }\n        if (planId === 'free') {\n            console.log('❌ [PRE-REGISTER-PAID] Plan gratuito no permitido');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Este endpoint es solo para planes de pago'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🔄 Pre-registrando usuario para plan de pago:', {\n            email,\n            planId,\n            customerName,\n            timestamp: new Date().toISOString()\n        });\n        try {\n            // Crear usuario en Supabase sin confirmar email\n            const userData = {\n                name: customerName || email.split('@')[0],\n                plan: planId,\n                payment_verified: false,\n                pre_registered: true,\n                pre_registration_date: new Date().toISOString(),\n                awaiting_payment: true\n            };\n            const { data, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.SupabaseAdminService.createUserWithPassword(email, password, userData, false // No enviar email de confirmación todavía\n            );\n            if (error) {\n                console.error('Error creando usuario:', error);\n                // Manejar error de email duplicado\n                if (error.message?.includes('User already registered') || error.message?.includes('email_address_not_authorized')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: 'Ya existe una cuenta con este email. Por favor, usa otro email o inicia sesión.'\n                    }, {\n                        status: 409\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error creando la cuenta. Por favor, inténtalo de nuevo.'\n                }, {\n                    status: 500\n                });\n            }\n            if (!data?.user) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Error creando la cuenta'\n                }, {\n                    status: 500\n                });\n            }\n            const userId = data.user.id;\n            console.log('✅ Usuario pre-registrado exitosamente:', userId);\n            // Crear perfil de usuario en estado pendiente\n            try {\n                const profileResult = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_1__.SupabaseAdminService.createUserProfile({\n                    user_id: userId,\n                    subscription_plan: planId,\n                    monthly_token_limit: planConfig.tokenLimit,\n                    current_month_tokens: 0,\n                    current_month: new Date().toISOString().split('T')[0],\n                    payment_verified: false,\n                    plan_features: planConfig.features,\n                    security_flags: {\n                        pre_registered: true,\n                        awaiting_payment: true,\n                        created_at: new Date().toISOString()\n                    }\n                });\n                console.log('✅ Perfil de usuario creado en estado pendiente');\n            } catch (profileError) {\n                console.error('Error creando perfil:', profileError);\n            // No fallar completamente, el perfil se puede crear después\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                userId: userId,\n                message: 'Usuario pre-registrado exitosamente',\n                data: {\n                    email: email,\n                    planId: planId,\n                    awaitingPayment: true\n                }\n            });\n        } catch (createError) {\n            console.error('Error en pre-registro:', createError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error interno del servidor'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Error procesando pre-registro:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error procesando la solicitud'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/pre-register-paid/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Tipos para las nuevas tablas\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                // Requerido para generateLink\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: _objectSpread(_objectSpread({}, user.user_metadata), {}, {\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                })\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/planLimits.ts":
/*!*************************************!*\
  !*** ./src/lib/utils/planLimits.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/lib/utils/planLimits.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            // Límite total para el trial de 5 días\n            testsForTrial: 10,\n            // Límite total para el trial de 5 días\n            flashcardsForTrial: 10,\n            // Límite total para el trial de 5 días\n            tokensForTrial: 50000,\n            // Límite total para el trial de 5 días\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        // €10.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        // €15.00\n        limits: {\n            documents: -1,\n            // ilimitado\n            mindMapsPerWeek: -1,\n            // Ilimitado semanal\n            testsPerWeek: -1,\n            // Ilimitado semanal\n            flashcardsPerWeek: -1,\n            // Ilimitado semanal\n            monthlyTokens: 1000000,\n            // Límite mensual\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 1000000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzL3BsYW5MaW1pdHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQXdCQTtBQUNPLE1BQU1BLG1CQUFzRCxHQUFHO0lBQ3BFQyxJQUFJLEVBQUU7UUFDSkMsRUFBRSxFQUFFLE1BQU07UUFDVkMsSUFBSSxFQUFFLGFBQWE7UUFDbkJDLEtBQUssRUFBRSxDQUFDO1FBQ1JDLE1BQU0sRUFBRTtZQUNOQyxTQUFTLEVBQUUsQ0FBQztZQUNaQyxnQkFBZ0IsRUFBRSxDQUFDO1lBQVM7WUFDNUJDLGFBQWEsRUFBRSxFQUFFO1lBQVc7WUFDNUJDLGtCQUFrQixFQUFFLEVBQUU7WUFBTTtZQUM1QkMsY0FBYyxFQUFFLEtBQUs7WUFBTztZQUM1QkMsUUFBUSxFQUFFO2dCQUFDLGlCQUFpQjtnQkFBRSxpQkFBaUI7Z0JBQUUsc0JBQXNCO2dCQUFFLHFCQUFxQjthQUFBO1FBQ2hHLENBQUM7UUFDREEsUUFBUSxFQUFFO1lBQ1IsaUJBQWlCO1lBQ2pCLGlCQUFpQjtZQUNqQixzQkFBc0I7WUFDdEIscUJBQXFCO1NBQ3RCO1FBQ0RDLGtCQUFrQixFQUFFO1lBQ2xCLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsZUFBZTtTQUFBO0lBRW5CLENBQUM7SUFDREMsT0FBTyxFQUFFO1FBQ1BYLEVBQUUsRUFBRSxTQUFTO1FBQ2JDLElBQUksRUFBRSxjQUFjO1FBQ3BCQyxLQUFLLEVBQUUsSUFBSTtRQUFFO1FBQ2JDLE1BQU0sRUFBRTtZQUNOQyxTQUFTLEVBQUUsQ0FBQyxDQUFDO1lBQUU7WUFDZlEsZUFBZSxFQUFFLENBQUMsQ0FBQztZQUFTO1lBQzVCQyxZQUFZLEVBQUUsQ0FBQyxDQUFDO1lBQVk7WUFDNUJDLGlCQUFpQixFQUFFLENBQUMsQ0FBQztZQUFPO1lBQzVCQyxhQUFhLEVBQUUsT0FBTztZQUFNO1lBQzVCTixRQUFRLEVBQUU7Z0JBQUMsaUJBQWlCO2dCQUFFLGVBQWU7Z0JBQUUsaUJBQWlCO2dCQUFFLHNCQUFzQjtnQkFBRSxxQkFBcUI7YUFBQTtRQUNqSCxDQUFDO1FBQ0RBLFFBQVEsRUFBRTtZQUNSLGlCQUFpQjtZQUNqQixlQUFlO1lBQ2YsaUJBQWlCO1lBQ2pCLHNCQUFzQjtZQUN0QixxQkFBcUI7U0FDdEI7UUFDREMsa0JBQWtCLEVBQUU7WUFDbEIsZ0JBQWdCO1lBQ2hCLGVBQWU7U0FBQTtJQUVuQixDQUFDO0lBQ0RNLEdBQUcsRUFBRTtRQUNIaEIsRUFBRSxFQUFFLEtBQUs7UUFDVEMsSUFBSSxFQUFFLFVBQVU7UUFDaEJDLEtBQUssRUFBRSxJQUFJO1FBQUU7UUFDYkMsTUFBTSxFQUFFO1lBQ05DLFNBQVMsRUFBRSxDQUFDLENBQUM7WUFBRTtZQUNmUSxlQUFlLEVBQUUsQ0FBQyxDQUFDO1lBQVM7WUFDNUJDLFlBQVksRUFBRSxDQUFDLENBQUM7WUFBWTtZQUM1QkMsaUJBQWlCLEVBQUUsQ0FBQyxDQUFDO1lBQU87WUFDNUJDLGFBQWEsRUFBRSxPQUFPO1lBQU07WUFDNUJOLFFBQVEsRUFBRTtnQkFBQyxpQkFBaUI7Z0JBQUUsZ0JBQWdCO2dCQUFFLGVBQWU7Z0JBQUUsaUJBQWlCO2dCQUFFLHNCQUFzQjtnQkFBRSxxQkFBcUI7Z0JBQUUsZUFBZTthQUFBO1FBQ3BKLENBQUM7UUFDREEsUUFBUSxFQUFFO1lBQ1IsaUJBQWlCO1lBQ2pCLGdCQUFnQjtZQUNoQixlQUFlO1lBQ2YsaUJBQWlCO1lBQ2pCLHNCQUFzQjtZQUN0QixxQkFBcUI7WUFDckIsZUFBZTtTQUNoQjtRQUNEQyxrQkFBa0IsRUFBRTtJQUN0QjtBQUNGLENBQUM7QUFFRDtBQUNPLFNBQVNPLG9CQUFvQkEsQ0FBQ0MsTUFBYyxFQUE0QjtJQUM3RSxPQUFPcEIsbUJBQW1CLENBQUNvQixNQUFNLENBQUMsSUFBSSxJQUFJO0FBQzVDO0FBRU8sU0FBU0Msb0JBQW9CQSxDQUFDRCxNQUFjLEVBQVU7SUFDM0QsTUFBTUUsTUFBTSxHQUFHSCxvQkFBb0IsQ0FBQ0MsTUFBTSxDQUFDO0lBQzNDLElBQUksQ0FBQ0UsTUFBTSxFQUFFLE9BQU8sS0FBSztJQUV6QjtJQUNBLElBQUlGLE1BQU0sS0FBSyxNQUFNLEVBQUU7UUFDckIsT0FBT0UsTUFBTSxDQUFDakIsTUFBTSxDQUFDSyxjQUFjLElBQUksS0FBSztJQUM5QztJQUVBO0lBQ0EsT0FBT1ksTUFBTSxDQUFDakIsTUFBTSxDQUFDWSxhQUFhLElBQUksT0FBTztBQUMvQztBQUVPLFNBQVNNLGdCQUFnQkEsQ0FBQ0gsTUFBYyxFQUFFSSxPQUFlLEVBQVc7SUFDekUsTUFBTUYsTUFBTSxHQUFHSCxvQkFBb0IsQ0FBQ0MsTUFBTSxDQUFDO0lBQzNDLElBQUksQ0FBQ0UsTUFBTSxFQUFFLE9BQU8sS0FBSztJQUV6QjtJQUNBLElBQUlBLE1BQU0sQ0FBQ1Ysa0JBQWtCLENBQUNhLFFBQVEsQ0FBQ0QsT0FBTyxDQUFDLEVBQUU7UUFDL0MsT0FBTyxLQUFLO0lBQ2Q7SUFFQTtJQUNBLE9BQU9GLE1BQU0sQ0FBQ1gsUUFBUSxDQUFDYyxRQUFRLENBQUNELE9BQU8sQ0FBQztBQUMxQztBQUVPLFNBQVNFLGNBQWNBLENBQUNOLE1BQWMsRUFBRU8sU0FBOEMsRUFBVTtJQUNyRyxNQUFNTCxNQUFNLEdBQUdILG9CQUFvQixDQUFDQyxNQUFNLENBQUM7SUFDM0MsSUFBSSxDQUFDRSxNQUFNLEVBQUUsT0FBTyxDQUFDO0lBRXJCLE9BQVFLLFNBQVM7UUFDZixLQUFLLFVBQVU7WUFDYixPQUFPTCxNQUFNLENBQUNqQixNQUFNLENBQUNTLGVBQWUsSUFBSSxDQUFDO1FBQzNDLEtBQUssT0FBTztZQUNWLE9BQU9RLE1BQU0sQ0FBQ2pCLE1BQU0sQ0FBQ1UsWUFBWSxJQUFJLENBQUM7UUFDeEMsS0FBSyxZQUFZO1lBQ2YsT0FBT08sTUFBTSxDQUFDakIsTUFBTSxDQUFDVyxpQkFBaUIsSUFBSSxDQUFDO1FBQzdDO1lBQ0UsT0FBTyxDQUFDO0lBQ1o7QUFDRjtBQUVBO0FBQ08sU0FBU1ksYUFBYUEsQ0FBQ1IsTUFBYyxFQUFFTyxTQUF5RCxFQUFVO0lBQy9HLE1BQU1MLE1BQU0sR0FBR0gsb0JBQW9CLENBQUNDLE1BQU0sQ0FBQztJQUMzQyxJQUFJLENBQUNFLE1BQU0sSUFBSUYsTUFBTSxLQUFLLE1BQU0sRUFBRSxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUM7SUFFN0MsT0FBUU8sU0FBUztRQUNmLEtBQUssVUFBVTtZQUNiLE9BQU9MLE1BQU0sQ0FBQ2pCLE1BQU0sQ0FBQ0UsZ0JBQWdCLElBQUksQ0FBQztRQUM1QyxLQUFLLE9BQU87WUFDVixPQUFPZSxNQUFNLENBQUNqQixNQUFNLENBQUNHLGFBQWEsSUFBSSxDQUFDO1FBQ3pDLEtBQUssWUFBWTtZQUNmLE9BQU9jLE1BQU0sQ0FBQ2pCLE1BQU0sQ0FBQ0ksa0JBQWtCLElBQUksQ0FBQztRQUM5QyxLQUFLLFFBQVE7WUFDWCxPQUFPYSxNQUFNLENBQUNqQixNQUFNLENBQUNLLGNBQWMsSUFBSSxDQUFDO1FBQzFDO1lBQ0UsT0FBTyxDQUFDO0lBQ1o7QUFDRjtBQUVPLFNBQVNtQixXQUFXQSxDQUFDQyxLQUFhLEVBQVc7SUFDbEQsT0FBT0EsS0FBSyxLQUFLLENBQUMsQ0FBQztBQUNyQjtBQUVBO0FBQ08sU0FBU0MsZ0JBQWdCQSxDQUM5QlgsTUFBYyxFQUNkSSxPQUFlLEVBQ2ZRLFlBQW9CLEVBQ3BCTCxTQUErQyxFQUNSO0lBQ3ZDLE1BQU1MLE1BQU0sR0FBR0gsb0JBQW9CLENBQUNDLE1BQU0sQ0FBQztJQUUzQyxJQUFJLENBQUNFLE1BQU0sRUFBRTtRQUNYLE9BQU87WUFBRVcsT0FBTyxFQUFFLEtBQUs7WUFBRUMsTUFBTSxFQUFFO1FBQWlCLENBQUM7SUFDckQ7SUFFQTtJQUNBLElBQUksQ0FBQ1gsZ0JBQWdCLENBQUNILE1BQU0sRUFBRUksT0FBTyxDQUFDLEVBQUU7UUFDdEMsT0FBTztZQUFFUyxPQUFPLEVBQUUsS0FBSztZQUFFQyxNQUFNLEVBQUcsa0JBQWlCVixPQUFRLHFCQUFvQkYsTUFBTSxDQUFDbkIsSUFBSztRQUFFLENBQUM7SUFDaEc7SUFFQTtJQUNBLElBQUl3QixTQUFTLEVBQUU7UUFDYixNQUFNUSxXQUFXLEdBQUdULGNBQWMsQ0FBQ04sTUFBTSxFQUFFTyxTQUFTLENBQUM7UUFDckQsSUFBSSxDQUFDRSxXQUFXLENBQUNNLFdBQVcsQ0FBQyxJQUFJSCxZQUFZLElBQUlHLFdBQVcsRUFBRTtZQUM1RCxPQUFPO2dCQUFFRixPQUFPLEVBQUUsS0FBSztnQkFBRUMsTUFBTSxFQUFHLHFCQUFvQlAsU0FBVSxlQUFjUSxXQUFZO1lBQUcsQ0FBQztRQUNoRztJQUNGO0lBRUEsT0FBTztRQUFFRixPQUFPLEVBQUU7SUFBSyxDQUFDO0FBQzFCO0FBRUE7QUFDTyxlQUFlRyxzQkFBc0JBLENBQUNaLE9BQWUsRUFBb0I7SUFDOUUsSUFBSTtRQUNGO1FBQ0EsTUFBTWEsUUFBUSxHQUFHLE1BQU1DLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQztRQUM5QyxJQUFJLENBQUNELFFBQVEsQ0FBQ0UsRUFBRSxFQUFFO1lBQ2hCQyxPQUFPLENBQUNDLEtBQUssQ0FBQyxtQ0FBbUMsQ0FBQztZQUNsRDtZQUNBLE9BQU9sQixnQkFBZ0IsQ0FBQyxNQUFNLEVBQUVDLE9BQU8sQ0FBQztRQUMxQztRQUVBLE1BQU0sRUFBRWtCLElBQUFBLEVBQU0sR0FBRyxNQUFNTCxRQUFRLENBQUNNLElBQUksQ0FBQyxDQUFDO1FBQ3RDLE1BQU1DLFFBQVEsR0FBR0YsSUFBSSxJQUFJLE1BQU07UUFFL0I7UUFDQSxPQUFPbkIsZ0JBQWdCLENBQUNxQixRQUFRLEVBQUVwQixPQUFPLENBQUM7SUFDNUMsQ0FBQyxDQUFDLE9BQU9pQixLQUFLLEVBQUU7UUFDZEQsT0FBTyxDQUFDQyxLQUFLLENBQUMsNENBQTRDLEVBQUVBLEtBQUssQ0FBQztRQUNsRTtRQUNBLE9BQU9sQixnQkFBZ0IsQ0FBQyxNQUFNLEVBQUVDLE9BQU8sQ0FBQztJQUMxQztBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFx1dGlsc1xccGxhbkxpbWl0cy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvbGliL3V0aWxzL3BsYW5MaW1pdHMudHNcbi8vIENvbmZpZ3VyYWNpw7NuIGNlbnRyYWxpemFkYSBkZSBsw61taXRlcyB5IGNhcmFjdGVyw61zdGljYXMgcG9yIHBsYW5cblxuZXhwb3J0IGludGVyZmFjZSBQbGFuTGltaXRzIHtcbiAgZG9jdW1lbnRzOiBudW1iZXI7IC8vIC0xID0gaWxpbWl0YWRvXG4gIG1pbmRNYXBzUGVyV2Vlaz86IG51bWJlcjsgICAgICAvLyBQYXJhIHBsYW5lcyBkZSBwYWdvIGNvbiBsw61taXRlcyBzZW1hbmFsZXNcbiAgdGVzdHNQZXJXZWVrPzogbnVtYmVyOyAgICAgICAgIC8vIFBhcmEgcGxhbmVzIGRlIHBhZ28gY29uIGzDrW1pdGVzIHNlbWFuYWxlc1xuICBmbGFzaGNhcmRzUGVyV2Vlaz86IG51bWJlcjsgICAgLy8gUGFyYSBwbGFuZXMgZGUgcGFnbyBjb24gbMOtbWl0ZXMgc2VtYW5hbGVzXG4gIG1vbnRobHlUb2tlbnM/OiBudW1iZXI7ICAgICAgICAvLyBQYXJhIHBsYW5lcyBkZSBwYWdvIGNvbiBsw61taXRlcyBtZW5zdWFsZXNcbiAgbWluZE1hcHNGb3JUcmlhbD86IG51bWJlcjsgICAgIC8vIEVzcGVjw61maWNvIHBhcmEgdHJpYWwgZGUgNSBkw61hc1xuICB0ZXN0c0ZvclRyaWFsPzogbnVtYmVyOyAgICAgICAgLy8gRXNwZWPDrWZpY28gcGFyYSB0cmlhbCBkZSA1IGTDrWFzXG4gIGZsYXNoY2FyZHNGb3JUcmlhbD86IG51bWJlcjsgICAvLyBFc3BlY8OtZmljbyBwYXJhIHRyaWFsIGRlIDUgZMOtYXNcbiAgdG9rZW5zRm9yVHJpYWw/OiBudW1iZXI7ICAgICAgIC8vIEVzcGVjw61maWNvIHBhcmEgdHJpYWwgZGUgNSBkw61hc1xuICBmZWF0dXJlczogc3RyaW5nW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUGxhbkNvbmZpZ3VyYXRpb24ge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIHByaWNlOiBudW1iZXI7IC8vIGVuIGNlbnRhdm9zXG4gIGxpbWl0czogUGxhbkxpbWl0cztcbiAgZmVhdHVyZXM6IHN0cmluZ1tdO1xuICByZXN0cmljdGVkRmVhdHVyZXM6IHN0cmluZ1tdO1xufVxuXG4vLyBDb25maWd1cmFjacOzbiBjb21wbGV0YSBkZSBwbGFuZXMgY29uIGzDrW1pdGVzIHkgY2FyYWN0ZXLDrXN0aWNhc1xuZXhwb3J0IGNvbnN0IFBMQU5fQ09ORklHVVJBVElPTlM6IFJlY29yZDxzdHJpbmcsIFBsYW5Db25maWd1cmF0aW9uPiA9IHtcbiAgZnJlZToge1xuICAgIGlkOiAnZnJlZScsXG4gICAgbmFtZTogJ1BsYW4gR3JhdGlzJyxcbiAgICBwcmljZTogMCxcbiAgICBsaW1pdHM6IHtcbiAgICAgIGRvY3VtZW50czogMSxcbiAgICAgIG1pbmRNYXBzRm9yVHJpYWw6IDIsICAgICAgICAvLyBMw61taXRlIHRvdGFsIHBhcmEgZWwgdHJpYWwgZGUgNSBkw61hc1xuICAgICAgdGVzdHNGb3JUcmlhbDogMTAsICAgICAgICAgIC8vIEzDrW1pdGUgdG90YWwgcGFyYSBlbCB0cmlhbCBkZSA1IGTDrWFzXG4gICAgICBmbGFzaGNhcmRzRm9yVHJpYWw6IDEwLCAgICAgLy8gTMOtbWl0ZSB0b3RhbCBwYXJhIGVsIHRyaWFsIGRlIDUgZMOtYXNcbiAgICAgIHRva2Vuc0ZvclRyaWFsOiA1MDAwMCwgICAgICAvLyBMw61taXRlIHRvdGFsIHBhcmEgZWwgdHJpYWwgZGUgNSBkw61hc1xuICAgICAgZmVhdHVyZXM6IFsnZG9jdW1lbnRfdXBsb2FkJywgJ3Rlc3RfZ2VuZXJhdGlvbicsICdmbGFzaGNhcmRfZ2VuZXJhdGlvbicsICdtaW5kX21hcF9nZW5lcmF0aW9uJ11cbiAgICB9LFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnZG9jdW1lbnRfdXBsb2FkJyxcbiAgICAgICd0ZXN0X2dlbmVyYXRpb24nLFxuICAgICAgJ2ZsYXNoY2FyZF9nZW5lcmF0aW9uJywgXG4gICAgICAnbWluZF9tYXBfZ2VuZXJhdGlvbidcbiAgICBdLFxuICAgIHJlc3RyaWN0ZWRGZWF0dXJlczogW1xuICAgICAgJ3N0dWR5X3BsYW5uaW5nJyxcbiAgICAgICdhaV90dXRvcl9jaGF0JyxcbiAgICAgICdzdW1tYXJ5X2ExX2EyJ1xuICAgIF1cbiAgfSxcbiAgdXN1YXJpbzoge1xuICAgIGlkOiAndXN1YXJpbycsXG4gICAgbmFtZTogJ1BsYW4gVXN1YXJpbycsXG4gICAgcHJpY2U6IDEwMDAsIC8vIOKCrDEwLjAwXG4gICAgbGltaXRzOiB7XG4gICAgICBkb2N1bWVudHM6IC0xLCAvLyBpbGltaXRhZG9cbiAgICAgIG1pbmRNYXBzUGVyV2VlazogLTEsICAgICAgICAvLyBJbGltaXRhZG8gc2VtYW5hbFxuICAgICAgdGVzdHNQZXJXZWVrOiAtMSwgICAgICAgICAgIC8vIElsaW1pdGFkbyBzZW1hbmFsXG4gICAgICBmbGFzaGNhcmRzUGVyV2VlazogLTEsICAgICAgLy8gSWxpbWl0YWRvIHNlbWFuYWxcbiAgICAgIG1vbnRobHlUb2tlbnM6IDEwMDAwMDAsICAgICAvLyBMw61taXRlIG1lbnN1YWxcbiAgICAgIGZlYXR1cmVzOiBbJ2RvY3VtZW50X3VwbG9hZCcsICdhaV90dXRvcl9jaGF0JywgJ3Rlc3RfZ2VuZXJhdGlvbicsICdmbGFzaGNhcmRfZ2VuZXJhdGlvbicsICdtaW5kX21hcF9nZW5lcmF0aW9uJ11cbiAgICB9LFxuICAgIGZlYXR1cmVzOiBbXG4gICAgICAnZG9jdW1lbnRfdXBsb2FkJyxcbiAgICAgICdhaV90dXRvcl9jaGF0JyxcbiAgICAgICd0ZXN0X2dlbmVyYXRpb24nLFxuICAgICAgJ2ZsYXNoY2FyZF9nZW5lcmF0aW9uJyxcbiAgICAgICdtaW5kX21hcF9nZW5lcmF0aW9uJ1xuICAgIF0sXG4gICAgcmVzdHJpY3RlZEZlYXR1cmVzOiBbXG4gICAgICAnc3R1ZHlfcGxhbm5pbmcnLFxuICAgICAgJ3N1bW1hcnlfYTFfYTInXG4gICAgXVxuICB9LFxuICBwcm86IHtcbiAgICBpZDogJ3BybycsXG4gICAgbmFtZTogJ1BsYW4gUHJvJyxcbiAgICBwcmljZTogMTUwMCwgLy8g4oKsMTUuMDBcbiAgICBsaW1pdHM6IHtcbiAgICAgIGRvY3VtZW50czogLTEsIC8vIGlsaW1pdGFkb1xuICAgICAgbWluZE1hcHNQZXJXZWVrOiAtMSwgICAgICAgIC8vIElsaW1pdGFkbyBzZW1hbmFsXG4gICAgICB0ZXN0c1BlcldlZWs6IC0xLCAgICAgICAgICAgLy8gSWxpbWl0YWRvIHNlbWFuYWxcbiAgICAgIGZsYXNoY2FyZHNQZXJXZWVrOiAtMSwgICAgICAvLyBJbGltaXRhZG8gc2VtYW5hbFxuICAgICAgbW9udGhseVRva2VuczogMTAwMDAwMCwgICAgIC8vIEzDrW1pdGUgbWVuc3VhbFxuICAgICAgZmVhdHVyZXM6IFsnZG9jdW1lbnRfdXBsb2FkJywgJ3N0dWR5X3BsYW5uaW5nJywgJ2FpX3R1dG9yX2NoYXQnLCAndGVzdF9nZW5lcmF0aW9uJywgJ2ZsYXNoY2FyZF9nZW5lcmF0aW9uJywgJ21pbmRfbWFwX2dlbmVyYXRpb24nLCAnc3VtbWFyeV9hMV9hMiddXG4gICAgfSxcbiAgICBmZWF0dXJlczogW1xuICAgICAgJ2RvY3VtZW50X3VwbG9hZCcsXG4gICAgICAnc3R1ZHlfcGxhbm5pbmcnLFxuICAgICAgJ2FpX3R1dG9yX2NoYXQnLFxuICAgICAgJ3Rlc3RfZ2VuZXJhdGlvbicsXG4gICAgICAnZmxhc2hjYXJkX2dlbmVyYXRpb24nLFxuICAgICAgJ21pbmRfbWFwX2dlbmVyYXRpb24nLFxuICAgICAgJ3N1bW1hcnlfYTFfYTInXG4gICAgXSxcbiAgICByZXN0cmljdGVkRmVhdHVyZXM6IFtdXG4gIH1cbn07XG5cbi8vIEZ1bmNpb25lcyBkZSB1dGlsaWRhZFxuZXhwb3J0IGZ1bmN0aW9uIGdldFBsYW5Db25maWd1cmF0aW9uKHBsYW5JZDogc3RyaW5nKTogUGxhbkNvbmZpZ3VyYXRpb24gfCBudWxsIHtcbiAgcmV0dXJuIFBMQU5fQ09ORklHVVJBVElPTlNbcGxhbklkXSB8fCBudWxsO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0VG9rZW5MaW1pdEZvclBsYW4ocGxhbklkOiBzdHJpbmcpOiBudW1iZXIge1xuICBjb25zdCBjb25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbihwbGFuSWQpO1xuICBpZiAoIWNvbmZpZykgcmV0dXJuIDUwMDAwO1xuXG4gIC8vIFBhcmEgcGxhbiBncmF0dWl0bywgdXNhciB0b2tlbnNGb3JUcmlhbFxuICBpZiAocGxhbklkID09PSAnZnJlZScpIHtcbiAgICByZXR1cm4gY29uZmlnLmxpbWl0cy50b2tlbnNGb3JUcmlhbCB8fCA1MDAwMDtcbiAgfVxuXG4gIC8vIFBhcmEgcGxhbmVzIGRlIHBhZ28sIHVzYXIgbW9udGhseVRva2Vuc1xuICByZXR1cm4gY29uZmlnLmxpbWl0cy5tb250aGx5VG9rZW5zIHx8IDEwMDAwMDA7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoYXNGZWF0dXJlQWNjZXNzKHBsYW5JZDogc3RyaW5nLCBmZWF0dXJlOiBzdHJpbmcpOiBib29sZWFuIHtcbiAgY29uc3QgY29uZmlnID0gZ2V0UGxhbkNvbmZpZ3VyYXRpb24ocGxhbklkKTtcbiAgaWYgKCFjb25maWcpIHJldHVybiBmYWxzZTtcblxuICAvLyBTaSBsYSBjYXJhY3RlcsOtc3RpY2EgZXN0w6EgZW4gbGEgbGlzdGEgZGUgcmVzdHJpbmdpZGFzLCBubyB0aWVuZSBhY2Nlc29cbiAgaWYgKGNvbmZpZy5yZXN0cmljdGVkRmVhdHVyZXMuaW5jbHVkZXMoZmVhdHVyZSkpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICAvLyBTaSBubyBlc3TDoSByZXN0cmluZ2lkYSwgdmVyaWZpY2FyIHNpIGVzdMOhIGVuIGxhcyBjYXJhY3RlcsOtc3RpY2FzIHBlcm1pdGlkYXNcbiAgcmV0dXJuIGNvbmZpZy5mZWF0dXJlcy5pbmNsdWRlcyhmZWF0dXJlKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdldFdlZWtseUxpbWl0KHBsYW5JZDogc3RyaW5nLCBsaW1pdFR5cGU6ICdtaW5kTWFwcycgfCAndGVzdHMnIHwgJ2ZsYXNoY2FyZHMnKTogbnVtYmVyIHtcbiAgY29uc3QgY29uZmlnID0gZ2V0UGxhbkNvbmZpZ3VyYXRpb24ocGxhbklkKTtcbiAgaWYgKCFjb25maWcpIHJldHVybiAwO1xuXG4gIHN3aXRjaCAobGltaXRUeXBlKSB7XG4gICAgY2FzZSAnbWluZE1hcHMnOlxuICAgICAgcmV0dXJuIGNvbmZpZy5saW1pdHMubWluZE1hcHNQZXJXZWVrIHx8IDA7XG4gICAgY2FzZSAndGVzdHMnOlxuICAgICAgcmV0dXJuIGNvbmZpZy5saW1pdHMudGVzdHNQZXJXZWVrIHx8IDA7XG4gICAgY2FzZSAnZmxhc2hjYXJkcyc6XG4gICAgICByZXR1cm4gY29uZmlnLmxpbWl0cy5mbGFzaGNhcmRzUGVyV2VlayB8fCAwO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gMDtcbiAgfVxufVxuXG4vLyBOdWV2YSBmdW5jacOzbiBwYXJhIG9idGVuZXIgbMOtbWl0ZXMgZGUgdHJpYWwgKHBhcmEgcGxhbiBncmF0dWl0bylcbmV4cG9ydCBmdW5jdGlvbiBnZXRUcmlhbExpbWl0KHBsYW5JZDogc3RyaW5nLCBsaW1pdFR5cGU6ICdtaW5kTWFwcycgfCAndGVzdHMnIHwgJ2ZsYXNoY2FyZHMnIHwgJ3Rva2VucycpOiBudW1iZXIge1xuICBjb25zdCBjb25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbihwbGFuSWQpO1xuICBpZiAoIWNvbmZpZyB8fCBwbGFuSWQgIT09ICdmcmVlJykgcmV0dXJuIC0xOyAvLyAtMSBwYXJhIGlsaW1pdGFkbyBvIG5vIGFwbGljYVxuXG4gIHN3aXRjaCAobGltaXRUeXBlKSB7XG4gICAgY2FzZSAnbWluZE1hcHMnOlxuICAgICAgcmV0dXJuIGNvbmZpZy5saW1pdHMubWluZE1hcHNGb3JUcmlhbCB8fCAwO1xuICAgIGNhc2UgJ3Rlc3RzJzpcbiAgICAgIHJldHVybiBjb25maWcubGltaXRzLnRlc3RzRm9yVHJpYWwgfHwgMDtcbiAgICBjYXNlICdmbGFzaGNhcmRzJzpcbiAgICAgIHJldHVybiBjb25maWcubGltaXRzLmZsYXNoY2FyZHNGb3JUcmlhbCB8fCAwO1xuICAgIGNhc2UgJ3Rva2Vucyc6XG4gICAgICByZXR1cm4gY29uZmlnLmxpbWl0cy50b2tlbnNGb3JUcmlhbCB8fCAwO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gMDtcbiAgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNVbmxpbWl0ZWQobGltaXQ6IG51bWJlcik6IGJvb2xlYW4ge1xuICByZXR1cm4gbGltaXQgPT09IC0xO1xufVxuXG4vLyBWYWxpZGFyIHNpIHVuIHVzdWFyaW8gcHVlZGUgcmVhbGl6YXIgdW5hIGFjY2nDs24gZXNwZWPDrWZpY2FcbmV4cG9ydCBmdW5jdGlvbiBjYW5QZXJmb3JtQWN0aW9uKFxuICBwbGFuSWQ6IHN0cmluZyxcbiAgZmVhdHVyZTogc3RyaW5nLFxuICBjdXJyZW50VXNhZ2U6IG51bWJlcixcbiAgbGltaXRUeXBlPzogJ21pbmRNYXBzJyB8ICd0ZXN0cycgfCAnZmxhc2hjYXJkcydcbik6IHsgYWxsb3dlZDogYm9vbGVhbjsgcmVhc29uPzogc3RyaW5nIH0ge1xuICBjb25zdCBjb25maWcgPSBnZXRQbGFuQ29uZmlndXJhdGlvbihwbGFuSWQpO1xuXG4gIGlmICghY29uZmlnKSB7XG4gICAgcmV0dXJuIHsgYWxsb3dlZDogZmFsc2UsIHJlYXNvbjogJ1BsYW4gbm8gdsOhbGlkbycgfTtcbiAgfVxuXG4gIC8vIFZlcmlmaWNhciBzaSB0aWVuZSBhY2Nlc28gYSBsYSBjYXJhY3RlcsOtc3RpY2FcbiAgaWYgKCFoYXNGZWF0dXJlQWNjZXNzKHBsYW5JZCwgZmVhdHVyZSkpIHtcbiAgICByZXR1cm4geyBhbGxvd2VkOiBmYWxzZSwgcmVhc29uOiBgQ2FyYWN0ZXLDrXN0aWNhICR7ZmVhdHVyZX0gbm8gZGlzcG9uaWJsZSBlbiAke2NvbmZpZy5uYW1lfWAgfTtcbiAgfVxuXG4gIC8vIFZlcmlmaWNhciBsw61taXRlcyBzZW1hbmFsZXMgc2kgYXBsaWNhXG4gIGlmIChsaW1pdFR5cGUpIHtcbiAgICBjb25zdCB3ZWVrbHlMaW1pdCA9IGdldFdlZWtseUxpbWl0KHBsYW5JZCwgbGltaXRUeXBlKTtcbiAgICBpZiAoIWlzVW5saW1pdGVkKHdlZWtseUxpbWl0KSAmJiBjdXJyZW50VXNhZ2UgPj0gd2Vla2x5TGltaXQpIHtcbiAgICAgIHJldHVybiB7IGFsbG93ZWQ6IGZhbHNlLCByZWFzb246IGBMw61taXRlIHNlbWFuYWwgZGUgJHtsaW1pdFR5cGV9IGFsY2FuemFkbyAoJHt3ZWVrbHlMaW1pdH0pYCB9O1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiB7IGFsbG93ZWQ6IHRydWUgfTtcbn1cblxuLy8gRnVuY2nDs24gcGFyYSB2ZXJpZmljYXIgYWNjZXNvIGRlIHVzdWFyaW8gKHBhcmEgdXNvIGVuIGZyb250ZW5kKVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGNoZWNrVXNlckZlYXR1cmVBY2Nlc3MoZmVhdHVyZTogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIHRyeSB7XG4gICAgLy8gT2J0ZW5lciBlbCBwbGFuIGRlbCB1c3VhcmlvIGRlc2RlIGxhIEFQSVxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvdXNlci9wbGFuJyk7XG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igb2J0ZW5pZW5kbyBwbGFuIGRlbCB1c3VhcmlvJyk7XG4gICAgICAvLyBTaSBoYXkgZXJyb3IsIGFzdW1pciBwbGFuIGdyYXR1aXRvXG4gICAgICByZXR1cm4gaGFzRmVhdHVyZUFjY2VzcygnZnJlZScsIGZlYXR1cmUpO1xuICAgIH1cblxuICAgIGNvbnN0IHsgcGxhbiB9ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgIGNvbnN0IHVzZXJQbGFuID0gcGxhbiB8fCAnZnJlZSc7XG5cbiAgICAvLyBVc2FyIGxhIG1pc21hIGzDs2dpY2EgcXVlIGxhIGZ1bmNpw7NuIGhhc0ZlYXR1cmVBY2Nlc3NcbiAgICByZXR1cm4gaGFzRmVhdHVyZUFjY2Vzcyh1c2VyUGxhbiwgZmVhdHVyZSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdmVyaWZpY2FuZG8gYWNjZXNvIGEgY2FyYWN0ZXLDrXN0aWNhOicsIGVycm9yKTtcbiAgICAvLyBFbiBjYXNvIGRlIGVycm9yLCBhc3VtaXIgcGxhbiBncmF0dWl0b1xuICAgIHJldHVybiBoYXNGZWF0dXJlQWNjZXNzKCdmcmVlJywgZmVhdHVyZSk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJQTEFOX0NPTkZJR1VSQVRJT05TIiwiZnJlZSIsImlkIiwibmFtZSIsInByaWNlIiwibGltaXRzIiwiZG9jdW1lbnRzIiwibWluZE1hcHNGb3JUcmlhbCIsInRlc3RzRm9yVHJpYWwiLCJmbGFzaGNhcmRzRm9yVHJpYWwiLCJ0b2tlbnNGb3JUcmlhbCIsImZlYXR1cmVzIiwicmVzdHJpY3RlZEZlYXR1cmVzIiwidXN1YXJpbyIsIm1pbmRNYXBzUGVyV2VlayIsInRlc3RzUGVyV2VlayIsImZsYXNoY2FyZHNQZXJXZWVrIiwibW9udGhseVRva2VucyIsInBybyIsImdldFBsYW5Db25maWd1cmF0aW9uIiwicGxhbklkIiwiZ2V0VG9rZW5MaW1pdEZvclBsYW4iLCJjb25maWciLCJoYXNGZWF0dXJlQWNjZXNzIiwiZmVhdHVyZSIsImluY2x1ZGVzIiwiZ2V0V2Vla2x5TGltaXQiLCJsaW1pdFR5cGUiLCJnZXRUcmlhbExpbWl0IiwiaXNVbmxpbWl0ZWQiLCJsaW1pdCIsImNhblBlcmZvcm1BY3Rpb24iLCJjdXJyZW50VXNhZ2UiLCJhbGxvd2VkIiwicmVhc29uIiwid2Vla2x5TGltaXQiLCJjaGVja1VzZXJGZWF0dXJlQWNjZXNzIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiY29uc29sZSIsImVycm9yIiwicGxhbiIsImpzb24iLCJ1c2VyUGxhbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/planLimits.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Fpre-register-paid%2Froute&page=%2Fapi%2Fauth%2Fpre-register-paid%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fpre-register-paid%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();