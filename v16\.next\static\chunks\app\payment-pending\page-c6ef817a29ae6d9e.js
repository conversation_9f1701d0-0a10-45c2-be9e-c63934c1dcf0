(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8997],{6066:(e,t,s)=>{"use strict";var r=s(98790);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},7333:(e,t,s)=>{Promise.resolve().then(s.bind(s,19398))},19398:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(33311),n=s(28295),a=s.n(n),o=s(12115),c=s(6066),i=s(95155);function l(){var e,t,s=(0,c.useRouter)(),n=(0,c.useSearchParams)(),l=(0,o.useState)(!1),u=l[0],d=l[1],m=(0,o.useState)(null),x=m[0],h=m[1],p=(0,o.useState)("pending"),f=p[0],v=p[1],b=n.get("session_id"),g=n.get("email"),j=n.get("plan");(0,o.useEffect)(function(){g&&h(g);var e,t=(e=(0,r.A)(a().mark(function e(){var t,r;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(b){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,fetch("/api/payment/status?session_id=".concat(b));case 5:return t=e.sent,e.next=8,t.json();case 8:(r=e.sent).success&&"completed"===r.status?(v("completed"),setTimeout(function(){var e="/thank-you?session_id=".concat(b).concat(j?"&plan=".concat(j):"","&payment_successful=true");s.push(e)},2e3)):r.success&&"failed"===r.status&&v("failed"),e.next=15;break;case 12:e.prev=12,e.t0=e.catch(2),console.error("Error verificando estado del pago:",e.t0);case 15:case"end":return e.stop()}},e,null,[[2,12]])})),function(){return e.apply(this,arguments)});t();var n=setInterval(t,5e3),o=setTimeout(function(){clearInterval(n)},3e5);return function(){clearInterval(n),clearTimeout(o)}},[b,j,s]);var N=(e=(0,r.A)(a().mark(function e(){var t,r,n;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(d(!0),e.prev=1,!b){e.next=10;break}return e.next=5,fetch("/api/payment/status?session_id=".concat(b));case 5:return t=e.sent,e.next=8,t.json();case 8:(r=e.sent).success&&"completed"===r.status?(v("completed"),n="/thank-you?session_id=".concat(b).concat(j?"&plan=".concat(j):"","&payment_successful=true"),s.push(n)):r.success&&"failed"===r.status&&v("failed");case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(1),console.error("Error verificando estado:",e.t0);case 15:return e.prev=15,d(!1),e.finish(15);case 18:case"end":return e.stop()}},e,null,[[1,12,15,18]])})),function(){return e.apply(this,arguments)}),y=(t=(0,r.A)(a().mark(function e(){var t,s;return a().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(x){e.next=2;break}return e.abrupt("return");case 2:return e.prev=2,e.next=5,fetch("/api/auth/resend-confirmation",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:x})});case 5:return t=e.sent,e.next=8,t.json();case 8:(s=e.sent).success?alert("Email de confirmaci\xf3n reenviado exitosamente"):alert("Error reenviando email: "+s.error),e.next=16;break;case 12:e.prev=12,e.t0=e.catch(2),console.error("Error reenviando email:",e.t0),alert("Error reenviando email");case 16:case"end":return e.stop()}},e,null,[[2,12]])})),function(){return t.apply(this,arguments)});return(0,i.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,i.jsx)("div",{className:"max-w-md mx-auto",children:(0,i.jsxs)("div",{className:"bg-white shadow rounded-lg p-6",children:["pending"===f&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"text-center mb-6",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Procesando tu Pago"}),(0,i.jsx)("p",{className:"text-gray-600",children:"Tu pago est\xe1 siendo procesado. Una vez completado, tu cuenta ser\xe1 activada autom\xe1ticamente."})]}),(0,i.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:(0,i.jsxs)("div",{className:"flex",children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:(0,i.jsx)("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,i.jsxs)("div",{className:"ml-3",children:[(0,i.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"\xbfQu\xe9 est\xe1 pasando?"}),(0,i.jsx)("div",{className:"mt-2 text-sm text-blue-700",children:(0,i.jsxs)("ul",{className:"list-disc list-inside space-y-1",children:[(0,i.jsx)("li",{children:"Tu cuenta ha sido creada"}),(0,i.jsx)("li",{children:"Estamos verificando tu pago"}),(0,i.jsx)("li",{children:"Tu cuenta ser\xe1 activada autom\xe1ticamente una vez completado"})]})})]})]})}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("button",{onClick:N,disabled:u,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:u?"Verificando...":"Verificar Estado"}),x&&(0,i.jsx)("button",{onClick:y,className:"w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",children:"Reenviar Email de Confirmaci\xf3n"})]})]}),"completed"===f&&(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4",children:(0,i.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"\xa1Pago Completado!"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Tu pago ha sido procesado exitosamente. Redirigiendo..."}),(0,i.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"})]}),"failed"===f&&(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4",children:(0,i.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Error en el Pago"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:"Hubo un problema procesando tu pago. Por favor, intenta de nuevo."}),(0,i.jsx)("button",{onClick:function(){return s.push("/upgrade-plan")},className:"w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",children:"Intentar de Nuevo"})]}),(0,i.jsx)("div",{className:"mt-6 text-center",children:(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfNecesitas ayuda? ",(0,i.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:text-blue-500",children:"Cont\xe1ctanos"})]})})]})})})}},33311:(e,t,s)=>{"use strict";function r(e,t,s,r,n,a,o){try{var c=e[a](o),i=c.value}catch(e){s(e);return}c.done?t(i):Promise.resolve(i).then(r,n)}function n(e){return function(){var t=this,s=arguments;return new Promise(function(n,a){var o=e.apply(t,s);function c(e){r(o,n,a,c,i,"next",e)}function i(e){r(o,n,a,c,i,"throw",e)}c(void 0)})}}s.d(t,{A:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,6891,7358],()=>t(7333)),_N_E=e.O()}]);