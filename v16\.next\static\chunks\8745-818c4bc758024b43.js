"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8745],{2775:(r,e,t)=>{t.d(e,{Iv:()=>A,Og:()=>y,Q1:()=>b,QU:()=>F,_W:()=>E,_p:()=>N,as:()=>T,kO:()=>w,oE:()=>m,qJ:()=>d,xq:()=>q,yK:()=>v,yf:()=>D});var n=t(10631),a=t(37711),s=t(33311),o=t(28295),c=t.n(o),i=t(55564),u=t(66430);function l(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,n)}return t}function p(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?l(Object(t),!0).forEach(function(e){(0,a.A)(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function d(r,e){return f.apply(this,arguments)}function f(){return(f=(0,s.A)(c().mark(function r(e,t){var n,a,s,o,l;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,u.iF)();case 3:if(a=r.sent.user){r.next=8;break}return console.error("No hay usuario autenticado"),r.abrupt("return",null);case 8:return r.next=10,i.N.from("colecciones_flashcards").insert([{titulo:e,descripcion:t,user_id:a.id}]).select();case 10:if(o=(s=r.sent).data,!(l=s.error)){r.next=16;break}return console.error("Error al crear colecci\xf3n de flashcards:",l),r.abrupt("return",null);case 16:return r.abrupt("return",(null==o||null==(n=o[0])?void 0:n.id)||null);case 19:return r.prev=19,r.t0=r.catch(0),console.error("Error al crear colecci\xf3n de flashcards:",r.t0),r.abrupt("return",null);case 23:case"end":return r.stop()}},r,null,[[0,19]])}))).apply(this,arguments)}function m(){return h.apply(this,arguments)}function h(){return(h=(0,s.A)(c().mark(function r(){var e,t,n,a,o,l,d;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,u.iF)();case 3:if(t=(e=r.sent).user,!(n=e.error)){r.next=9;break}return console.error("Error al obtener usuario:",n),r.abrupt("return",[]);case 9:if(t){r.next=12;break}return console.error("No hay usuario autenticado"),r.abrupt("return",[]);case 12:return r.next=14,i.N.from("colecciones_flashcards").select("*").eq("user_id",t.id).order("creado_en",{ascending:!1});case 14:if(o=(a=r.sent).data,!(l=a.error)){r.next=20;break}return console.error("Error al obtener colecciones de flashcards:",l),r.abrupt("return",[]);case 20:if(!(!o||0===o.length)){r.next=22;break}return r.abrupt("return",[]);case 22:return r.next=24,Promise.all(o.map(function(){var r=(0,s.A)(c().mark(function r(e){var t,n,a,s,o,u;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,i.N.from("flashcards").select("id").eq("coleccion_id",e.id);case 3:if(n=(t=r.sent).data,!(a=t.error)){r.next=9;break}return console.error("Error al contar flashcards para colecci\xf3n",e.id,":",a),r.abrupt("return",p(p({},e),{},{numero_flashcards:0,pendientes_hoy:0}));case 9:return r.next=11,i.N.from("flashcards").select("\n              id,\n              progreso_flashcards!inner(\n                proxima_revision,\n                estado\n              )\n            ").eq("coleccion_id",e.id).lte("progreso_flashcards.proxima_revision",new Date().toISOString());case 11:return o=(s=r.sent).data,u=s.error?0:(null==o?void 0:o.length)||0,r.abrupt("return",p(p({},e),{},{numero_flashcards:(null==n?void 0:n.length)||0,pendientes_hoy:u}));case 18:return r.prev=18,r.t0=r.catch(0),console.error("Error al procesar colecci\xf3n",e.id,":",r.t0),r.abrupt("return",p(p({},e),{},{numero_flashcards:0,pendientes_hoy:0}));case 22:case"end":return r.stop()}},r,null,[[0,18]])}));return function(e){return r.apply(this,arguments)}}()));case 24:return d=r.sent,r.abrupt("return",d);case 28:return r.prev=28,r.t0=r.catch(0),console.error("Error general al obtener colecciones de flashcards:",r.t0),r.abrupt("return",[]);case 32:case"end":return r.stop()}},r,null,[[0,28]])}))).apply(this,arguments)}function b(r){return x.apply(this,arguments)}function x(){return(x=(0,s.A)(c().mark(function r(e){var t,n,a;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,i.N.from("flashcards").select("*").eq("coleccion_id",e).order("creado_en",{ascending:!0});case 2:if(n=(t=r.sent).data,!(a=t.error)){r.next=8;break}return console.error("Error al obtener flashcards:",a),r.abrupt("return",[]);case 8:return r.abrupt("return",n||[]);case 9:case"end":return r.stop()}},r)}))).apply(this,arguments)}function v(r){return g.apply(this,arguments)}function g(){return(g=(0,s.A)(c().mark(function r(e){var t,n,a;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,i.N.from("flashcards").insert(e).select();case 2:if(n=(t=r.sent).data,!(a=t.error)){r.next=8;break}return console.error("Error al guardar flashcards:",a),r.abrupt("return",null);case 8:return r.abrupt("return",(null==n?void 0:n.map(function(r){return r.id}))||null);case 9:case"end":return r.stop()}},r)}))).apply(this,arguments)}function y(r){return _.apply(this,arguments)}function _(){return(_=(0,s.A)(c().mark(function r(e){var t,n,a,s,o,u;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,b(e);case 2:return t=r.sent,r.next=5,i.N.from("progreso_flashcards").select("*").in("flashcard_id",t.map(function(r){return r.id}));case 5:if(a=(n=r.sent).data,!(s=n.error)){r.next=11;break}return console.error("Error al obtener progreso de flashcards:",s),r.abrupt("return",[]);case 11:return u=new Date((o=new Date).getFullYear(),o.getMonth(),o.getDate()),r.abrupt("return",t.map(function(r){var e=null==a?void 0:a.find(function(e){return e.flashcard_id===r.id});if(!e)return p(p({},r),{},{debeEstudiar:!0});var t=new Date(e.proxima_revision),n=new Date(t.getFullYear(),t.getMonth(),t.getDate())<=u;return p(p({},r),{},{debeEstudiar:n,progreso:{factor_facilidad:e.factor_facilidad,intervalo:e.intervalo,repeticiones:e.repeticiones,estado:e.estado,proxima_revision:e.proxima_revision}})}));case 14:case"end":return r.stop()}},r)}))).apply(this,arguments)}function w(r){return j.apply(this,arguments)}function j(){return(j=(0,s.A)(c().mark(function r(e){var t,n,a,s,o,u,l,d,f=arguments;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return t=f.length>1&&void 0!==f[1]?f[1]:10,r.prev=1,r.next=4,y(e);case 4:return a=(n=r.sent).map(function(r){return r.id}),r.next=8,i.N.from("historial_revisiones").select("flashcard_id, dificultad").in("flashcard_id",a);case 8:if(o=(s=r.sent).data,!(u=s.error)){r.next=14;break}return console.error("Error al obtener historial de revisiones:",u),r.abrupt("return",n.slice(0,t));case 14:return l=new Map,null==o||o.forEach(function(r){var e=l.get(r.flashcard_id)||{dificil:0,total:0};e.total++,"dificil"===r.dificultad&&e.dificil++,l.set(r.flashcard_id,e)}),d=n.map(function(r){var e=l.get(r.id),t=e?e.dificil/e.total:0;return p(p({},r),{},{ratioDificultad:t})}).sort(function(r,e){return e.ratioDificultad-r.ratioDificultad}).slice(0,t),r.abrupt("return",d);case 20:return r.prev=20,r.t0=r.catch(1),console.error("Error al obtener flashcards m\xe1s dif\xedciles:",r.t0),r.abrupt("return",[]);case 24:case"end":return r.stop()}},r,null,[[1,20]])}))).apply(this,arguments)}function N(r){return k.apply(this,arguments)}function k(){return(k=(0,s.A)(c().mark(function r(e){var t,a,s,o=arguments;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return t=o.length>1&&void 0!==o[1]?o[1]:10,r.prev=1,r.next=4,y(e);case 4:return a=r.sent,s=(0,n.A)(a).sort(function(){return Math.random()-.5}).slice(0,t),r.abrupt("return",s);case 9:return r.prev=9,r.t0=r.catch(1),console.error("Error al obtener flashcards aleatorias:",r.t0),r.abrupt("return",[]);case 13:case"end":return r.stop()}},r,null,[[1,9]])}))).apply(this,arguments)}function E(r){return O.apply(this,arguments)}function O(){return(O=(0,s.A)(c().mark(function r(e){var t,n,a,s,o,u,l,d,f=arguments;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return t=f.length>1&&void 0!==f[1]?f[1]:10,r.prev=1,r.next=4,y(e);case 4:return a=(n=r.sent).map(function(r){return r.id}),r.next=8,i.N.from("historial_revisiones").select("flashcard_id, fecha").in("flashcard_id",a).order("fecha",{ascending:!1});case 8:if(o=(s=r.sent).data,!(u=s.error)){r.next=14;break}return console.error("Error al obtener \xfaltimas revisiones:",u),r.abrupt("return",n.slice(0,t));case 14:return l=new Map,null==o||o.forEach(function(r){l.has(r.flashcard_id)||l.set(r.flashcard_id,r.fecha)}),d=n.map(function(r){var e=l.get(r.id);return p(p({},r),{},{ultimaRevision:new Date(e||0)})}).sort(function(r,e){return r.ultimaRevision.getTime()-e.ultimaRevision.getTime()}).slice(0,t),r.abrupt("return",d);case 20:return r.prev=20,r.t0=r.catch(1),console.error("Error al obtener flashcards no recientes:",r.t0),r.abrupt("return",[]);case 24:case"end":return r.stop()}},r,null,[[1,20]])}))).apply(this,arguments)}function A(r,e){return S.apply(this,arguments)}function S(){return(S=(0,s.A)(c().mark(function r(e,t){var n,a,s=arguments;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return n=s.length>2&&void 0!==s[2]?s[2]:10,r.prev=1,r.next=4,y(e);case 4:return a=r.sent.filter(function(r){return r.progreso?r.progreso.estado===t:"nuevo"===t}).slice(0,n),r.abrupt("return",a);case 9:return r.prev=9,r.t0=r.catch(1),console.error("Error al obtener flashcards por estado:",r.t0),r.abrupt("return",[]);case 13:case"end":return r.stop()}},r,null,[[1,9]])}))).apply(this,arguments)}function D(r,e){return P.apply(this,arguments)}function P(){return(P=(0,s.A)(c().mark(function r(e,t){var n,a,s,o,u,l,p,d,f,m,h,b,x,v;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,n=2.5,a=1,s=0,o="nuevo",u=!1,r.next=8,i.N.from("progreso_flashcards").select("factor_facilidad, intervalo, repeticiones, estado").eq("flashcard_id",e).single();case 8:if(p=(l=r.sent).data,!l.error&&p&&(n=p.factor_facilidad||2.5,a=p.intervalo||1,s=p.repeticiones||0,o=p.estado||"nuevo",u=!0),d=n,f=a,m=s,h=o,"dificil"===t?(d=Math.max(1.3,n-.3),m=0,f=1,h="aprendiendo"):(m++,"normal"===t?d=n-.15:"facil"===t&&(d=n+.1),d=Math.max(1.3,Math.min(2.5,d)),1===m?(f=1,h="aprendiendo"):2===m?(f=6,h="repasando"):h=(f=Math.round(a*d))>30?"aprendido":"repasando"),(x=new Date(b=new Date)).setDate(x.getDate()+f),v=null,!u){r.next=29;break}return r.next=24,i.N.from("progreso_flashcards").update({factor_facilidad:d,intervalo:f,repeticiones:m,estado:h,ultima_revision:b.toISOString(),proxima_revision:x.toISOString()}).eq("flashcard_id",e);case 24:v=r.sent.error,r.next=34;break;case 29:return r.next=31,i.N.from("progreso_flashcards").insert({flashcard_id:e,factor_facilidad:d,intervalo:f,repeticiones:m,estado:h,ultima_revision:b.toISOString(),proxima_revision:x.toISOString()});case 31:v=r.sent.error;case 34:if(!v){r.next=37;break}return console.error("Error al guardar progreso:",v),r.abrupt("return",!1);case 37:return r.next=39,i.N.from("historial_revisiones").insert({flashcard_id:e,dificultad:t,factor_facilidad:d,intervalo:f,repeticiones:m,fecha:b.toISOString()});case 39:return r.sent.error,r.abrupt("return",!0);case 45:return r.prev=45,r.t0=r.catch(0),r.abrupt("return",!1);case 48:case"end":return r.stop()}},r,null,[[0,45]])}))).apply(this,arguments)}function q(r,e,t){return I.apply(this,arguments)}function I(){return(I=(0,s.A)(c().mark(function r(e,t,n){return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,i.N.from("flashcards").update({pregunta:t,respuesta:n,actualizado_en:new Date().toISOString()}).eq("id",e);case 3:if(!r.sent.error){r.next=7;break}return r.abrupt("return",!1);case 7:return r.abrupt("return",!0);case 10:return r.prev=10,r.t0=r.catch(0),r.abrupt("return",!1);case 13:case"end":return r.stop()}},r,null,[[0,10]])}))).apply(this,arguments)}function F(r){return R.apply(this,arguments)}function R(){return(R=(0,s.A)(c().mark(function r(e){var t,n,a;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,i.N.from("progreso_flashcards").delete().eq("flashcard_id",e);case 3:if(!r.sent.error){r.next=7;break}return r.abrupt("return",!1);case 7:return r.next=9,i.N.from("historial_revisiones").delete().eq("flashcard_id",e);case 9:if(!r.sent.error){r.next=13;break}return r.abrupt("return",!1);case 13:return r.next=15,i.N.from("flashcards").delete({count:"exact"}).eq("id",e);case 15:if(n=(t=r.sent).error,a=t.count,!n){r.next=20;break}return r.abrupt("return",!1);case 20:if(0!==a){r.next=22;break}return r.abrupt("return",!1);case 22:return r.abrupt("return",!0);case 25:return r.prev=25,r.t0=r.catch(0),r.abrupt("return",!1);case 28:case"end":return r.stop()}},r,null,[[0,25]])}))).apply(this,arguments)}function T(r){return z.apply(this,arguments)}function z(){return(z=(0,s.A)(c().mark(function r(e){var t,n,a,s,o,l,p;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,u.iF)();case 3:if(t=r.sent.user){r.next=7;break}return r.abrupt("return",!1);case 7:return r.next=9,i.N.from("flashcards").select("id").eq("coleccion_id",e);case 9:if(a=(n=r.sent).data,!n.error){r.next=14;break}return r.abrupt("return",!1);case 14:if(!((s=(null==a?void 0:a.map(function(r){return r.id}))||[]).length>0)){r.next=34;break}return r.next=18,i.N.from("progreso_flashcards").delete().in("flashcard_id",s);case 18:if(!r.sent.error){r.next=22;break}return r.abrupt("return",!1);case 22:return r.next=24,i.N.from("historial_revisiones").delete().in("flashcard_id",s);case 24:if(!r.sent.error){r.next=28;break}return r.abrupt("return",!1);case 28:return r.next=30,i.N.from("flashcards").delete().eq("coleccion_id",e);case 30:if(!r.sent.error){r.next=34;break}return r.abrupt("return",!1);case 34:return r.next=36,i.N.from("colecciones_flashcards").delete({count:"exact"}).eq("id",e).eq("user_id",t.id);case 36:if(l=(o=r.sent).error,p=o.count,!l){r.next=41;break}return r.abrupt("return",!1);case 41:if(0!==p){r.next=43;break}return r.abrupt("return",!1);case 43:return r.abrupt("return",!0);case 46:return r.prev=46,r.t0=r.catch(0),r.abrupt("return",!1);case 49:case"end":return r.stop()}},r,null,[[0,46]])}))).apply(this,arguments)}},5929:(r,e,t)=>{t.d(e,{wU:()=>l,yV:()=>i});var n=t(33311),a=t(28295),s=t.n(a),o=t(55564),c=t(2775);function i(r){return u.apply(this,arguments)}function u(){return(u=(0,n.A)(s().mark(function r(e){var t,n,a,i,u,l;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,(0,c.Q1)(e);case 2:if(0!==(t=r.sent).length){r.next=5;break}return r.abrupt("return",{total:0,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0});case 5:return r.next=7,o.N.from("progreso_flashcards").select("*").in("flashcard_id",t.map(function(r){return r.id}));case 7:if(a=(n=r.sent).data,!(i=n.error)){r.next=13;break}return console.error("Error al obtener progreso de flashcards:",i),r.abrupt("return",{total:t.length,nuevas:t.length,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:t.length});case 13:return u={total:t.length,nuevas:0,aprendiendo:0,repasando:0,aprendidas:0,paraHoy:0},l=new Date,t.forEach(function(r){var e=null==a?void 0:a.find(function(e){return e.flashcard_id===r.id});if(e){switch(e.estado){case"nuevo":u.nuevas++;break;case"aprendiendo":u.aprendiendo++;break;case"repasando":u.repasando++;break;case"aprendido":u.aprendidas++}var t=new Date(e.proxima_revision);new Date(t.getFullYear(),t.getMonth(),t.getDate())<=new Date(l.getFullYear(),l.getMonth(),l.getDate())&&u.paraHoy++}else u.nuevas++,u.paraHoy++}),r.abrupt("return",u);case 17:case"end":return r.stop()}},r)}))).apply(this,arguments)}function l(r){return p.apply(this,arguments)}function p(){return(p=(0,n.A)(s().mark(function r(e){var t,n,a,i,u,l,p,d,f,m;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,c.Q1)(e);case 3:if(0!==(t=r.sent).length){r.next=6;break}return r.abrupt("return",{totalSesiones:0,totalRevisiones:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]});case 6:return n=t.map(function(r){return r.id}),r.next=9,o.N.from("historial_revisiones").select("*").in("flashcard_id",n).order("fecha",{ascending:!0});case 9:if(i=(a=r.sent).data,!(u=a.error)){r.next=15;break}return console.error("Error al obtener revisiones:",u),r.abrupt("return",null);case 15:return r.next=17,o.N.from("progreso_flashcards").select("*").in("flashcard_id",n);case 17:return(l=r.sent).data,(p=l.error)&&console.error("Error al obtener progreso:",p),d={totalSesiones:0,totalRevisiones:i?i.length:0,distribucionDificultad:{dificil:0,normal:0,facil:0},progresoTiempo:[],tarjetasMasDificiles:[]},i&&i.length>0&&(i.forEach(function(r){"dificil"===r.dificultad?d.distribucionDificultad.dificil++:"normal"===r.dificultad?d.distribucionDificultad.normal++:"facil"===r.dificultad&&d.distribucionDificultad.facil++}),f=new Set,i.forEach(function(r){var e=new Date(r.fecha).toISOString().split("T")[0];f.add(e)}),d.totalSesiones=f.size,m=new Map,t.forEach(function(r){m.set(r.id,{dificil:0,normal:0,facil:0,total:0})}),i.forEach(function(r){var e=m.get(r.flashcard_id);e&&("dificil"===r.dificultad?e.dificil++:"normal"===r.dificultad?e.normal++:"facil"===r.dificultad&&e.facil++,e.total++)}),d.tarjetasMasDificiles=t.map(function(r){var e=m.get(r.id)||{dificil:0,normal:0,facil:0,total:0};return{id:r.id,pregunta:r.pregunta,dificil:e.dificil,normal:e.normal,facil:e.facil,totalRevisiones:e.total}}).filter(function(r){return r.totalRevisiones>0}).sort(function(r,e){var t=r.totalRevisiones>0?r.dificil/r.totalRevisiones:0;return(e.totalRevisiones>0?e.dificil/e.totalRevisiones:0)-t}).slice(0,10)),r.abrupt("return",d);case 26:return r.prev=26,r.t0=r.catch(0),console.error("Error al calcular estad\xedsticas detalladas:",r.t0),r.abrupt("return",null);case 30:case"end":return r.stop()}},r,null,[[0,26]])}))).apply(this,arguments)}},11555:(r,e,t)=>{t.d(e,{IE:()=>c,qk:()=>u,qo:()=>o});var n=t(33311),a=t(28295),s=t.n(a),o={free:{id:"free",name:"Plan Gratis",price:0,limits:{documents:1,mindMapsForTrial:2,testsForTrial:10,flashcardsForTrial:10,tokensForTrial:5e4,features:["document_upload","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","ai_tutor_chat","summary_a1_a2"]},usuario:{id:"usuario",name:"Plan Usuario",price:1e3,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"]},features:["document_upload","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation"],restrictedFeatures:["study_planning","summary_a1_a2"]},pro:{id:"pro",name:"Plan Pro",price:1500,limits:{documents:-1,mindMapsPerWeek:-1,testsPerWeek:-1,flashcardsPerWeek:-1,monthlyTokens:1e6,features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"]},features:["document_upload","study_planning","ai_tutor_chat","test_generation","flashcard_generation","mind_map_generation","summary_a1_a2"],restrictedFeatures:[]}};function c(r){return o[r]||null}function i(r,e){var t=c(r);return!(!t||t.restrictedFeatures.includes(e))&&t.features.includes(e)}function u(r){return l.apply(this,arguments)}function l(){return(l=(0,n.A)(s().mark(function r(e){var t,n;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,fetch("/api/user/plan");case 3:if((t=r.sent).ok){r.next=7;break}return console.error("Error obteniendo plan del usuario"),r.abrupt("return",i("free",e));case 7:return r.next=9,t.json();case 9:return n=r.sent.plan||"free",r.abrupt("return",i(n,e));case 15:return r.prev=15,r.t0=r.catch(0),console.error("Error verificando acceso a caracter\xedstica:",r.t0),r.abrupt("return",i("free",e));case 19:case"end":return r.stop()}},r,null,[[0,15]])}))).apply(this,arguments)}},17863:(r,e,t)=>{t.d(e,{$S:()=>l,d7:()=>d,fF:()=>i});var n=t(33311),a=t(28295),s=t.n(a),o=t(66618),c=t(57759);function i(r){return u.apply(this,arguments)}function u(){return(u=(0,n.A)(s().mark(function r(e){var t,n,a,i,u,l,p,d,f;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,c.iF)();case 3:if(n=(t=r.sent).user,a=t.error,!(!n||a)){r.next=8;break}return r.abrupt("return",null);case 8:return r.next=10,o.N.from("temarios").select("id").eq("id",e).eq("user_id",n.id).single();case 10:if(u=(i=r.sent).data,!(l=i.error)){r.next=19;break}if("PGRST116"!==l.code){r.next=17;break}return console.log("Temario no encontrado:",e),r.abrupt("return",null);case 17:return console.error("Error al verificar temario:",l),r.abrupt("return",null);case 19:if(u){r.next=22;break}return console.log("Temario no encontrado:",e),r.abrupt("return",null);case 22:return r.next=24,o.N.from("planes_estudios").select("*").eq("user_id",n.id).eq("temario_id",e).eq("activo",!0).single();case 24:if(d=(p=r.sent).data,!(f=p.error)){r.next=32;break}if("PGRST116"!==f.code){r.next=30;break}return r.abrupt("return",null);case 30:return console.error("Error al obtener plan activo:",f),r.abrupt("return",null);case 32:return r.abrupt("return",d);case 35:return r.prev=35,r.t0=r.catch(0),console.error("Error al obtener plan activo:",r.t0),r.abrupt("return",null);case 39:case"end":return r.stop()}},r,null,[[0,35]])}))).apply(this,arguments)}function l(r){return p.apply(this,arguments)}function p(){return(p=(0,n.A)(s().mark(function r(e){var t,n,a,i,u,l;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,c.iF)();case 3:if(n=(t=r.sent).user,a=t.error,!(!n||a)){r.next=8;break}return r.abrupt("return",[]);case 8:return r.next=10,o.N.from("progreso_plan_estudios").select("*").eq("plan_id",e).eq("user_id",n.id).order("semana_numero",{ascending:!0}).order("creado_en",{ascending:!0});case 10:if(u=(i=r.sent).data,!(l=i.error)){r.next=16;break}return console.error("Error al obtener progreso del plan:",l),r.abrupt("return",[]);case 16:return r.abrupt("return",u||[]);case 19:return r.prev=19,r.t0=r.catch(0),console.error("Error al obtener progreso del plan:",r.t0),r.abrupt("return",[]);case 23:case"end":return r.stop()}},r,null,[[0,19]])}))).apply(this,arguments)}function d(r,e,t,n,a,s,o,c,i){return f.apply(this,arguments)}function f(){return(f=(0,n.A)(s().mark(function r(e,t,n,a,i,u,l,p,d){var f,m,h,b,x,v;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,c.iF)();case 3:if(m=(f=r.sent).user,h=f.error,!(!m||h)){r.next=8;break}return r.abrupt("return",!1);case 8:return r.next=10,o.N.from("progreso_plan_estudios").select("id").eq("plan_id",e).eq("user_id",m.id).eq("semana_numero",t).eq("dia_nombre",n).eq("tarea_titulo",a).single();case 10:if(!(b=r.sent.data)){r.next=22;break}return r.next=15,o.N.from("progreso_plan_estudios").update({completado:u,fecha_completado:u?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:p,calificacion:d,actualizado_en:new Date().toISOString()}).eq("id",b.id);case 15:if(!(x=r.sent.error)){r.next=20;break}return console.error("Error al actualizar progreso:",x),r.abrupt("return",!1);case 20:r.next=29;break;case 22:return r.next=24,o.N.from("progreso_plan_estudios").insert([{plan_id:e,user_id:m.id,semana_numero:t,dia_nombre:n,tarea_titulo:a,tarea_tipo:i,completado:u,fecha_completado:u?new Date().toISOString():null,tiempo_real_minutos:l,notas_progreso:p,calificacion:d}]);case 24:if(!(v=r.sent.error)){r.next=29;break}return console.error("Error al crear progreso:",v),r.abrupt("return",!1);case 29:return r.abrupt("return",!0);case 32:return r.prev=32,r.t0=r.catch(0),console.error("Error al guardar progreso de tarea:",r.t0),r.abrupt("return",!1);case 36:case"end":return r.stop()}},r,null,[[0,32]])}))).apply(this,arguments)}},25519:(r,e,t)=>{t.d(e,{Pk:()=>h,u9:()=>f,vD:()=>p});var n=t(37711),a=t(33311),s=t(28295),o=t.n(s),c=t(66618),i=t(57759);function u(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,n)}return t}function l(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?u(Object(t),!0).forEach(function(e){(0,n.A)(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function p(r){return d.apply(this,arguments)}function d(){return(d=(0,a.A)(o().mark(function r(e){var t,n,a,s,u,l,p,d,f;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(n=(t=r.sent).user,a=t.error,!(!n||a)){r.next=9;break}return console.log("No hay usuario autenticado o error:",a),r.abrupt("return",!1);case 9:return r.next=11,c.N.from("temarios").select("id").eq("id",e).eq("user_id",n.id).single();case 11:if(u=(s=r.sent).data,!(l=s.error)){r.next=19;break}if("PGRST116"!==l.code){r.next=17;break}return r.abrupt("return",!1);case 17:return console.error("Error al verificar temario:",l),r.abrupt("return",!1);case 19:if(u){r.next=21;break}return r.abrupt("return",!1);case 21:return r.next=23,c.N.from("planificacion_usuario").select("id").eq("user_id",n.id).eq("temario_id",e).eq("completado",!0).limit(1);case 23:if(d=(p=r.sent).data,!(f=p.error)){r.next=29;break}return console.error("Error al verificar planificaci\xf3n:",f),r.abrupt("return",!1);case 29:return r.abrupt("return",d&&d.length>0);case 32:return r.prev=32,r.t0=r.catch(0),console.error("Error al verificar planificaci\xf3n:",r.t0),r.abrupt("return",!1);case 36:case"end":return r.stop()}},r,null,[[0,32]])}))).apply(this,arguments)}function f(r){return m.apply(this,arguments)}function m(){return(m=(0,a.A)(o().mark(function r(e){var t,n,a,s,u,l;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(n=(t=r.sent).user,a=t.error,!(!n||a)){r.next=8;break}return r.abrupt("return",null);case 8:return r.next=10,c.N.from("planificacion_usuario").select("*").eq("user_id",n.id).eq("temario_id",e).single();case 10:if(u=(s=r.sent).data,!(l=s.error)){r.next=18;break}if("PGRST116"!==l.code){r.next=16;break}return r.abrupt("return",null);case 16:return console.error("Error al obtener planificaci\xf3n:",l),r.abrupt("return",null);case 18:return r.abrupt("return",u);case 21:return r.prev=21,r.t0=r.catch(0),console.error("Error al obtener planificaci\xf3n:",r.t0),r.abrupt("return",null);case 25:case"end":return r.stop()}},r,null,[[0,21]])}))).apply(this,arguments)}function h(r,e){return b.apply(this,arguments)}function b(){return(b=(0,a.A)(o().mark(function r(e,t){var n,a,s,u,p,d,m,h,b,x;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(a=(n=r.sent).user,s=n.error,!(!a||s)){r.next=9;break}return console.error("No hay usuario autenticado"),r.abrupt("return",null);case 9:return r.next=11,f(e);case 11:if(!(u=r.sent)){r.next=24;break}return r.next=15,c.N.from("planificacion_usuario").update(l(l({},t),{},{completado:!0,actualizado_en:new Date().toISOString()})).eq("id",u.id).select().single();case 15:if(d=(p=r.sent).data,!(m=p.error)){r.next=21;break}return console.error("Error al actualizar planificaci\xf3n:",m),r.abrupt("return",null);case 21:return r.abrupt("return",d.id);case 24:return r.next=26,c.N.from("planificacion_usuario").insert([l(l({user_id:a.id,temario_id:e},t),{},{completado:!0})]).select().single();case 26:if(b=(h=r.sent).data,!(x=h.error)){r.next=32;break}return console.error("Error al crear planificaci\xf3n:",x),r.abrupt("return",null);case 32:return r.abrupt("return",b.id);case 33:r.next=39;break;case 35:return r.prev=35,r.t0=r.catch(0),console.error("Error al guardar planificaci\xf3n:",r.t0),r.abrupt("return",null);case 39:case"end":return r.stop()}},r,null,[[0,35]])}))).apply(this,arguments)}},33821:(r,e,t)=>{t.d(e,{B$:()=>k,Il:()=>S,Se:()=>_,cN:()=>O,cm:()=>x,jg:()=>f,oS:()=>j,r5:()=>h,sW:()=>g,xv:()=>P,yr:()=>p});var n=t(37711),a=t(33311),s=t(28295),o=t.n(s),c=t(66618),i=t(57759);function u(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,n)}return t}function l(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?u(Object(t),!0).forEach(function(e){(0,n.A)(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function p(){return d.apply(this,arguments)}function d(){return(d=(0,a.A)(o().mark(function r(){var e,t,n,a,s,u,l,p,d;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(t=(e=r.sent).user,n=e.error,!(!t||n)){r.next=8;break}return r.abrupt("return",!1);case 8:return r.next=10,c.N.from("temarios").select("id").eq("user_id",t.id).limit(1);case 10:if(s=(a=r.sent).data,!(u=a.error)){r.next=18;break}if(!("PGRST116"===u.code||null!=(l=u.message)&&l.includes("relation")||null!=(p=u.message)&&p.includes("does not exist"))){r.next=16;break}return r.abrupt("return",!1);case 16:return console.error("Error al verificar temario en Supabase:",u),r.abrupt("return",!1);case 18:return d=s&&s.length>0,r.abrupt("return",d);case 22:return r.prev=22,r.t0=r.catch(0),console.error("Error general al verificar temario:",r.t0),r.abrupt("return",!1);case 26:case"end":return r.stop()}},r,null,[[0,22]])}))).apply(this,arguments)}function f(){return m.apply(this,arguments)}function m(){return(m=(0,a.A)(o().mark(function r(){var e,t,n,a,s,u;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(t=(e=r.sent).user,n=e.error,!(!t||n)){r.next=8;break}return r.abrupt("return",null);case 8:return r.next=10,c.N.from("temarios").select("*").eq("user_id",t.id).single();case 10:if(s=(a=r.sent).data,!(u=a.error)){r.next=18;break}if("PGRST116"!==u.code){r.next=16;break}return r.abrupt("return",null);case 16:return console.error("Error al obtener temario en Supabase:",u),r.abrupt("return",null);case 18:return r.abrupt("return",s);case 21:return r.prev=21,r.t0=r.catch(0),console.error("Error general al obtener temario:",r.t0),r.abrupt("return",null);case 25:case"end":return r.stop()}},r,null,[[0,21]])}))).apply(this,arguments)}function h(r,e,t){return b.apply(this,arguments)}function b(){return(b=(0,a.A)(o().mark(function r(e,t,n){var a,s,u,l,p,d;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(s=(a=r.sent).user,u=a.error,!(!s||u)){r.next=9;break}return console.error("No hay usuario autenticado o error:",u),r.abrupt("return",null);case 9:return r.next=11,c.N.from("temarios").insert([{titulo:e,descripcion:t,tipo:n,user_id:s.id}]).select().single();case 11:if(p=(l=r.sent).data,!(d=l.error)){r.next=17;break}return console.error("Error al crear temario:",d),r.abrupt("return",null);case 17:return r.abrupt("return",p.id);case 20:return r.prev=20,r.t0=r.catch(0),console.error("Error al crear temario:",r.t0),r.abrupt("return",null);case 24:case"end":return r.stop()}},r,null,[[0,20]])}))).apply(this,arguments)}function x(r){return v.apply(this,arguments)}function v(){return(v=(0,a.A)(o().mark(function r(e){var t,n,a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,c.N.from("temas").select("*").eq("temario_id",e).order("orden",{ascending:!0});case 3:if(n=(t=r.sent).data,!(a=t.error)){r.next=9;break}return console.error("Error al obtener temas:",a),r.abrupt("return",[]);case 9:return r.abrupt("return",n||[]);case 12:return r.prev=12,r.t0=r.catch(0),console.error("Error al obtener temas:",r.t0),r.abrupt("return",[]);case 16:case"end":return r.stop()}},r,null,[[0,12]])}))).apply(this,arguments)}function g(r,e){return y.apply(this,arguments)}function y(){return(y=(0,a.A)(o().mark(function r(e,t){var n,a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,n=t.map(function(r){return l(l({},r),{},{temario_id:e})}),r.next=4,c.N.from("temas").insert(n);case 4:if(!(a=r.sent.error)){r.next=9;break}return console.error("Error al crear temas:",a),r.abrupt("return",!1);case 9:return r.abrupt("return",!0);case 12:return r.prev=12,r.t0=r.catch(0),console.error("Error al crear temas:",r.t0),r.abrupt("return",!1);case 16:case"end":return r.stop()}},r,null,[[0,12]])}))).apply(this,arguments)}function _(r,e,t){return w.apply(this,arguments)}function w(){return(w=(0,a.A)(o().mark(function r(e,t,n){var a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,c.N.from("temarios").update({titulo:t,descripcion:n,actualizado_en:new Date().toISOString()}).eq("id",e);case 3:if(!(a=r.sent.error)){r.next=8;break}return console.error("Error al actualizar temario:",a),r.abrupt("return",!1);case 8:return r.abrupt("return",!0);case 11:return r.prev=11,r.t0=r.catch(0),console.error("Error al actualizar temario:",r.t0),r.abrupt("return",!1);case 15:case"end":return r.stop()}},r,null,[[0,11]])}))).apply(this,arguments)}function j(r,e,t){return N.apply(this,arguments)}function N(){return(N=(0,a.A)(o().mark(function r(e,t,n){var a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,c.N.from("temas").update({titulo:t,descripcion:n,actualizado_en:new Date().toISOString()}).eq("id",e);case 3:if(!(a=r.sent.error)){r.next=8;break}return console.error("Error al actualizar tema:",a),r.abrupt("return",!1);case 8:return r.abrupt("return",!0);case 11:return r.prev=11,r.t0=r.catch(0),console.error("Error al actualizar tema:",r.t0),r.abrupt("return",!1);case 15:case"end":return r.stop()}},r,null,[[0,11]])}))).apply(this,arguments)}function k(r){return E.apply(this,arguments)}function E(){return(E=(0,a.A)(o().mark(function r(e){var t;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,c.N.from("temas").delete().eq("id",e);case 3:if(!(t=r.sent.error)){r.next=8;break}return console.error("Error al eliminar tema:",t),r.abrupt("return",!1);case 8:return r.abrupt("return",!0);case 11:return r.prev=11,r.t0=r.catch(0),console.error("Error al eliminar tema:",r.t0),r.abrupt("return",!1);case 15:case"end":return r.stop()}},r,null,[[0,11]])}))).apply(this,arguments)}function O(r,e){return A.apply(this,arguments)}function A(){return(A=(0,a.A)(o().mark(function r(e,t){var n,a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,n={completado:t,actualizado_en:new Date().toISOString()},t?n.fecha_completado=new Date().toISOString():n.fecha_completado=null,r.next=5,c.N.from("temas").update(n).eq("id",e);case 5:if(!(a=r.sent.error)){r.next=10;break}return console.error("Error al actualizar estado del tema:",a),r.abrupt("return",!1);case 10:return r.abrupt("return",!0);case 13:return r.prev=13,r.t0=r.catch(0),console.error("Error al actualizar estado del tema:",r.t0),r.abrupt("return",!1);case 17:case"end":return r.stop()}},r,null,[[0,13]])}))).apply(this,arguments)}function S(r){return D.apply(this,arguments)}function D(){return(D=(0,a.A)(o().mark(function r(e){var t,n,a,s,i,u;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,c.N.from("temas").select("completado").eq("temario_id",e);case 3:if(n=(t=r.sent).data,!(a=t.error)){r.next=9;break}return console.error("Error al obtener estad\xedsticas del temario:",a),r.abrupt("return",null);case 9:return s=n.length,i=n.filter(function(r){return r.completado}).length,u=s>0?i/s*100:0,r.abrupt("return",{totalTemas:s,temasCompletados:i,porcentajeCompletado:u});case 15:return r.prev=15,r.t0=r.catch(0),console.error("Error al obtener estad\xedsticas del temario:",r.t0),r.abrupt("return",null);case 19:case"end":return r.stop()}},r,null,[[0,15]])}))).apply(this,arguments)}function P(r){return q.apply(this,arguments)}function q(){return(q=(0,a.A)(o().mark(function r(e){var t;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,c.N.from("temarios").delete().eq("id",e);case 3:if(!(t=r.sent.error)){r.next=8;break}return console.error("Error al eliminar temario:",t),r.abrupt("return",!1);case 8:return r.abrupt("return",!0);case 11:return r.prev=11,r.t0=r.catch(0),console.error("Error al eliminar temario:",r.t0),r.abrupt("return",!1);case 15:case"end":return r.stop()}},r,null,[[0,11]])}))).apply(this,arguments)}},47493:(r,e,t)=>{t.d(e,{Gl:()=>g,Kj:()=>h,Lx:()=>p,OA:()=>x,_4:()=>u,dd:()=>j,hg:()=>f,oC:()=>_});var n=t(3243),a=t(33311),s=t(28295),o=t.n(s),c=t(55564),i=t(66430);function u(r,e,t){return l.apply(this,arguments)}function l(){return(l=(0,a.A)(o().mark(function r(e,t,n){var a,s,u,l,p,d;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,console.log("\uD83D\uDCDD Creando nuevo test:",e),r.next=4,(0,i.iF)();case 4:if(u=r.sent.user){r.next=9;break}return console.error("❌ No hay usuario autenticado para crear test"),r.abrupt("return",null);case 9:return console.log("\uD83D\uDC64 Usuario autenticado:",u.id),r.next=12,c.N.from("tests").insert([{titulo:e,descripcion:t,documentos_ids:n,user_id:u.id}]).select();case 12:if(p=(l=r.sent).data,!(d=l.error)){r.next=18;break}return console.error("❌ Error al crear test:",d),r.abrupt("return",null);case 18:return console.log("✅ Test creado exitosamente:",null==p||null==(a=p[0])?void 0:a.id),r.abrupt("return",(null==p||null==(s=p[0])?void 0:s.id)||null);case 22:return r.prev=22,r.t0=r.catch(0),console.error("\uD83D\uDCA5 Error inesperado al crear test:",r.t0),r.abrupt("return",null);case 26:case"end":return r.stop()}},r,null,[[0,22]])}))).apply(this,arguments)}function p(){return d.apply(this,arguments)}function d(){return(d=(0,a.A)(o().mark(function r(){var e,t,n,a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(e=r.sent.user){r.next=8;break}return console.error("No hay usuario autenticado"),r.abrupt("return",[]);case 8:return r.next=10,c.N.from("tests").select("*").eq("user_id",e.id).order("creado_en",{ascending:!1});case 10:if(n=(t=r.sent).data,!(a=t.error)){r.next=16;break}return console.error("Error al obtener tests:",a),r.abrupt("return",[]);case 16:return r.abrupt("return",n||[]);case 19:return r.prev=19,r.t0=r.catch(0),console.error("Error al obtener tests:",r.t0),r.abrupt("return",[]);case 23:case"end":return r.stop()}},r,null,[[0,19]])}))).apply(this,arguments)}function f(r){return m.apply(this,arguments)}function m(){return(m=(0,a.A)(o().mark(function r(e){var t,n,a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,c.N.from("preguntas_test").select("*").eq("test_id",e);case 2:if(n=(t=r.sent).data,!(a=t.error)){r.next=8;break}return console.error("Error al obtener preguntas de test:",a),r.abrupt("return",[]);case 8:return r.abrupt("return",n||[]);case 9:case"end":return r.stop()}},r)}))).apply(this,arguments)}function h(r){return b.apply(this,arguments)}function b(){return(b=(0,a.A)(o().mark(function r(e){var t,n,a;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,c.N.from("preguntas_test").select("*",{count:"exact",head:!0}).eq("test_id",e);case 2:if(n=(t=r.sent).count,!(a=t.error)){r.next=8;break}return console.error("Error al obtener conteo de preguntas:",a),r.abrupt("return",0);case 8:return r.abrupt("return",n||0);case 9:case"end":return r.stop()}},r)}))).apply(this,arguments)}function x(r){return v.apply(this,arguments)}function v(){return(v=(0,a.A)(o().mark(function r(e){var t;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,c.N.from("preguntas_test").insert(e);case 2:if(!(t=r.sent.error)){r.next=7;break}return console.error("Error al guardar preguntas de test:",t),r.abrupt("return",!1);case 7:return r.abrupt("return",!0);case 8:case"end":return r.stop()}},r)}))).apply(this,arguments)}function g(r,e,t,n){return y.apply(this,arguments)}function y(){return(y=(0,a.A)(o().mark(function r(e,t,n,a){var s;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,c.N.from("estadisticas_test").insert([{test_id:e,pregunta_id:t,respuesta_seleccionada:n,es_correcta:a,fecha_respuesta:new Date().toISOString()}]);case 2:if(!(s=r.sent.error)){r.next=7;break}return console.error("Error al registrar respuesta de test:",s),r.abrupt("return",!1);case 7:return r.abrupt("return",!0);case 8:case"end":return r.stop()}},r)}))).apply(this,arguments)}function _(){return w.apply(this,arguments)}function w(){return(w=(0,a.A)(o().mark(function r(){var e,t,n,a,s,i,u,l;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,c.N.from("estadisticas_test").select("*");case 2:if(t=(e=r.sent).data,!(n=e.error)){r.next=8;break}return console.error("Error al obtener estad\xedsticas de tests:",n),r.abrupt("return",{totalTests:0,totalPreguntas:0,totalRespuestasCorrectas:0,totalRespuestasIncorrectas:0,porcentajeAcierto:0});case 8:return a=new Set((null==t?void 0:t.map(function(r){return r.test_id}))||[]),s=new Set((null==t?void 0:t.map(function(r){return r.pregunta_id}))||[]),i=(null==t?void 0:t.filter(function(r){return r.es_correcta}).length)||0,u=((null==t?void 0:t.length)||0)-i,l=t&&t.length>0?Math.round(i/t.length*100):0,r.abrupt("return",{totalTests:a.size,totalPreguntas:s.size,totalRespuestasCorrectas:i,totalRespuestasIncorrectas:u,porcentajeAcierto:l});case 14:case"end":return r.stop()}},r)}))).apply(this,arguments)}function j(r){return N.apply(this,arguments)}function N(){return(N=(0,a.A)(o().mark(function r(e){var t,a,s,i,u,l,p,d,f,m,h;return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,c.N.from("estadisticas_test").select("*").eq("test_id",e);case 2:if(a=(t=r.sent).data,!(s=t.error)){r.next=8;break}return console.error("Error al obtener estad\xedsticas del test:",s),r.abrupt("return",{testId:e,totalPreguntas:0,totalCorrectas:0,totalIncorrectas:0,porcentajeAcierto:0,fechasRealizacion:[],preguntasMasFalladas:[]});case 8:return r.next=10,c.N.from("preguntas_test").select("*").eq("test_id",e);case 10:return i=r.sent.data,u=(null==a?void 0:a.filter(function(r){return r.es_correcta}).length)||0,l=((null==a?void 0:a.length)||0)-u,p=a&&a.length>0?Math.round(u/a.length*100):0,d=new Set,null==a||a.forEach(function(r){var e=new Date(r.fecha_respuesta);d.add("".concat(e.getDate(),"/").concat(e.getMonth()+1,"/").concat(e.getFullYear()))}),f=Array.from(d),m=new Map,null==a||a.forEach(function(r){var e=m.get(r.pregunta_id)||{fallos:0,aciertos:0};r.es_correcta?e.aciertos++:e.fallos++,m.set(r.pregunta_id,e)}),h=Array.from(m.entries()).map(function(r){var e,t=(0,n.A)(r,2),a=t[0],s=t[1];return{preguntaId:a,totalFallos:s.fallos,totalAciertos:s.aciertos,pregunta:(null==i||null==(e=i.find(function(r){return r.id===a}))?void 0:e.pregunta)||"Desconocida"}}).sort(function(r,e){return e.totalFallos-r.totalFallos}).slice(0,5),r.abrupt("return",{testId:e,totalPreguntas:(null==i?void 0:i.length)||0,totalCorrectas:u,totalIncorrectas:l,porcentajeAcierto:p,fechasRealizacion:f,preguntasMasFalladas:h});case 22:case"end":return r.stop()}},r)}))).apply(this,arguments)}},52750:(r,e,t)=>{t.d(e,{A:()=>c}),t(12115);var n=t(73329),a=t.n(n),s=t(75780),o=t(95155);function c(r){var e=r.feature,t=r.featureDescription,n=r.benefits,c=void 0===n?[]:n,i=r.className,u=c.length>0?c:["Acceso ilimitado a todas las funcionalidades","Generaci\xf3n de contenido sin l\xedmites","Soporte prioritario","Nuevas funcionalidades en primicia"],l={ai_tutor_chat:{name:"Chat con IA",description:"Conversa con tu preparador personal de IA para resolver dudas y obtener explicaciones detalladas."},study_planning:{name:"Planificaci\xf3n de Estudios",description:"Crea planes de estudio personalizados con IA que se adaptan a tu ritmo y objetivos."},summary_a1_a2:{name:"Res\xfamenes Avanzados",description:"Genera res\xfamenes inteligentes y estructurados de tus documentos de estudio."}}[e]||{name:e,description:t||"Esta funcionalidad avanzada te ayudar\xe1 a mejorar tu preparaci\xf3n."};return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4 ".concat(void 0===i?"":i),children:(0,o.jsx)("div",{className:"max-w-2xl w-full",children:(0,o.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl overflow-hidden",children:[(0,o.jsxs)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-12 text-center text-white",children:[(0,o.jsx)("div",{className:"w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-6",children:(0,o.jsx)(s.F5$,{className:"w-10 h-10"})}),(0,o.jsx)("h1",{className:"text-3xl font-bold mb-4",children:l.name}),(0,o.jsx)("p",{className:"text-blue-100 text-lg leading-relaxed",children:l.description})]}),(0,o.jsxs)("div",{className:"px-8 py-8",children:[(0,o.jsxs)("div",{className:"text-center mb-8",children:[(0,o.jsxs)("div",{className:"inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium mb-6",children:[(0,o.jsx)(s.usP,{className:"w-4 h-4 mr-2"}),"Funcionalidad Premium"]}),(0,o.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-4",children:"Actualiza tu plan para acceder"}),(0,o.jsx)("p",{className:"text-gray-600 text-lg",children:"Esta funcionalidad est\xe1 disponible para usuarios con planes de pago. Actualiza ahora y desbloquea todo el potencial de OposiAI."})]}),(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 text-center",children:"\xbfQu\xe9 obtienes al actualizar?"}),(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map(function(r,e){return(0,o.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,o.jsx)("div",{className:"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",children:(0,o.jsx)(s.YrT,{className:"w-4 h-4 text-green-600"})}),(0,o.jsx)("span",{className:"text-gray-700",children:r})]},e)})})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,o.jsxs)(a(),{href:"/upgrade-plan",className:"inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,o.jsx)(s.ei4,{className:"w-5 h-5 mr-2"}),"Actualizar Plan"]}),(0,o.jsx)(a(),{href:"/app",className:"inline-flex items-center justify-center px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:border-gray-400 hover:bg-gray-50 transition-colors",children:"Volver al Dashboard"})]}),(0,o.jsx)("div",{className:"mt-8 text-center",children:(0,o.jsxs)("p",{className:"text-sm text-gray-500",children:["\xbfTienes preguntas? ",(0,o.jsx)("a",{href:"mailto:<EMAIL>",className:"text-blue-600 hover:underline",children:"Cont\xe1ctanos"})]})})]})]})})})}},57759:(r,e,t)=>{t.d(e,{iF:()=>i});var n=t(33311),a=t(28295),s=t.n(a),o=t(66618);function c(){return(c=_asyncToGenerator(_regeneratorRuntime.mark(function r(){var e,t,n;return _regeneratorRuntime.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,supabase.auth.getSession();case 3:if(t=(e=r.sent).data,!(n=e.error)){r.next=10;break}if("Auth session missing!"!==n.message){r.next=9;break}return r.abrupt("return",{session:null,error:null});case 9:return r.abrupt("return",{session:null,error:n.message});case 10:return r.abrupt("return",{session:t.session,error:null});case 13:return r.prev=13,r.t0=r.catch(0),r.abrupt("return",{session:null,error:"Ha ocurrido un error inesperado al obtener la sesi\xf3n"});case 16:case"end":return r.stop()}},r,null,[[0,13]])}))).apply(this,arguments)}function i(){return u.apply(this,arguments)}function u(){return(u=(0,n.A)(s().mark(function r(){var e,t,n;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,o.N.auth.getUser();case 3:if(t=(e=r.sent).data.user,!(n=e.error)){r.next=10;break}if("Auth session missing!"!==n.message){r.next=9;break}return r.abrupt("return",{user:null,error:null});case 9:return r.abrupt("return",{user:null,error:n.message});case 10:return r.abrupt("return",{user:t,error:null});case 13:return r.prev=13,r.t0=r.catch(0),r.abrupt("return",{user:null,error:"Ha ocurrido un error inesperado al obtener el usuario actual"});case 16:case"end":return r.stop()}},r,null,[[0,13]])}))).apply(this,arguments)}},74100:(r,e,t)=>{t.d(e,{C9:()=>_,CM:()=>h,QE:()=>g,Sl:()=>x,Yp:()=>c,fW:()=>p,sj:()=>u,sq:()=>j,vW:()=>f});var n=t(33311),a=t(28295),s=t.n(a),o=t(55564);function c(r){return i.apply(this,arguments)}function i(){return(i=(0,n.A)(s().mark(function r(e){var t,n,a,c,i,u,l=arguments;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return t=l.length>1&&void 0!==l[1]&&l[1],r.prev=1,r.next=4,o.N.auth.getUser();case 4:if(a=r.sent.data.user){r.next=9;break}return console.error("No hay usuario autenticado para crear conversaci\xf3n"),r.abrupt("return",null);case 9:if(!t){r.next=12;break}return r.next=12,h();case 12:return r.next=14,o.N.from("conversaciones").insert([{titulo:e,activa:t,user_id:a.id}]).select();case 14:if(i=(c=r.sent).data,!(u=c.error)){r.next=20;break}return console.error("Error al crear conversaci\xf3n:",u),r.abrupt("return",null);case 20:return r.abrupt("return",(null==i||null==(n=i[0])?void 0:n.id)||null);case 23:return r.prev=23,r.t0=r.catch(1),console.error("Error inesperado al crear conversaci\xf3n:",r.t0),r.abrupt("return",null);case 27:case"end":return r.stop()}},r,null,[[1,23]])}))).apply(this,arguments)}function u(){return l.apply(this,arguments)}function l(){return(l=(0,n.A)(s().mark(function r(){var e,t,n,a;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,o.N.auth.getUser();case 3:if(e=r.sent.data.user){r.next=8;break}return console.error("No hay usuario autenticado para obtener conversaciones"),r.abrupt("return",[]);case 8:return r.next=10,o.N.from("conversaciones").select("*").eq("user_id",e.id).order("actualizado_en",{ascending:!1});case 10:if(n=(t=r.sent).data,!(a=t.error)){r.next=16;break}return console.error("Error al obtener conversaciones:",a),r.abrupt("return",[]);case 16:return r.abrupt("return",n||[]);case 19:return r.prev=19,r.t0=r.catch(0),console.error("Error inesperado al obtener conversaciones:",r.t0),r.abrupt("return",[]);case 23:case"end":return r.stop()}},r,null,[[0,19]])}))).apply(this,arguments)}function p(r,e){return d.apply(this,arguments)}function d(){return(d=(0,n.A)(s().mark(function r(e,t){var n;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,o.N.from("conversaciones").update({titulo:t,actualizado_en:new Date().toISOString()}).eq("id",e);case 2:if(!(n=r.sent.error)){r.next=7;break}return console.error("Error al actualizar conversaci\xf3n:",n),r.abrupt("return",!1);case 7:return r.abrupt("return",!0);case 8:case"end":return r.stop()}},r)}))).apply(this,arguments)}function f(r){return m.apply(this,arguments)}function m(){return(m=(0,n.A)(s().mark(function r(e){var t;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,h();case 3:return r.next=5,o.N.from("conversaciones").update({activa:!0,actualizado_en:new Date().toISOString()}).eq("id",e);case 5:if(!(t=r.sent.error)){r.next=10;break}return console.error("Error al activar conversaci\xf3n:",t),r.abrupt("return",!1);case 10:return r.abrupt("return",!0);case 13:return r.prev=13,r.t0=r.catch(0),console.error("Error inesperado al activar conversaci\xf3n:",r.t0),r.abrupt("return",!1);case 17:case"end":return r.stop()}},r,null,[[0,13]])}))).apply(this,arguments)}function h(){return b.apply(this,arguments)}function b(){return(b=(0,n.A)(s().mark(function r(){var e,t;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,o.N.auth.getUser();case 3:if(e=r.sent.data.user){r.next=8;break}return console.error("No hay usuario autenticado para desactivar conversaciones"),r.abrupt("return",!1);case 8:return r.next=10,o.N.from("conversaciones").update({activa:!1}).eq("user_id",e.id).eq("activa",!0);case 10:if(!(t=r.sent.error)){r.next=15;break}return console.error("Error al desactivar todas las conversaciones:",t),r.abrupt("return",!1);case 15:return r.abrupt("return",!0);case 18:return r.prev=18,r.t0=r.catch(0),console.error("Error inesperado al desactivar conversaciones:",r.t0),r.abrupt("return",!1);case 22:case"end":return r.stop()}},r,null,[[0,18]])}))).apply(this,arguments)}function x(){return v.apply(this,arguments)}function v(){return(v=(0,n.A)(s().mark(function r(){var e,t,n,a,c,i;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,o.N.auth.getUser();case 3:if(e=r.sent.data.user){r.next=8;break}return console.warn("No hay usuario autenticado para obtener conversaci\xf3n activa"),r.abrupt("return",null);case 8:return r.next=10,o.N.from("conversaciones").select("*").eq("user_id",e.id);case 10:return(t=r.sent).data,(n=t.error)&&console.error("Error al obtener todas las conversaciones:",n),r.next=16,o.N.from("conversaciones").select("*").eq("user_id",e.id).eq("activa",!0).limit(1);case 16:if(c=(a=r.sent).data,!(i=a.error)){r.next=24;break}if(!("406"===i.code||i.message.includes("406"))){r.next=22;break}return r.abrupt("return",null);case 22:return console.error("Error al obtener conversaci\xf3n activa:",i),r.abrupt("return",null);case 24:return r.abrupt("return",c&&c.length>0?c[0]:null);case 27:return r.prev=27,r.t0=r.catch(0),console.error("Error inesperado al obtener conversaci\xf3n activa:",r.t0),r.abrupt("return",null);case 31:case"end":return r.stop()}},r,null,[[0,27]])}))).apply(this,arguments)}function g(r){return y.apply(this,arguments)}function y(){return(y=(0,n.A)(s().mark(function r(e){var t,n,a,c,i,u,l;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,o.N.from("conversaciones").select("id").eq("id",e.conversacion_id).single();case 3:if((n=r.sent).data,!(a=n.error)){r.next=9;break}return console.error("Error al verificar la conversaci\xf3n:",a),r.abrupt("return",null);case 9:return r.next=11,o.N.from("mensajes").insert([e]).select();case 11:if(i=(c=r.sent).data,!(u=c.error)){r.next=17;break}return console.error("Error al guardar mensaje:",u),r.abrupt("return",null);case 17:return r.next=19,o.N.from("conversaciones").update({actualizado_en:new Date().toISOString()}).eq("id",e.conversacion_id);case 19:return(l=r.sent.error)&&console.error("Error al actualizar la fecha de la conversaci\xf3n:",l),r.abrupt("return",(null==i||null==(t=i[0])?void 0:t.id)||null);case 25:return r.prev=25,r.t0=r.catch(0),console.error("Error inesperado al guardar mensaje:",r.t0),r.abrupt("return",null);case 29:case"end":return r.stop()}},r,null,[[0,25]])}))).apply(this,arguments)}function _(r){return w.apply(this,arguments)}function w(){return(w=(0,n.A)(s().mark(function r(e){var t,n,a;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,o.N.from("mensajes").select("*").eq("conversacion_id",e).order("timestamp",{ascending:!0});case 2:if(n=(t=r.sent).data,!(a=t.error)){r.next=8;break}return console.error("Error al obtener mensajes:",a),r.abrupt("return",[]);case 8:return r.abrupt("return",n||[]);case 9:case"end":return r.stop()}},r)}))).apply(this,arguments)}function j(r){return N.apply(this,arguments)}function N(){return(N=(0,n.A)(s().mark(function r(e){var t,n,a,c,i;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,o.N.auth.getUser();case 3:if(t=r.sent.data.user){r.next=8;break}return console.error("No hay usuario autenticado para eliminar conversaci\xf3n"),r.abrupt("return",!1);case 8:return r.next=10,o.N.from("mensajes").delete().eq("conversacion_id",e);case 10:if(!(n=r.sent.error)){r.next=15;break}return console.error("Error al eliminar mensajes de la conversaci\xf3n:",n),r.abrupt("return",!1);case 15:return r.next=17,o.N.from("conversaciones").delete({count:"exact"}).eq("id",e).eq("user_id",t.id);case 17:if(c=(a=r.sent).error,i=a.count,!c){r.next=23;break}return console.error("Error al eliminar conversaci\xf3n:",c),r.abrupt("return",!1);case 23:if(0!==i){r.next=25;break}return r.abrupt("return",!1);case 25:return r.abrupt("return",!0);case 28:return r.prev=28,r.t0=r.catch(0),console.error("Error inesperado al eliminar conversaci\xf3n:",r.t0),r.abrupt("return",!1);case 32:case"end":return r.stop()}},r,null,[[0,28]])}))).apply(this,arguments)}},76133:(r,e,t)=>{t.d(e,{A:()=>h});var n=t(37711),a=t(10631),s=t(33311),o=t(28295),c=t.n(o),i=t(12115),u=t(75780),l=t(17863),p=t(1448),d=t(95155);function f(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,n)}return t}function m(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?f(Object(t),!0).forEach(function(e){(0,n.A)(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):f(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}let h=function(r){var e,t,n=r.plan,o=r.temarioId,f=(0,i.useState)([]),h=f[0],b=f[1],x=(0,i.useState)(null),v=x[0],g=x[1],y=(0,i.useState)(!0),_=y[0],w=y[1];(0,i.useEffect)(function(){j()},[o]);var j=(e=(0,s.A)(c().mark(function r(){var e;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,l.fF)(o);case 3:if(e=r.sent){r.next=10;break}return console.warn("No se encontr\xf3 plan activo para el temario:",o),g(null),b([]),w(!1),r.abrupt("return");case 10:return g(e.id),r.next=13,(0,l.$S)(e.id);case 13:b(r.sent),r.next=22;break;case 17:r.prev=17,r.t0=r.catch(0),console.error("Error al cargar progreso:",r.t0),g(null),b([]);case 22:return r.prev=22,w(!1),r.finish(22);case 25:case"end":return r.stop()}},r,null,[[0,17,22,25]])})),function(){return e.apply(this,arguments)}),N=(t=(0,s.A)(c().mark(function r(e,t,n){var s,o;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(v){r.next=3;break}return p.oR.error("No se pudo identificar el plan de estudios"),r.abrupt("return");case 3:return r.prev=3,o=!(null!=(s=h.find(function(r){return r.semana_numero===t&&r.dia_nombre===n&&r.tarea_titulo===e.titulo}))&&s.completado),r.next=8,(0,l.d7)(v,t,n,e.titulo,e.tipo,o);case 8:r.sent?(b(function(r){var s=r.findIndex(function(r){return r.semana_numero===t&&r.dia_nombre===n&&r.tarea_titulo===e.titulo});if(!(s>=0))return[].concat((0,a.A)(r),[{id:"temp-".concat(Date.now()),plan_id:v,user_id:"",semana_numero:t,dia_nombre:n,tarea_titulo:e.titulo,tarea_tipo:e.tipo,completado:o,fecha_completado:o?new Date().toISOString():void 0,creado_en:new Date().toISOString(),actualizado_en:new Date().toISOString()}]);var c=(0,a.A)(r);return c[s]=m(m({},c[s]),{},{completado:o,fecha_completado:o?new Date().toISOString():void 0}),c}),p.oR.success(o?"Tarea completada":"Tarea marcada como pendiente")):p.oR.error("Error al actualizar el progreso"),r.next=16;break;case 12:r.prev=12,r.t0=r.catch(3),console.error("Error al actualizar tarea:",r.t0),p.oR.error("Error al actualizar el progreso");case 16:case"end":return r.stop()}},r,null,[[3,12]])})),function(r,e,n){return t.apply(this,arguments)}),k=function(){if(!n||!n.semanas||!Array.isArray(n.semanas))return{completadas:0,total:0,porcentaje:0};var r=n.semanas.reduce(function(r,e){return e&&e.dias&&Array.isArray(e.dias)?r+e.dias.reduce(function(r,e){return e&&e.tareas&&Array.isArray(e.tareas)?r+e.tareas.length:r},0):r},0),e=h.filter(function(r){return r.completado}).length;return{completadas:e,total:r,porcentaje:r>0?Math.round(e/r*100):0}}();return _?(0,d.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,d.jsx)("span",{className:"ml-3 text-gray-600",children:"Cargando progreso..."})]}):n?o&&""!==o.trim()?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Introducci\xf3n"}),(0,d.jsx)("p",{className:"text-blue-800",children:n.introduccion||"Introducci\xf3n no disponible"})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-900",children:"Progreso General"}),(0,d.jsxs)("span",{className:"text-2xl font-bold text-green-600",children:[k.porcentaje,"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,d.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300",style:{width:"".concat(k.porcentaje,"%")}})}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[k.completadas," de ",k.total," tareas completadas"]})]}),n.resumen&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.Ohp,{className:"w-5 h-5 text-blue-600 mr-2"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Tiempo Total"}),(0,d.jsx)("p",{className:"font-semibold",children:n.resumen.tiempoTotalEstudio||"No disponible"})]})]})}),(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.H9b,{className:"w-5 h-5 text-green-600 mr-2"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Temas"}),(0,d.jsx)("p",{className:"font-semibold",children:n.resumen.numeroTemas||"No disponible"})]})]})}),(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.x_j,{className:"w-5 h-5 text-purple-600 mr-2"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Estudio Nuevo"}),(0,d.jsx)("p",{className:"font-semibold",children:n.resumen.duracionEstudioNuevo||"No disponible"})]})]})}),(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(u.jTZ,{className:"w-5 h-5 text-orange-600 mr-2"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-600",children:"Repaso Final"}),(0,d.jsx)("p",{className:"font-semibold",children:n.resumen.duracionRepasoFinal||"No disponible"})]})]})})]}),n.semanas&&n.semanas.length>0&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 flex items-center",children:[(0,d.jsx)(u.wIk,{className:"w-5 h-5 mr-2"}),"Cronograma Semanal"]}),n.semanas.map(function(r,e){return(0,d.jsxs)("div",{className:"border border-gray-200 rounded-lg overflow-hidden",children:[(0,d.jsxs)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("h4",{className:"text-lg font-semibold text-gray-900",children:["Semana ",(null==r?void 0:r.numero)||"N/A"]}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[(null==r?void 0:r.fechaInicio)||"N/A"," - ",(null==r?void 0:r.fechaFin)||"N/A"]})]}),(0,d.jsx)("p",{className:"text-gray-700 mt-2",children:(null==r?void 0:r.objetivoPrincipal)||"Objetivo no especificado"})]}),(0,d.jsx)("div",{className:"p-6 space-y-4",children:r.dias&&Array.isArray(r.dias)?r.dias.map(function(e,t){return(0,d.jsxs)("div",{className:"border border-gray-100 rounded-lg p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h5",{className:"font-semibold text-gray-900",children:(null==e?void 0:e.dia)||"D\xeda no especificado"}),(0,d.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded",children:[(null==e?void 0:e.horas)||0,"h"]})]}),(0,d.jsx)("div",{className:"space-y-2",children:e.tareas&&Array.isArray(e.tareas)?e.tareas.map(function(t,n){var a,s,o=(a=r.numero,s=e.dia,h.some(function(r){return r.semana_numero===a&&r.dia_nombre===s&&r.tarea_titulo===t.titulo&&r.completado}));return(0,d.jsxs)("div",{className:"flex items-start p-3 rounded-lg border transition-all cursor-pointer ".concat(o?"bg-green-50 border-green-200":"bg-white border-gray-200 hover:border-blue-300"),onClick:function(){return N(t,r.numero,e.dia)},children:[(0,d.jsx)("div",{className:"flex-shrink-0 w-5 h-5 rounded border-2 mr-3 mt-0.5 flex items-center justify-center ".concat(o?"bg-green-500 border-green-500":"border-gray-300 hover:border-blue-400"),children:o&&(0,d.jsx)(u.YrT,{className:"w-3 h-3 text-white"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h6",{className:"font-medium ".concat(o?"text-green-800 line-through":"text-gray-900"),children:(null==t?void 0:t.titulo)||"Tarea sin t\xedtulo"}),(null==t?void 0:t.descripcion)&&(0,d.jsx)("p",{className:"text-sm mt-1 ".concat(o?"text-green-700":"text-gray-600"),children:t.descripcion}),(0,d.jsxs)("div",{className:"flex items-center mt-2 space-x-3",children:[(0,d.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat((null==t?void 0:t.tipo)==="estudio"?"bg-blue-100 text-blue-800":(null==t?void 0:t.tipo)==="repaso"?"bg-yellow-100 text-yellow-800":(null==t?void 0:t.tipo)==="practica"?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:(null==t?void 0:t.tipo)||"general"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:(null==t?void 0:t.duracionEstimada)||"No especificado"})]})]})]},n)}):(0,d.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay tareas disponibles"})})]},t)}):(0,d.jsx)("p",{className:"text-gray-500 text-sm",children:"No hay d\xedas disponibles"})})]},e)})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-yellow-900 mb-2",children:"Estrategia de Repasos"}),(0,d.jsx)("p",{className:"text-yellow-800",children:"string"==typeof n.estrategiaRepasos?n.estrategiaRepasos:n.estrategiaRepasos&&"object"==typeof n.estrategiaRepasos&&n.estrategiaRepasos.descripcion||"Estrategia de repasos no disponible"})]}),(0,d.jsxs)("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-purple-900 mb-2",children:"Pr\xf3ximos Pasos y Consejos"}),(0,d.jsx)("p",{className:"text-purple-800",children:"string"==typeof n.proximosPasos?n.proximosPasos:n.proximosPasos&&"object"==typeof n.proximosPasos&&n.proximosPasos.descripcion||"Pr\xf3ximos pasos no disponibles"})]})]}):(0,d.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("p",{className:"text-gray-600",children:"ID de temario no v\xe1lido"})})}):(0,d.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("p",{className:"text-gray-600",children:"No se pudo cargar el plan de estudios"})})})}},76538:(r,e,t)=>{t.d(e,{w:()=>m,x:()=>d});var n=t(33311),a=t(28295),s=t.n(a),o=t(55564),c=t(66430),i=t(2775),u=t(47493),l=t(5929);function p(r,e){(null==e||e>r.length)&&(e=r.length);for(var t=0,n=Array(e);t<e;t++)n[t]=r[t];return n}function d(){return f.apply(this,arguments)}function f(){return(f=(0,n.A)(s().mark(function r(){var e,t,a,p,d,f,m,h,b,x,v,g;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,c.iF)();case 3:if(e=r.sent.user){r.next=7;break}throw Error("Usuario no autenticado");case 7:return r.next=9,o.N.from("documentos").select("id").eq("user_id",e.id);case 9:return t=r.sent.data,r.next=13,(0,i.oE)();case 13:return a=r.sent,r.next=16,(0,u.Lx)();case 16:return p=r.sent,r.next=19,(0,u.oC)();case 19:return d=r.sent,f=0,m=0,h=0,b=0,x=0,r.next=27,Promise.all(a.map(function(){var r=(0,n.A)(s().mark(function r(e){var t;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,(0,l.yV)(e.id);case 2:return t=r.sent,f+=t.total,m+=t.paraHoy,h+=t.nuevas,b+=t.aprendiendo,x+=t.repasando,r.abrupt("return",{id:e.id,titulo:e.titulo,fechaCreacion:e.creado_en,paraHoy:t.paraHoy});case 9:case"end":return r.stop()}},r)}));return function(e){return r.apply(this,arguments)}}()));case 27:return v=r.sent.sort(function(r,e){return new Date(e.fechaCreacion).getTime()-new Date(r.fechaCreacion).getTime()}).slice(0,5),g=p.map(function(r){return{id:r.id,titulo:r.titulo,fechaCreacion:r.creado_en,numeroPreguntas:r.numero_preguntas||0}}).slice(0,5),r.abrupt("return",{totalDocumentos:(null==t?void 0:t.length)||0,totalColeccionesFlashcards:a.length,totalTests:p.length,totalFlashcards:f,flashcardsParaHoy:m,flashcardsNuevas:h,flashcardsAprendiendo:b,flashcardsRepasando:x,testsRealizados:d.totalTests,porcentajeAcierto:d.porcentajeAcierto,coleccionesRecientes:v,testsRecientes:g});case 33:return r.prev=33,r.t0=r.catch(0),console.error("Error al obtener estad\xedsticas del dashboard:",r.t0),r.abrupt("return",{totalDocumentos:0,totalColeccionesFlashcards:0,totalTests:0,totalFlashcards:0,flashcardsParaHoy:0,flashcardsNuevas:0,flashcardsAprendiendo:0,flashcardsRepasando:0,testsRealizados:0,porcentajeAcierto:0,coleccionesRecientes:[],testsRecientes:[]});case 37:case"end":return r.stop()}},r,null,[[0,33]])}))).apply(this,arguments)}function m(){return h.apply(this,arguments)}function h(){return(h=(0,n.A)(s().mark(function r(){var e,t,n,a,u,l,d,f,m,h,b,x,v,g,y=arguments;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return e=y.length>0&&void 0!==y[0]?y[0]:10,r.prev=1,r.next=4,(0,c.iF)();case 4:if(r.sent.user){r.next=8;break}return r.abrupt("return",[]);case 8:return r.next=10,(0,i.oE)();case 10:if(0!==(t=r.sent).length){r.next=13;break}return r.abrupt("return",[]);case 13:return(n=new Date).setHours(23,59,59,999),r.next=17,o.N.from("progreso_flashcards").select("flashcard_id, proxima_revision, estado").lte("proxima_revision",n.toISOString()).order("proxima_revision",{ascending:!0}).limit(e);case 17:if(u=(a=r.sent).data,!(l=a.error)){r.next=23;break}return console.error("Error al obtener progreso de flashcards:",l),r.abrupt("return",[]);case 23:if(!(!u||0===u.length)){r.next=25;break}return r.abrupt("return",[]);case 25:return d=u.map(function(r){return r.flashcard_id}),r.next=28,o.N.from("flashcards").select("id, pregunta, coleccion_id").in("id",d);case 28:if(m=(f=r.sent).data,!(h=f.error)){r.next=34;break}return console.error("Error al obtener flashcards:",h),r.abrupt("return",[]);case 34:b=[],x=function(r,e){var t="undefined"!=typeof Symbol&&r[Symbol.iterator]||r["@@iterator"];if(!t){if(Array.isArray(r)||(t=function(r,e){if(r){if("string"==typeof r)return p(r,void 0);var t=Object.prototype.toString.call(r).slice(8,-1);if("Object"===t&&r.constructor&&(t=r.constructor.name),"Map"===t||"Set"===t)return Array.from(r);if("Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return p(r,e)}}(r))){t&&(r=t);var n=0,a=function(){};return{s:a,n:function(){return n>=r.length?{done:!0}:{done:!1,value:r[n++]}},e:function(r){throw r},f:a}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,o=!0,c=!1;return{s:function(){t=t.call(r)},n:function(){var r=t.next();return o=r.done,r},e:function(r){c=!0,s=r},f:function(){try{o||null==t.return||t.return()}finally{if(c)throw s}}}}(u),r.prev=36,g=s().mark(function r(){var e,n,a;return s().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:e=v.value,(n=null==m?void 0:m.find(function(r){return r.id===e.flashcard_id}))&&(a=t.find(function(r){return r.id===n.coleccion_id}))&&b.push({id:n.id,pregunta:n.pregunta,coleccionTitulo:a.titulo,coleccionId:a.id,proximaRevision:e.proxima_revision,estado:e.estado||"nuevo"});case 3:case"end":return r.stop()}},r)}),x.s();case 39:if((v=x.n()).done){r.next=43;break}return r.delegateYield(g(),"t0",41);case 41:r.next=39;break;case 43:r.next=48;break;case 45:r.prev=45,r.t1=r.catch(36),x.e(r.t1);case 48:return r.prev=48,x.f(),r.finish(48);case 51:return r.abrupt("return",b);case 54:return r.prev=54,r.t2=r.catch(1),console.error("Error al obtener pr\xf3ximas flashcards:",r.t2),r.abrupt("return",[]);case 58:case"end":return r.stop()}},r,null,[[1,54],[36,45,48,51]])}))).apply(this,arguments)}},86520:(r,e,t)=>{t.d(e,{A:()=>m});var n=t(37711),a=t(33311),s=t(28295),o=t.n(s),c=t(12115),i=t(36343),u=t(87925),l=t(95155);function p(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,n)}return t}function d(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?p(Object(t),!0).forEach(function(e){(0,n.A)(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):p(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}var f=(0,c.forwardRef)(function(r,e){var t,n=r.onSelectionChange,s=(0,c.useState)([]),p=s[0],f=s[1],m=(0,c.useState)([]),h=m[0],b=m[1],x=(0,c.useState)(!0),v=x[0],g=x[1],y=(t=(0,a.A)(o().mark(function r(){return o().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return g(!0),r.prev=1,r.next=4,(0,u.R1)();case 4:f(r.sent),r.next=11;break;case 8:r.prev=8,r.t0=r.catch(1),console.error("Error al cargar documentos:",r.t0);case 11:return r.prev=11,g(!1),r.finish(11);case 14:case"end":return r.stop()}},r,null,[[1,8,11,14]])})),function(){return t.apply(this,arguments)});(0,c.useEffect)(function(){y()},[]),(0,c.useImperativeHandle)(e,function(){return{recargarDocumentos:y}});var _=p.map(function(r){return{value:r.id,label:"".concat(r.numero_tema?"Tema ".concat(r.numero_tema,": "):"").concat(r.titulo," ").concat(r.categoria?"(".concat(r.categoria,")"):"")}});return(0,l.jsxs)("div",{className:"mb-3",children:[(0,l.jsx)("label",{className:"block text-gray-700 text-sm font-medium mb-1",children:"Selecciona los documentos para consultar:"}),(0,l.jsx)(i.Ay,{instanceId:"document-selector",isMulti:!0,isLoading:v,options:_,className:"basic-multi-select",classNamePrefix:"select",placeholder:"Selecciona uno o m\xe1s documentos...",noOptionsMessage:function(){return"No hay documentos disponibles"},onChange:function(r){b(r||[]),n(r.map(function(r){return p.find(function(e){return e.id===r.value})}).filter(Boolean))},value:h,styles:{control:function(r){return d(d({},r),{},{minHeight:"36px",fontSize:"14px"})},multiValue:function(r){return d(d({},r),{},{fontSize:"12px"})},placeholder:function(r){return d(d({},r),{},{fontSize:"14px"})}}}),0===h.length&&(0,l.jsx)("p",{className:"text-red-500 text-xs italic mt-0.5",children:"Debes seleccionar al menos un documento"})]})});f.displayName="DocumentSelector";let m=f},87925:(r,e,t)=>{t.d(e,{vW:()=>x.vW,fW:()=>x.fW,xq:()=>v.xq,qJ:()=>v.qJ,Yp:()=>x.Yp,_4:()=>y._4,CM:()=>x.CM,sq:()=>x.sq,Q3:()=>h,hE:()=>f,yK:()=>v.yK,QE:()=>x.QE,OA:()=>y.OA,oE:()=>v.oE,Sl:()=>x.Sl,sj:()=>x.sj,R1:()=>p,yV:()=>g.yV,wU:()=>g.wU,oC:()=>y.oC,dd:()=>y.dd,C9:()=>x.C9,hg:()=>y.hg,Kj:()=>y.Kj,Lx:()=>y.Lx,Gl:()=>y.Gl});var n=t(55564),a=t(37711),s=t(33311),o=t(28295),c=t.n(o),i=t(66430);function u(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable})),t.push.apply(t,n)}return t}function l(r){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?u(Object(t),!0).forEach(function(e){(0,a.A)(r,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(t,e))})}return r}function p(){return d.apply(this,arguments)}function d(){return(d=(0,s.A)(c().mark(function r(){var e,t,a,s;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(e=r.sent.user){r.next=8;break}return console.error("No hay usuario autenticado"),r.abrupt("return",[]);case 8:return r.next=10,n.N.from("documentos").select("*").eq("user_id",e.id).order("numero_tema",{ascending:!0});case 10:if(a=(t=r.sent).data,!(s=t.error)){r.next=16;break}return console.error("Error al obtener documentos:",s),r.abrupt("return",[]);case 16:return r.abrupt("return",a||[]);case 19:return r.prev=19,r.t0=r.catch(0),console.error("Error al obtener documentos:",r.t0),r.abrupt("return",[]);case 23:case"end":return r.stop()}},r,null,[[0,19]])}))).apply(this,arguments)}function f(r){return m.apply(this,arguments)}function m(){return(m=(0,s.A)(c().mark(function r(e){var t,a,s,o,u,p;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,r.next=3,(0,i.iF)();case 3:if(a=r.sent.user){r.next=8;break}return console.error("No hay usuario autenticado"),r.abrupt("return",null);case 8:return s=l(l({},e),{},{user_id:a.id,tipo_original:e.tipo_original}),r.next=11,n.N.from("documentos").insert([s]).select();case 11:if(u=(o=r.sent).data,!(p=o.error)){r.next=17;break}return console.error("Error al guardar documento:",p),r.abrupt("return",null);case 17:return r.abrupt("return",(null==u||null==(t=u[0])?void 0:t.id)||null);case 20:return r.prev=20,r.t0=r.catch(0),console.error("Error al guardar documento:",r.t0),r.abrupt("return",null);case 24:case"end":return r.stop()}},r,null,[[0,20]])}))).apply(this,arguments)}function h(r){return b.apply(this,arguments)}function b(){return(b=(0,s.A)(c().mark(function r(e){var t,a,s,o;return c().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.prev=0,console.log("\uD83D\uDDD1️ Iniciando eliminaci\xf3n de documento:",e),r.next=4,(0,i.iF)();case 4:if(t=r.sent.user){r.next=9;break}return console.error("❌ No hay usuario autenticado para eliminar documento"),r.abrupt("return",!1);case 9:return console.log("\uD83D\uDC64 Usuario autenticado:",t.id),console.log("\uD83D\uDCC4 Eliminando documento ID:",e),r.next=13,n.N.from("documentos").delete({count:"exact"}).eq("id",e).eq("user_id",t.id);case 13:if(s=(a=r.sent).error,o=a.count,!s){r.next=19;break}return console.error("❌ Error al eliminar documento de Supabase:",s),r.abrupt("return",!1);case 19:if(console.log("✅ Documento eliminado exitosamente. Filas afectadas:",o),0!==o){r.next=23;break}return console.warn("⚠️ No se elimin\xf3 ning\xfan documento. Posibles causas: documento no existe o no pertenece al usuario"),r.abrupt("return",!1);case 23:return r.abrupt("return",!0);case 26:return r.prev=26,r.t0=r.catch(0),console.error("\uD83D\uDCA5 Error inesperado al eliminar documento:",r.t0),r.abrupt("return",!1);case 30:case"end":return r.stop()}},r,null,[[0,26]])}))).apply(this,arguments)}var x=t(74100),v=t(2775),g=t(5929),y=t(47493);t(76538)}}]);