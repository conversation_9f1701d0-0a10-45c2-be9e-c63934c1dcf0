"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx":
/*!****************************************************************!*\
  !*** ./src/features/summaries/components/SummaryGenerator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryGenerator)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/resumenesService */ \"(app-pages-browser)/./src/lib/supabase/resumenesService.ts\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\summaries\\\\components\\\\SummaryGenerator.tsx\", _s1 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\n\nvar summarySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    instrucciones: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres')\n});\nfunction SummaryGenerator(_ref) {\n    _s();\n    _s1();\n    var documentosSeleccionados = _ref.documentosSeleccionados, onSummaryGenerated = _ref.onSummaryGenerated;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), resumenGenerado = _useState[0], setResumenGenerado = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), tiempoEstimado = _useState2[0], setTiempoEstimado = _useState2[1];\n    var _useBackgroundGenerat = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration)(), generateResumen = _useBackgroundGenerat.generateResumen, isGenerating = _useBackgroundGenerat.isGenerating, getActiveTask = _useBackgroundGenerat.getActiveTask;\n    var _useForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(summarySchema),\n        defaultValues: {\n            instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'\n        }\n    }), register = _useForm.register, handleSubmit = _useForm.handleSubmit, errors = _useForm.formState.errors, reset = _useForm.reset;\n    // Funciones de validación\n    var validarDocumentoParaResumen = function validarDocumentoParaResumen(documento) {\n        if (!documento) {\n            return {\n                valido: false,\n                error: 'No se ha proporcionado ningún documento'\n            };\n        }\n        if (!documento.titulo || documento.titulo.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener un título'\n            };\n        }\n        if (!documento.contenido || documento.contenido.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener contenido'\n            };\n        }\n        if (documento.contenido.trim().length < 50) {\n            return {\n                valido: false,\n                error: 'El contenido del documento es demasiado corto para generar un resumen útil'\n            };\n        }\n        return {\n            valido: true\n        };\n    };\n    var estimarTiempoGeneracion = function estimarTiempoGeneracion(documento) {\n        if (!documento || !documento.contenido) {\n            return 30; // 30 segundos por defecto\n        }\n        var palabras = documento.contenido.split(/\\s+/).length;\n        // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos\n        var tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));\n        return tiempoEstimado;\n    };\n    // Validaciones\n    var puedeGenerar = documentosSeleccionados.length === 1;\n    var documento = documentosSeleccionados[0];\n    var validacionDocumento = documento ? validarDocumentoParaResumen(documento) : {\n        valido: false,\n        error: 'No hay documento seleccionado'\n    };\n    var onSubmit = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee(data) {\n            var _documento$contenido, _requestData$contexto, _result$result, _result$result2, existe, requestData, response, responseText, result, resumenContent, resumenId, errorMessage, errorObj;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        if (!(!puedeGenerar || !documento || !validacionDocumento.valido)) {\n                            _context.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');\n                        return _context.abrupt(\"return\");\n                    case 3:\n                        _context.prev = 3;\n                        console.log('🚀 Iniciando generación de resumen...');\n                        setIsGenerating(true);\n                        setTiempoEstimado(estimarTiempoGeneracion(documento));\n                        console.log('📄 Documento seleccionado:', {\n                            id: documento.id,\n                            titulo: documento.titulo,\n                            categoria: documento.categoria,\n                            numero_tema: documento.numero_tema,\n                            contenidoLength: ((_documento$contenido = documento.contenido) === null || _documento$contenido === void 0 ? void 0 : _documento$contenido.length) || 0\n                        });\n                        // Verificar si ya existe un resumen para este documento\n                        console.log('🔍 Verificando si ya existe resumen...');\n                        _context.next = 11;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.existeResumenParaDocumento)(documento.id);\n                    case 11:\n                        existe = _context.sent;\n                        if (!existe) {\n                            _context.next = 17;\n                            break;\n                        }\n                        console.log('⚠️ Ya existe un resumen para este documento');\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');\n                        setIsGenerating(false);\n                        return _context.abrupt(\"return\");\n                    case 17:\n                        console.log('✅ No existe resumen previo, continuando...');\n                        // Preparar datos para la API\n                        requestData = {\n                            action: 'generarResumen',\n                            peticion: \"\".concat(documento.titulo, \"|\").concat(documento.categoria || '', \"|\").concat(documento.numero_tema || '', \"|\").concat(data.instrucciones),\n                            contextos: [\n                                documento.contenido\n                            ]\n                        };\n                        console.log('📡 Enviando petición a la API:', {\n                            action: requestData.action,\n                            peticion: requestData.peticion,\n                            contextosLength: requestData.contextos.length,\n                            primerContextoLength: ((_requestData$contexto = requestData.contextos[0]) === null || _requestData$contexto === void 0 ? void 0 : _requestData$contexto.length) || 0\n                        });\n                        // Generar el resumen usando la API\n                        _context.next = 22;\n                        return fetch('/api/ai', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify(requestData)\n                        });\n                    case 22:\n                        response = _context.sent;\n                        console.log(\"\\uD83D\\uDCE8 Respuesta recibida - Status: \".concat(response.status, \", OK: \").concat(response.ok));\n                        // Capturar respuesta como texto primero para depuración\n                        _context.next = 26;\n                        return response.text();\n                    case 26:\n                        responseText = _context.sent;\n                        console.log('📄 Respuesta como texto (primeros 200 chars):', responseText.substring(0, 200));\n                        // Intentar parsear como JSON\n                        _context.prev = 28;\n                        result = JSON.parse(responseText);\n                        console.log('✅ JSON parseado exitosamente');\n                        _context.next = 38;\n                        break;\n                    case 33:\n                        _context.prev = 33;\n                        _context.t0 = _context[\"catch\"](28);\n                        console.error('❌ Error al parsear respuesta JSON:', _context.t0);\n                        console.error('📄 Respuesta completa:', responseText);\n                        throw new Error(\"Error en formato de respuesta: \".concat(responseText.substring(0, 100), \"...\"));\n                    case 38:\n                        if (response.ok) {\n                            _context.next = 41;\n                            break;\n                        }\n                        console.error('❌ Error en respuesta de la API:', result);\n                        throw new Error(result.detalles || result.error || 'Error al generar el resumen');\n                    case 41:\n                        console.log('📋 Resultado de la API:', {\n                            hasResult: !!result.result,\n                            resultLength: ((_result$result = result.result) === null || _result$result === void 0 ? void 0 : _result$result.length) || 0,\n                            resultPreview: ((_result$result2 = result.result) === null || _result$result2 === void 0 ? void 0 : _result$result2.substring(0, 100)) || 'Sin contenido'\n                        });\n                        resumenContent = result.result;\n                        if (resumenContent) {\n                            _context.next = 46;\n                            break;\n                        }\n                        console.error('❌ No se recibió contenido del resumen');\n                        throw new Error('No se recibió contenido del resumen');\n                    case 46:\n                        console.log('✅ Contenido del resumen recibido, longitud:', resumenContent.length);\n                        setResumenGenerado(resumenContent);\n                        // Guardar en Supabase\n                        console.log('💾 Guardando resumen en Supabase...');\n                        _context.next = 51;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.guardarResumen)(documento.id, \"Resumen: \".concat(documento.titulo), resumenContent, data.instrucciones);\n                    case 51:\n                        resumenId = _context.sent;\n                        if (!resumenId) {\n                            _context.next = 59;\n                            break;\n                        }\n                        console.log('✅ Resumen guardado exitosamente con ID:', resumenId);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success('Resumen generado y guardado exitosamente');\n                        onSummaryGenerated === null || onSummaryGenerated === void 0 || onSummaryGenerated(resumenId);\n                        reset();\n                        _context.next = 61;\n                        break;\n                    case 59:\n                        console.error('❌ Error al guardar el resumen - no se recibió ID');\n                        throw new Error('Error al guardar el resumen en la base de datos');\n                    case 61:\n                        _context.next = 70;\n                        break;\n                    case 63:\n                        _context.prev = 63;\n                        _context.t1 = _context[\"catch\"](3);\n                        console.error('Error completo al generar resumen:', _context.t1);\n                        // Manejo mejorado de errores con más detalles\n                        errorMessage = 'Error al generar el resumen';\n                        if (_context.t1 instanceof Error) {\n                            errorMessage = _context.t1.message;\n                        } else if (typeof _context.t1 === 'object' && _context.t1 !== null) {\n                            // Si es un objeto, intentar extraer información útil\n                            errorObj = _context.t1;\n                            if (errorObj.message) {\n                                errorMessage = errorObj.message;\n                            } else if (errorObj.error) {\n                                errorMessage = errorObj.error;\n                            } else if (errorObj.detalles) {\n                                errorMessage = errorObj.detalles;\n                            } else {\n                                errorMessage = \"Error del servidor: \".concat(JSON.stringify(_context.t1));\n                            }\n                        } else if (typeof _context.t1 === 'string') {\n                            errorMessage = _context.t1;\n                        }\n                        console.error('Mensaje de error procesado:', errorMessage);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(errorMessage);\n                    case 70:\n                        _context.prev = 70;\n                        setIsGenerating(false);\n                        setTiempoEstimado(0);\n                        return _context.finish(70);\n                    case 74:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    3,\n                    63,\n                    70,\n                    74\n                ],\n                [\n                    28,\n                    33\n                ]\n            ]);\n        }));\n        return function onSubmit(_x) {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCC4 Generaci\\xF3n de Res\\xFAmenes\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    !puedeGenerar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Selecci\\xF3n incorrecta\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: documentosSeleccionados.length === 0 ? 'Debes seleccionar exactamente un documento para generar un resumen.' : \"Tienes \".concat(documentosSeleccionados.length, \" documentos seleccionados. Solo se permite generar un resumen por tema.\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this) : !validacionDocumento.valido ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Documento no v\\xE1lido\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: validacionDocumento.error\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u2705 Documento seleccionado:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                        children: documento.titulo\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    documento.numero_tema && \" (Tema \".concat(documento.numero_tema, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-blue-600\",\n                                children: [\n                                    \"Contenido: ~\",\n                                    documento.contenido.split(/\\s+/).length,\n                                    \" palabras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 7\n            }, this),\n            puedeGenerar && validacionDocumento.valido && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"label\", {\n                                htmlFor: \"instrucciones\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Instrucciones para el resumen:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 262,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"textarea\", _objectSpread(_objectSpread({\n                                id: \"instrucciones\"\n                            }, register('instrucciones')), {}, {\n                                disabled: isGenerating,\n                                rows: 4,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical\",\n                                placeholder: \"Describe c\\xF3mo quieres que sea el resumen...\"\n                            }), void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this),\n                            errors.instrucciones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.instrucciones.message\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 276,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 261,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isGenerating,\n                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 288,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generando resumen...\",\n                                tiempoEstimado > 0 && \" (~\".concat(tiempoEstimado, \"s)\")\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 299,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generar Resumen\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                    children: \"\\u23F3 Generando resumen...\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 309,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 309,\n                                    columnNumber: 56\n                                }, this),\n                                \"La IA est\\xE1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar \",\n                                tiempoEstimado > 0 ? \"aproximadamente \".concat(tiempoEstimado, \" segundos\") : 'unos momentos',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 308,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 9\n            }, this),\n            resumenGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-3\",\n                        children: \"\\uD83D\\uDCCB Resumen Generado\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: resumenGenerado.replace(/\\n/g, '<br />')\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-2\",\n                        children: \"\\u2705 Resumen guardado exitosamente. Puedes acceder a \\xE9l desde la secci\\xF3n de res\\xFAmenes.\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(SummaryGenerator, \"9+gDAxb3pjD4iqKqS0jBtFUhQfY=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c1 = SummaryGenerator;\n_s1(SummaryGenerator, \"7n573ccQAM0ST1evOWg7gNsS69Y=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SummaryGenerator;\nvar _c;\n$RefreshReg$(_c, \"SummaryGenerator\");\nvar _c1;\n$RefreshReg$(_c1, \"SummaryGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9mZWF0dXJlcy9zdW1tYXJpZXMvY29tcG9uZW50cy9TdW1tYXJ5R2VuZXJhdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBYTtBQUFBO0FBQUEsSUFBQUUsWUFBQSw0SEFBQUMsRUFBQSxJQUFBQyxZQUFBO0FBQUEsU0FBQUMsUUFBQUMsQ0FBQSxFQUFBQyxDQUFBO0lBQUEsSUFBQUMsQ0FBQSxHQUFBQyxNQUFBLENBQUFDLElBQUEsQ0FBQUosQ0FBQTtJQUFBLElBQUFHLE1BQUEsQ0FBQUUscUJBQUE7UUFBQSxJQUFBQyxDQUFBLEdBQUFILE1BQUEsQ0FBQUUscUJBQUEsQ0FBQUwsQ0FBQTtRQUFBQyxDQUFBLEtBQUFLLENBQUEsR0FBQUEsQ0FBQSxDQUFBQyxNQUFBLFVBQUFOLENBQUE7WUFBQSxPQUFBRSxNQUFBLENBQUFLLHdCQUFBLENBQUFSLENBQUEsRUFBQUMsQ0FBQSxFQUFBUSxVQUFBO1FBQUEsS0FBQVAsQ0FBQSxDQUFBUSxJQUFBLENBQUFDLEtBQUEsQ0FBQVQsQ0FBQSxFQUFBSSxDQUFBO0lBQUE7SUFBQSxPQUFBSixDQUFBO0FBQUE7QUFBQSxTQUFBVSxjQUFBWixDQUFBO0lBQUEsUUFBQUMsQ0FBQSxNQUFBQSxDQUFBLEdBQUFZLFNBQUEsQ0FBQUMsTUFBQSxFQUFBYixDQUFBO1FBQUEsSUFBQUMsQ0FBQSxXQUFBVyxTQUFBLENBQUFaLENBQUEsSUFBQVksU0FBQSxDQUFBWixDQUFBO1FBQUFBLENBQUEsT0FBQUYsT0FBQSxDQUFBSSxNQUFBLENBQUFELENBQUEsT0FBQWEsT0FBQSxVQUFBZCxDQUFBO1lBQUFQLDBLQUFBLENBQUFNLENBQUEsRUFBQUMsQ0FBQSxFQUFBQyxDQUFBLENBQUFELENBQUE7UUFBQSxLQUFBRSxNQUFBLENBQUFhLHlCQUFBLEdBQUFiLE1BQUEsQ0FBQWMsZ0JBQUEsQ0FBQWpCLENBQUEsRUFBQUcsTUFBQSxDQUFBYSx5QkFBQSxDQUFBZCxDQUFBLEtBQUFILE9BQUEsQ0FBQUksTUFBQSxDQUFBRCxDQUFBLEdBQUFhLE9BQUEsVUFBQWQsQ0FBQTtZQUFBRSxNQUFBLENBQUFlLGNBQUEsQ0FBQWxCLENBQUEsRUFBQUMsQ0FBQSxFQUFBRSxNQUFBLENBQUFLLHdCQUFBLENBQUFOLENBQUEsRUFBQUQsQ0FBQTtRQUFBO0lBQUE7SUFBQSxPQUFBRCxDQUFBO0FBQUE7QUFBQTtBQUU4QjtBQUNGO0FBQ1k7QUFDOUI7QUFDZ0I7QUFFcUQ7QUFDbkI7QUFBQztBQUFBO0FBRTFFLElBQU1nQyxhQUFhLEdBQUdULHlDQUFRLENBQUM7SUFDN0JXLGFBQWEsRUFBRVgseUNBQVEsQ0FBQyxDQUFDLENBQUNhLEdBQUcsQ0FBQyxFQUFFLEVBQUUsc0RBQXNEO0FBQzFGLENBQUMsQ0FBQztBQVNhLDBCQUF5QkUsSUFBQSxFQUF5RTs7SUFBQXpDLEVBQUE7SUFBQSxJQUF0RTBDLHVCQUF1QixHQUFBRCxJQUFBLENBQXZCQyx1QkFBdUIsRUFBRUMsa0JBQWtCLEdBQUFGLElBQUEsQ0FBbEJFLGtCQUFrQjtJQUNwRixJQUFBQyxTQUFBLEdBQThDckIsK0NBQVEsQ0FBZ0IsSUFBSSxDQUFDLEVBQXBFc0IsZUFBZSxHQUFBRCxTQUFBLEtBQUVFLGtCQUFrQixHQUFBRixTQUFBO0lBQzFDLElBQUFHLFVBQUEsR0FBNEN4QiwrQ0FBUSxDQUFTLENBQUMsQ0FBQyxFQUF4RHlCLGNBQWMsR0FBQUQsVUFBQSxLQUFFRSxpQkFBaUIsR0FBQUYsVUFBQTtJQUV4QyxJQUFBRyxxQkFBQSw4RkFBUUMsZUFBZSxHQUFBRCxxQkFBQSxDQUFmQyxlQUFlLEVBQUVDLFlBQVksR0FBQUYscUJBQUEsQ0FBWkUsWUFBWSxFQUFFQyxhQUFhLEdBQUFILHFCQUFBLENBQWJHLGFBQWE7SUFFcEQsSUFBQUMsUUFBQSw2REFLNkI7UUFDM0JDLFFBQVEsRUFBRTlCLG9FQUFXLENBQUNVLGFBQWEsQ0FBQztRQUNwQ3FCLGFBQWEsRUFBRTtZQUNibkIsYUFBYSxFQUFFO1FBQ2pCO0lBQ0YsQ0FBQyxDQUFDLEVBVEFvQixRQUFRLEdBQUFILFFBQUEsQ0FBUkcsUUFBUSxFQUNSQyxZQUFZLEdBQUFKLFFBQUEsQ0FBWkksWUFBWSxFQUNDQyxNQUFNLEdBQUFMLFFBQUEsQ0FBbkJNLFNBQVMsQ0FBSUQsTUFBTSxFQUNuQkUsS0FBSyxHQUFBUCxRQUFBLENBQUxPLEtBQUs7SUFRUDtJQUNBLElBQU1DLDJCQUEyQixHQUFHLFNBQTlCQSwyQkFBMkJBLENBQUlDLFNBQWMsRUFBMEM7UUFDM0YsSUFBSSxDQUFDQSxTQUFTLEVBQUU7WUFDZCxPQUFPO2dCQUFFQyxNQUFNLEVBQUUsS0FBSztnQkFBRUMsS0FBSyxFQUFFO1lBQTBDLENBQUM7UUFDNUU7UUFFQSxJQUFJLENBQUNGLFNBQVMsQ0FBQ0csTUFBTSxJQUFJSCxTQUFTLENBQUNHLE1BQU0sQ0FBQ0MsSUFBSSxDQUFDLENBQUMsQ0FBQ2xELE1BQU0sS0FBSyxDQUFDLEVBQUU7WUFDN0QsT0FBTztnQkFBRStDLE1BQU0sRUFBRSxLQUFLO2dCQUFFQyxLQUFLLEVBQUU7WUFBb0MsQ0FBQztRQUN0RTtRQUVBLElBQUksQ0FBQ0YsU0FBUyxDQUFDSyxTQUFTLElBQUlMLFNBQVMsQ0FBQ0ssU0FBUyxDQUFDRCxJQUFJLENBQUMsQ0FBQyxDQUFDbEQsTUFBTSxLQUFLLENBQUMsRUFBRTtZQUNuRSxPQUFPO2dCQUFFK0MsTUFBTSxFQUFFLEtBQUs7Z0JBQUVDLEtBQUssRUFBRTtZQUFvQyxDQUFDO1FBQ3RFO1FBRUEsSUFBSUYsU0FBUyxDQUFDSyxTQUFTLENBQUNELElBQUksQ0FBQyxDQUFDLENBQUNsRCxNQUFNLEdBQUcsRUFBRSxFQUFFO1lBQzFDLE9BQU87Z0JBQUUrQyxNQUFNLEVBQUUsS0FBSztnQkFBRUMsS0FBSyxFQUFFO1lBQTZFLENBQUM7UUFDL0c7UUFFQSxPQUFPO1lBQUVELE1BQU0sRUFBRTtRQUFLLENBQUM7SUFDekIsQ0FBQztJQUVELElBQU1LLHVCQUF1QixHQUFHLFNBQTFCQSx1QkFBdUJBLENBQUlOLFNBQWMsRUFBYTtRQUMxRCxJQUFJLENBQUNBLFNBQVMsSUFBSSxDQUFDQSxTQUFTLENBQUNLLFNBQVMsRUFBRTtZQUN0QyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1FBQ2I7UUFFQSxJQUFNRSxRQUFRLEdBQUdQLFNBQVMsQ0FBQ0ssU0FBUyxDQUFDRyxLQUFLLENBQUMsS0FBSyxDQUFDLENBQUN0RCxNQUFNO1FBRXhEO1FBQ0EsSUFBTStCLGNBQWMsR0FBR3dCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLEVBQUUsRUFBRUQsSUFBSSxDQUFDakMsR0FBRyxDQUFDLEdBQUcsRUFBRWlDLElBQUksQ0FBQ0UsSUFBSSxDQUFDSixRQUFRLEdBQUcsR0FBRyxDQUFDLENBQUMsQ0FBQztRQUU3RSxPQUFPdEIsY0FBYztJQUN2QixDQUFDO0lBRUQ7SUFDQSxJQUFNMkIsWUFBWSxHQUFHakMsdUJBQXVCLENBQUN6QixNQUFNLEtBQUssQ0FBQztJQUN6RCxJQUFNOEMsU0FBUyxHQUFHckIsdUJBQXVCLENBQUMsQ0FBQyxDQUFDO0lBRTVDLElBQU1rQyxtQkFBbUIsR0FBR2IsU0FBUyxHQUFHRCwyQkFBMkIsQ0FBQ0MsU0FBUyxDQUFDLEdBQUc7UUFBRUMsTUFBTSxFQUFFLEtBQUs7UUFBRUMsS0FBSyxFQUFFO0lBQWdDLENBQUM7SUFFMUksSUFBTVksUUFBUTtRQUFBLElBQUFDLEtBQUEsR0FBQWhGLDRLQUFBLGVBQUF3Qiw4SUFBQSxDQUFHLFNBQUEwRCxRQUFPQyxJQUFxQjtZQUFBLElBQUFDLG9CQUFBLEVBQUFDLHFCQUFBLEVBQUFDLGNBQUEsRUFBQUMsZUFBQSxFQUFBQyxNQUFBLEVBQUFDLFdBQUEsRUFBQUMsUUFBQSxFQUFBQyxZQUFBLEVBQUFDLE1BQUEsRUFBQUMsY0FBQSxFQUFBQyxTQUFBLEVBQUFDLFlBQUEsRUFBQUMsUUFBQTtZQUFBLE9BQUF4RSw4SUFBQSxVQUFBMEUsU0FBQUMsUUFBQTtnQkFBQSxlQUFBQSxRQUFBLENBQUFDLElBQUEsR0FBQUQsUUFBQSxDQUFBRSxJQUFBO29CQUFBO3dCQUFBLE1BQ3ZDLENBQUN4QixZQUFZLElBQUksQ0FBQ1osU0FBUyxJQUFJLENBQUNhLG1CQUFtQixDQUFDWixNQUFBQSxHQUFNOzRCQUFBaUMsUUFBQSxDQUFBRSxJQUFBOzRCQUFBO3dCQUFBO3dCQUM1RHhFLGtEQUFLLENBQUNzQyxLQUFLLENBQUMsNEZBQTRGLENBQUM7d0JBQUMsT0FBQWdDLFFBQUEsQ0FBQUcsTUFBQTtvQkFBQTt3QkFBQUgsUUFBQSxDQUFBQyxJQUFBO3dCQUsxR0csT0FBTyxDQUFDQyxHQUFHLENBQUMsdUNBQXVDLENBQUM7d0JBQ3BEQyxlQUFlLENBQUMsSUFBSSxDQUFDO3dCQUNyQnRELGlCQUFpQixDQUFDb0IsdUJBQXVCLENBQUNOLFNBQVMsQ0FBQyxDQUFDO3dCQUVyRHNDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDRCQUE0QixFQUFFOzRCQUN4Q0UsRUFBRSxFQUFFekMsU0FBUyxDQUFDeUMsRUFBRTs0QkFDaEJ0QyxNQUFNLEVBQUVILFNBQVMsQ0FBQ0csTUFBTTs0QkFDeEJ1QyxTQUFTLEVBQUUxQyxTQUFTLENBQUMwQyxTQUFTOzRCQUM5QkMsV0FBVyxFQUFFM0MsU0FBUyxDQUFDMkMsV0FBVzs0QkFDbENDLGVBQWUsRUFBRSxFQUFBekIsb0JBQUEsR0FBQW5CLFNBQVMsQ0FBQ0ssU0FBQUEsTUFBUyxRQUFBYyxvQkFBQSx1QkFBbkJBLG9CQUFBLENBQXFCakUsTUFBQUEsS0FBVTt3QkFDbEQsQ0FBQyxDQUFDO3dCQUVGO3dCQUNBb0YsT0FBTyxDQUFDQyxHQUFHLENBQUMsd0NBQXdDLENBQUM7d0JBQUNMLFFBQUEsQ0FBQUUsSUFBQTt3QkFBQSxPQUNqQ3RFLDBGQUEwQixDQUFDa0MsU0FBUyxDQUFDeUMsRUFBRSxDQUFDO29CQUFBO3dCQUF2RGxCLE1BQU0sR0FBQVcsUUFBQSxDQUFBVyxJQUFBO3dCQUFBLEtBQ1J0QixNQUFNOzRCQUFBVyxRQUFBLENBQUFFLElBQUE7NEJBQUE7d0JBQUE7d0JBQ1JFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLDZDQUE2QyxDQUFDO3dCQUMxRDNFLGtEQUFLLENBQUNzQyxLQUFLLENBQUMsZ0ZBQWdGLENBQUM7d0JBQzdGc0MsZUFBZSxDQUFDLEtBQUssQ0FBQzt3QkFBQyxPQUFBTixRQUFBLENBQUFHLE1BQUE7b0JBQUE7d0JBR3pCQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyw0Q0FBNEMsQ0FBQzt3QkFFekQ7d0JBQ01mLFdBQVcsR0FBRzs0QkFDbEJzQixNQUFNLEVBQUUsZ0JBQWdCOzRCQUN4QkMsUUFBUSxLQUFBQyxNQUFBLENBQUtoRCxTQUFTLENBQUNHLE1BQU0sT0FBQTZDLE1BQUEsQ0FBSWhELFNBQVMsQ0FBQzBDLFNBQVMsSUFBSSxFQUFFLE9BQUFNLE1BQUEsQ0FBSWhELFNBQVMsQ0FBQzJDLFdBQVcsSUFBSSxFQUFFLE9BQUFLLE1BQUEsQ0FBSTlCLElBQUksQ0FBQzVDLGFBQWEsQ0FBRTs0QkFDakgyRSxTQUFTLEVBQUU7Z0NBQUNqRCxTQUFTLENBQUNLLFNBQVM7NkJBQUE7d0JBQ2pDLENBQUM7d0JBRURpQyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxnQ0FBZ0MsRUFBRTs0QkFDNUNPLE1BQU0sRUFBRXRCLFdBQVcsQ0FBQ3NCLE1BQU07NEJBQzFCQyxRQUFRLEVBQUV2QixXQUFXLENBQUN1QixRQUFROzRCQUM5QkcsZUFBZSxFQUFFMUIsV0FBVyxDQUFDeUIsU0FBUyxDQUFDL0YsTUFBTTs0QkFDN0NpRyxvQkFBb0IsRUFBRSxFQUFBL0IscUJBQUEsR0FBQUksV0FBVyxDQUFDeUIsU0FBUyxDQUFDLEVBQUMsTUFBQyxRQUFBN0IscUJBQUEsdUJBQXhCQSxxQkFBQSxDQUEwQmxFLE1BQUFBLEtBQVU7d0JBQzVELENBQUMsQ0FBQzt3QkFFRjt3QkFBQWdGLFFBQUEsQ0FBQUUsSUFBQTt3QkFBQSxPQUN1QmdCLEtBQUssQ0FBQyxTQUFTLEVBQUU7NEJBQ3RDQyxNQUFNLEVBQUUsTUFBTTs0QkFDZEMsT0FBTyxFQUFFO2dDQUNQLGNBQWMsRUFBRTs0QkFDbEIsQ0FBQzs0QkFDREMsSUFBSSxFQUFFQyxJQUFJLENBQUNDLFNBQVMsQ0FBQ2pDLFdBQVc7d0JBQ2xDLENBQUMsQ0FBQztvQkFBQTt3QkFOSUMsUUFBUSxHQUFBUyxRQUFBLENBQUFXLElBQUE7d0JBUWRQLE9BQU8sQ0FBQ0MsR0FBRyw4Q0FBQVMsTUFBQSxDQUFvQ3ZCLFFBQVEsQ0FBQ2lDLE1BQU0sWUFBQVYsTUFBQSxDQUFTdkIsUUFBUSxDQUFDa0MsRUFBRSxDQUFFLENBQUM7d0JBRXJGO3dCQUFBekIsUUFBQSxDQUFBRSxJQUFBO3dCQUFBLE9BQzJCWCxRQUFRLENBQUNtQyxJQUFJLENBQUMsQ0FBQztvQkFBQTt3QkFBcENsQyxZQUFZLEdBQUFRLFFBQUEsQ0FBQVcsSUFBQTt3QkFDbEJQLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLCtDQUErQyxFQUFFYixZQUFZLENBQUNtQyxTQUFTLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxDQUFDO3dCQUU1Rjt3QkFBQTNCLFFBQUEsQ0FBQUMsSUFBQTt3QkFHRVIsTUFBTSxHQUFHNkIsSUFBSSxDQUFDTSxLQUFLLENBQUNwQyxZQUFZLENBQUM7d0JBQ2pDWSxPQUFPLENBQUNDLEdBQUcsQ0FBQyw4QkFBOEIsQ0FBQzt3QkFBQ0wsUUFBQSxDQUFBRSxJQUFBO3dCQUFBO29CQUFBO3dCQUFBRixRQUFBLENBQUFDLElBQUE7d0JBQUFELFFBQUEsQ0FBQTZCLEVBQUEsR0FBQTdCLFFBQUE7d0JBRTVDSSxPQUFPLENBQUNwQyxLQUFLLENBQUMsb0NBQW9DLEVBQUFnQyxRQUFBLENBQUE2QixFQUFZLENBQUM7d0JBQy9EekIsT0FBTyxDQUFDcEMsS0FBSyxDQUFDLHdCQUF3QixFQUFFd0IsWUFBWSxDQUFDO3dCQUFDLE1BQ2hELElBQUlzQyxLQUFLLG1DQUFBaEIsTUFBQSxDQUFtQ3RCLFlBQVksQ0FBQ21DLFNBQVMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLFFBQUssQ0FBQztvQkFBQTt3QkFBQSxJQUduRnBDLFFBQVEsQ0FBQ2tDLEVBQUU7NEJBQUF6QixRQUFBLENBQUFFLElBQUE7NEJBQUE7d0JBQUE7d0JBQ2RFLE9BQU8sQ0FBQ3BDLEtBQUssQ0FBQyxpQ0FBaUMsRUFBRXlCLE1BQU0sQ0FBQzt3QkFBQyxNQUNuRCxJQUFJcUMsS0FBSyxDQUFDckMsTUFBTSxDQUFDc0MsUUFBUSxJQUFJdEMsTUFBTSxDQUFDekIsS0FBSyxJQUFJLDZCQUE2QixDQUFDO29CQUFBO3dCQUduRm9DLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlCQUF5QixFQUFFOzRCQUNyQzJCLFNBQVMsRUFBRSxDQUFDLENBQUN2QyxNQUFNLENBQUNBLE1BQU07NEJBQzFCd0MsWUFBWSxFQUFFLEVBQUE5QyxjQUFBLEdBQUFNLE1BQU0sQ0FBQ0EsTUFBQUEsTUFBTSxRQUFBTixjQUFBLHVCQUFiQSxjQUFBLENBQWVuRSxNQUFNLEtBQUksQ0FBQzs0QkFDeENrSCxhQUFhLEVBQUUsRUFBQTlDLGVBQUEsR0FBQUssTUFBTSxDQUFDQSxNQUFBQSxNQUFNLFFBQUFMLGVBQUEsdUJBQWJBLGVBQUEsQ0FBZXVDLFNBQVMsQ0FBQyxDQUFDLEVBQUUsSUFBRyxDQUFDLElBQUk7d0JBQ3JELENBQUMsQ0FBQzt3QkFFSWpDLGNBQWMsR0FBR0QsTUFBTSxDQUFDQSxNQUFNO3dCQUFBLElBRS9CQyxjQUFjOzRCQUFBTSxRQUFBLENBQUFFLElBQUE7NEJBQUE7d0JBQUE7d0JBQ2pCRSxPQUFPLENBQUNwQyxLQUFLLENBQUMsdUNBQXVDLENBQUM7d0JBQUMsTUFDakQsSUFBSThELEtBQUssQ0FBQyxxQ0FBcUMsQ0FBQztvQkFBQTt3QkFHeEQxQixPQUFPLENBQUNDLEdBQUcsQ0FBQyw2Q0FBNkMsRUFBRVgsY0FBYyxDQUFDMUUsTUFBTSxDQUFDO3dCQUNqRjZCLGtCQUFrQixDQUFDNkMsY0FBYyxDQUFDO3dCQUVsQzt3QkFDQVUsT0FBTyxDQUFDQyxHQUFHLENBQUMscUNBQXFDLENBQUM7d0JBQUNMLFFBQUEsQ0FBQUUsSUFBQTt3QkFBQSxPQUMzQnZFLDhFQUFjLENBQ3BDbUMsU0FBUyxDQUFDeUMsRUFBRSxjQUFBTyxNQUFBLENBQ0FoRCxTQUFTLENBQUNHLE1BQU0sR0FDNUJ5QixjQUFjLEVBQ2RWLElBQUksQ0FBQzVDLGFBQ1AsQ0FBQztvQkFBQTt3QkFMS3VELFNBQVMsR0FBQUssUUFBQSxDQUFBVyxJQUFBO3dCQUFBLEtBT1hoQixTQUFTOzRCQUFBSyxRQUFBLENBQUFFLElBQUE7NEJBQUE7d0JBQUE7d0JBQ1hFLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHlDQUF5QyxFQUFFVixTQUFTLENBQUM7d0JBQ2pFakUsa0RBQUssQ0FBQ3lHLE9BQU8sQ0FBQywwQ0FBMEMsQ0FBQzt3QkFDekR6RixrQkFBa0IsYUFBbEJBLGtCQUFrQixlQUFsQkEsa0JBQWtCLENBQUdpRCxTQUFTLENBQUM7d0JBQy9CL0IsS0FBSyxDQUFDLENBQUM7d0JBQUNvQyxRQUFBLENBQUFFLElBQUE7d0JBQUE7b0JBQUE7d0JBRVJFLE9BQU8sQ0FBQ3BDLEtBQUssQ0FBQyxrREFBa0QsQ0FBQzt3QkFBQyxNQUM1RCxJQUFJOEQsS0FBSyxDQUFDLGlEQUFpRCxDQUFDO29CQUFBO3dCQUFBOUIsUUFBQSxDQUFBRSxJQUFBO3dCQUFBO29CQUFBO3dCQUFBRixRQUFBLENBQUFDLElBQUE7d0JBQUFELFFBQUEsQ0FBQW9DLEVBQUEsR0FBQXBDLFFBQUE7d0JBSXBFSSxPQUFPLENBQUNwQyxLQUFLLENBQUMsb0NBQW9DLEVBQUFnQyxRQUFBLENBQUFvQyxFQUFPLENBQUM7d0JBRTFEO3dCQUNJeEMsWUFBWSxHQUFHLDZCQUE2Qjt3QkFFaEQsSUFBSUksUUFBQSxDQUFBb0MsRUFBQSxZQUFpQk4sS0FBSyxFQUFFOzRCQUMxQmxDLFlBQVksR0FBR0ksUUFBQSxDQUFBb0MsRUFBQSxDQUFNQyxPQUFPO3dCQUM5QixDQUFDLE1BQU0sSUFBSSxPQUFBckMsUUFBQSxDQUFBb0MsRUFBWSxLQUFLLFFBQVEsSUFBSXBDLFFBQUEsQ0FBQW9DLEVBQUEsS0FBVSxJQUFJLEVBQUU7NEJBQ3REOzRCQUNNdkMsUUFBUSxHQUFBRyxRQUFBLENBQUFvQyxFQUFBOzRCQUNkLElBQUl2QyxRQUFRLENBQUN3QyxPQUFPLEVBQUU7Z0NBQ3BCekMsWUFBWSxHQUFHQyxRQUFRLENBQUN3QyxPQUFPOzRCQUNqQyxDQUFDLE1BQU0sSUFBSXhDLFFBQVEsQ0FBQzdCLEtBQUssRUFBRTtnQ0FDekI0QixZQUFZLEdBQUdDLFFBQVEsQ0FBQzdCLEtBQUs7NEJBQy9CLENBQUMsTUFBTSxJQUFJNkIsUUFBUSxDQUFDa0MsUUFBUSxFQUFFO2dDQUM1Qm5DLFlBQVksR0FBR0MsUUFBUSxDQUFDa0MsUUFBUTs0QkFDbEMsQ0FBQyxNQUFNO2dDQUNMbkMsWUFBWSwwQkFBQWtCLE1BQUEsQ0FBMEJRLElBQUksQ0FBQ0MsU0FBUyxDQUFBdkIsUUFBQSxDQUFBb0MsRUFBTSxDQUFDLENBQUU7NEJBQy9EO3dCQUNGLENBQUMsTUFBTSxJQUFJLE9BQUFwQyxRQUFBLENBQUFvQyxFQUFZLEtBQUssUUFBUSxFQUFFOzRCQUNwQ3hDLFlBQVksR0FBQUksUUFBQSxDQUFBb0MsRUFBUTt3QkFDdEI7d0JBRUFoQyxPQUFPLENBQUNwQyxLQUFLLENBQUMsNkJBQTZCLEVBQUU0QixZQUFZLENBQUM7d0JBQzFEbEUsa0RBQUssQ0FBQ3NDLEtBQUssQ0FBQzRCLFlBQVksQ0FBQztvQkFBQzt3QkFBQUksUUFBQSxDQUFBQyxJQUFBO3dCQUUxQkssZUFBZSxDQUFDLEtBQUssQ0FBQzt3QkFDdEJ0RCxpQkFBaUIsQ0FBQyxDQUFDLENBQUM7d0JBQUMsT0FBQWdELFFBQUEsQ0FBQXNDLE1BQUE7b0JBQUE7b0JBQUE7d0JBQUEsT0FBQXRDLFFBQUEsQ0FBQXVDLElBQUE7Z0JBQUE7WUFBQSxHQUFBeEQsT0FBQTtnQkFBQTtvQkFBQTtvQkFBQTtvQkFBQTtvQkFBQTtpQkFBQTtnQkFBQTtvQkFBQTtvQkFBQTtpQkFBQTthQUFBO1FBQUEsQ0FFeEI7UUFBQSxnQkE1SUtILFFBQVFBLENBQUE0RCxFQUFBO1lBQUEsT0FBQTNELEtBQUEsQ0FBQWhFLEtBQUEsT0FBQUUsU0FBQTtRQUFBO0lBQUEsR0E0SWI7SUFFRCxxQkFDRWdCLDZEQUFBO1FBQUswRyxTQUFTLEVBQUMsV0FBVztRQUFBQyxRQUFBO1lBQUEsY0FFeEIzRyw2REFBQTtnQkFBSzBHLFNBQVMsRUFBQyxrREFBa0Q7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDL0QzRyw2REFBQTt3QkFBSTBHLFNBQVMsRUFBQyxrQ0FBa0M7d0JBQUFDLFFBQUEsRUFBQztvQkFBMEI7d0JBQUFDLFFBQUEsRUFBQTdJLFlBQUE7d0JBQUE4SSxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLE9BQUksQ0FBQztvQkFFL0UsQ0FBQ25FLFlBQVksaUJBQ1ozQyw2REFBQTt3QkFBSzBHLFNBQVMsRUFBQyxjQUFjO3dCQUFBQyxRQUFBOzRCQUFBLGNBQzNCM0csNkRBQUE7Z0NBQUcwRyxTQUFTLEVBQUMsYUFBYTtnQ0FBQUMsUUFBQSxFQUFDOzRCQUF1QjtnQ0FBQUMsUUFBQSxFQUFBN0ksWUFBQTtnQ0FBQThJLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsT0FBRyxDQUFDOzRCQUFBLGNBQ3REOUcsNkRBQUE7Z0NBQUcwRyxTQUFTLEVBQUMsY0FBYztnQ0FBQUMsUUFBQSxFQUN4QmpHLHVCQUF1QixDQUFDekIsTUFBTSxLQUFLLENBQUMsR0FDakMscUVBQXFFLGFBQUE4RixNQUFBLENBQzNEckUsdUJBQXVCLENBQUN6QixNQUFNOzRCQUF5RTtnQ0FBQTJILFFBQUEsRUFBQTdJLFlBQUE7Z0NBQUE4SSxVQUFBO2dDQUFBQyxZQUFBOzRCQUFBLE9BRXBILENBQUM7eUJBQUE7b0JBQUE7d0JBQUFGLFFBQUEsRUFBQTdJLFlBQUE7d0JBQUE4SSxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLE9BQ0QsQ0FBQyxHQUNKLENBQUNsRSxtQkFBbUIsQ0FBQ1osTUFBTSxpQkFDN0JoQyw2REFBQTt3QkFBSzBHLFNBQVMsRUFBQyxjQUFjO3dCQUFBQyxRQUFBOzRCQUFBLGNBQzNCM0csNkRBQUE7Z0NBQUcwRyxTQUFTLEVBQUMsYUFBYTtnQ0FBQUMsUUFBQSxFQUFDOzRCQUFzQjtnQ0FBQUMsUUFBQSxFQUFBN0ksWUFBQTtnQ0FBQThJLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsT0FBRyxDQUFDOzRCQUFBLGNBQ3JEOUcsNkRBQUE7Z0NBQUcwRyxTQUFTLEVBQUMsY0FBYztnQ0FBQUMsUUFBQSxFQUFFL0QsbUJBQW1CLENBQUNYLEtBQUFBOzRCQUFLO2dDQUFBMkUsUUFBQSxFQUFBN0ksWUFBQTtnQ0FBQThJLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsT0FBSSxDQUFDO3lCQUFBO29CQUFBO3dCQUFBRixRQUFBLEVBQUE3SSxZQUFBO3dCQUFBOEksVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxPQUN4RCxDQUFDLGlCQUVOOUcsNkRBQUE7d0JBQUswRyxTQUFTLEVBQUMsZUFBZTt3QkFBQUMsUUFBQTs0QkFBQSxjQUM1QjNHLDZEQUFBO2dDQUFHMEcsU0FBUyxFQUFDLGFBQWE7Z0NBQUFDLFFBQUEsRUFBQzs0QkFBeUI7Z0NBQUFDLFFBQUEsRUFBQTdJLFlBQUE7Z0NBQUE4SSxVQUFBO2dDQUFBQyxZQUFBOzRCQUFBLE9BQUcsQ0FBQzs0QkFBQSxjQUN4RDlHLDZEQUFBO2dDQUFHMEcsU0FBUyxFQUFDLGNBQWM7Z0NBQUFDLFFBQUE7b0NBQUEsY0FDekIzRyw2REFBQTt3Q0FBQTJHLFFBQUEsRUFBUzVFLFNBQVMsQ0FBQ0csTUFBQUE7b0NBQU07d0NBQUEwRSxRQUFBLEVBQUE3SSxZQUFBO3dDQUFBOEksVUFBQTt3Q0FBQUMsWUFBQTtvQ0FBQSxPQUFTLENBQUM7b0NBQ2xDL0UsU0FBUyxDQUFDMkMsV0FBVyxjQUFBSyxNQUFBLENBQWNoRCxTQUFTLENBQUMyQyxXQUFXLE1BQUc7aUNBQUE7NEJBQUE7Z0NBQUFrQyxRQUFBLEVBQUE3SSxZQUFBO2dDQUFBOEksVUFBQTtnQ0FBQUMsWUFBQTs0QkFBQSxPQUMzRCxDQUFDOzRCQUFBLGNBQ0o5Ryw2REFBQTtnQ0FBRzBHLFNBQVMsRUFBQyw0QkFBNEI7Z0NBQUFDLFFBQUE7b0NBQUMsY0FDNUI7b0NBQUM1RSxTQUFTLENBQUNLLFNBQVMsQ0FBQ0csS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDdEQsTUFBTTtvQ0FBQyxXQUN2RDtpQ0FBQTs0QkFBQTtnQ0FBQTJILFFBQUEsRUFBQTdJLFlBQUE7Z0NBQUE4SSxVQUFBO2dDQUFBQyxZQUFBOzRCQUFBLE9BQUcsQ0FBQzt5QkFBQTtvQkFBQTt3QkFBQUYsUUFBQSxFQUFBN0ksWUFBQTt3QkFBQThJLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsT0FDRCxDQUNOO2lCQUFBO1lBQUE7Z0JBQUFGLFFBQUEsRUFBQTdJLFlBQUE7Z0JBQUE4SSxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsT0FDRSxDQUFDO1lBR0xuRSxZQUFZLElBQUlDLG1CQUFtQixDQUFDWixNQUFNLGtCQUN6Q2hDLDZEQUFBO2dCQUFNNkMsUUFBUSxFQUFFbkIsWUFBWSxDQUFDbUIsUUFBUSxDQUFFO2dCQUFDNkQsU0FBUyxFQUFDLFdBQVc7Z0JBQUFDLFFBQUE7b0JBQUEsY0FDM0QzRyw2REFBQTt3QkFBQTJHLFFBQUE7NEJBQUEsY0FDRTNHLDZEQUFBO2dDQUFPK0csT0FBTyxFQUFDLGVBQWU7Z0NBQUNMLFNBQVMsRUFBQyw0Q0FBNEM7Z0NBQUFDLFFBQUEsRUFBQzs0QkFFdEY7Z0NBQUFDLFFBQUEsRUFBQTdJLFlBQUE7Z0NBQUE4SSxVQUFBO2dDQUFBQyxZQUFBOzRCQUFBLE9BQU8sQ0FBQzs0QkFBQSxjQUNSOUcsNkRBQUEsYUFBQWpCLGFBQUEsQ0FBQUEsYUFBQTtnQ0FDRXlGLEVBQUUsRUFBQzs0QkFBZSxHQUNkL0MsUUFBUSxDQUFDLGVBQWUsQ0FBQztnQ0FDN0J1RixRQUFRLEVBQUU1RixZQUFhO2dDQUN2QjZGLElBQUksRUFBRSxDQUFFO2dDQUNSUCxTQUFTLEVBQUMsNElBQTRJO2dDQUN0SlEsV0FBVyxFQUFDOzRCQUE2QztnQ0FBQU4sUUFBQSxFQUFBN0ksWUFBQTtnQ0FBQThJLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsT0FDMUQsQ0FBQzs0QkFDRG5GLE1BQU0sQ0FBQ3RCLGFBQWEsa0JBQ25CTCw2REFBQTtnQ0FBTTBHLFNBQVMsRUFBQyxzQkFBc0I7Z0NBQUFDLFFBQUEsRUFBRWhGLE1BQU0sQ0FBQ3RCLGFBQWEsQ0FBQ2lHLE9BQUFBOzRCQUFPO2dDQUFBTSxRQUFBLEVBQUE3SSxZQUFBO2dDQUFBOEksVUFBQTtnQ0FBQUMsWUFBQTs0QkFBQSxPQUFPLENBQzVFOzRCQUFBLGNBQ0Q5Ryw2REFBQTtnQ0FBRzBHLFNBQVMsRUFBQyw0QkFBNEI7Z0NBQUFDLFFBQUEsRUFBQzs0QkFFMUM7Z0NBQUFDLFFBQUEsRUFBQTdJLFlBQUE7Z0NBQUE4SSxVQUFBO2dDQUFBQyxZQUFBOzRCQUFBLE9BQUcsQ0FBQzt5QkFBQTtvQkFBQTt3QkFBQUYsUUFBQSxFQUFBN0ksWUFBQTt3QkFBQThJLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsT0FDRCxDQUFDO29CQUFBLGNBRU45Ryw2REFBQTt3QkFDRW1ILElBQUksRUFBQyxRQUFRO3dCQUNiSCxRQUFRLEVBQUU1RixZQUFhO3dCQUN2QnNGLFNBQVMsRUFBQyx1SkFBdUo7d0JBQUFDLFFBQUEsRUFFaEt2RixZQUFZLGlCQUNYcEIsNkRBQUEsQ0FBQUUsMkRBQUE7NEJBQUF5RyxRQUFBO2dDQUFBLGNBQ0UzRyw2REFBQTtvQ0FBSzBHLFNBQVMsRUFBQyw0Q0FBNEM7b0NBQUNVLEtBQUssRUFBQyw0QkFBNEI7b0NBQUNDLElBQUksRUFBQyxNQUFNO29DQUFDQyxPQUFPLEVBQUMsV0FBVztvQ0FBQVgsUUFBQTt3Q0FBQSxjQUM1SDNHLDZEQUFBOzRDQUFRMEcsU0FBUyxFQUFDLFlBQVk7NENBQUNhLEVBQUUsRUFBQyxJQUFJOzRDQUFDQyxFQUFFLEVBQUMsSUFBSTs0Q0FBQ3BKLENBQUMsRUFBQyxJQUFJOzRDQUFDcUosTUFBTSxFQUFDLGNBQWM7NENBQUNDLFdBQVcsRUFBQzt3Q0FBRzs0Q0FBQWQsUUFBQSxFQUFBN0ksWUFBQTs0Q0FBQThJLFVBQUE7NENBQUFDLFlBQUE7d0NBQUEsT0FBUyxDQUFDO3dDQUFBLGNBQ3JHOUcsNkRBQUE7NENBQU0wRyxTQUFTLEVBQUMsWUFBWTs0Q0FBQ1csSUFBSSxFQUFDLGNBQWM7NENBQUNNLENBQUMsRUFBQzt3Q0FBaUg7NENBQUFmLFFBQUEsRUFBQTdJLFlBQUE7NENBQUE4SSxVQUFBOzRDQUFBQyxZQUFBO3dDQUFBLE9BQU8sQ0FBQztxQ0FBQTtnQ0FBQTtvQ0FBQUYsUUFBQSxFQUFBN0ksWUFBQTtvQ0FBQThJLFVBQUE7b0NBQUFDLFlBQUE7Z0NBQUEsT0FDekssQ0FBQztnQ0FBQSxzQkFFTjtnQ0FBQzlGLGNBQWMsR0FBRyxDQUFDLFVBQUErRCxNQUFBLENBQVUvRCxjQUFjLE9BQUk7NkJBQUE7d0JBQUEsZUFDL0MsQ0FBQyxpQkFFSGhCLDZEQUFBLENBQUFFLDJEQUFBOzRCQUFBeUcsUUFBQTtnQ0FBQSxjQUNFM0csNkRBQUE7b0NBQUtvSCxLQUFLLEVBQUMsNEJBQTRCO29DQUFDVixTQUFTLEVBQUMsY0FBYztvQ0FBQ1ksT0FBTyxFQUFDLFdBQVc7b0NBQUNELElBQUksRUFBQyxjQUFjO29DQUFBVixRQUFBO3dDQUFBLGNBQ3RHM0csNkRBQUE7NENBQU00SCxRQUFRLEVBQUMsU0FBUzs0Q0FBQ0QsQ0FBQyxFQUFDLGdGQUFnRjs0Q0FBQ0UsUUFBUSxFQUFDO3dDQUFTOzRDQUFBakIsUUFBQSxFQUFBN0ksWUFBQTs0Q0FBQThJLFVBQUE7NENBQUFDLFlBQUE7d0NBQUEsT0FBRSxDQUFDO3dDQUFBLGNBQ2pJOUcsNkRBQUE7NENBQU00SCxRQUFRLEVBQUMsU0FBUzs0Q0FBQ0QsQ0FBQyxFQUFDLDZJQUE2STs0Q0FBQ0UsUUFBUSxFQUFDO3dDQUFTOzRDQUFBakIsUUFBQSxFQUFBN0ksWUFBQTs0Q0FBQThJLFVBQUE7NENBQUFDLFlBQUE7d0NBQUEsT0FBRSxDQUFDO3FDQUFBO2dDQUFBO29DQUFBRixRQUFBLEVBQUE3SSxZQUFBO29DQUFBOEksVUFBQTtvQ0FBQUMsWUFBQTtnQ0FBQSxPQUMzTCxDQUFDO2dDQUFBLGlCQUVSOzZCQUFBO3dCQUFBLGVBQUU7b0JBQ0g7d0JBQUFGLFFBQUEsRUFBQTdJLFlBQUE7d0JBQUE4SSxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLE9BQ0ssQ0FBQztvQkFFUjFGLFlBQVksa0JBQ1hwQiw2REFBQTt3QkFBSzBHLFNBQVMsRUFBQyxzREFBc0Q7d0JBQUFDLFFBQUEsZ0JBQ25FM0csNkRBQUE7NEJBQUcwRyxTQUFTLEVBQUMseUJBQXlCOzRCQUFBQyxRQUFBO2dDQUFBLGNBQ3BDM0csNkRBQUE7b0NBQUEyRyxRQUFBLEVBQVE7Z0NBQXNCO29DQUFBQyxRQUFBLEVBQUE3SSxZQUFBO29DQUFBOEksVUFBQTtvQ0FBQUMsWUFBQTtnQ0FBQSxPQUFRLENBQUM7Z0NBQUEsY0FBQTlHLDZEQUFBO29DQUFBNEcsUUFBQSxFQUFBN0ksWUFBQTtvQ0FBQThJLFVBQUE7b0NBQUFDLFlBQUE7Z0NBQUEsT0FBSyxDQUFDO2dDQUFBLHFHQUVuQjtnQ0FBQzlGLGNBQWMsR0FBRyxDQUFDLHNCQUFBK0QsTUFBQSxDQUFzQi9ELGNBQWMsaUJBQWMsZUFBZTtnQ0FBQyxHQUNqSDs2QkFBQTt3QkFBQTs0QkFBQTRGLFFBQUEsRUFBQTdJLFlBQUE7NEJBQUE4SSxVQUFBOzRCQUFBQyxZQUFBO3dCQUFBLE9BQUc7b0JBQUM7d0JBQUFGLFFBQUEsRUFBQTdJLFlBQUE7d0JBQUE4SSxVQUFBO3dCQUFBQyxZQUFBO29CQUFBLE9BQ0QsQ0FDTjtpQkFBQTtZQUFBO2dCQUFBRixRQUFBLEVBQUE3SSxZQUFBO2dCQUFBOEksVUFBQTtnQkFBQUMsWUFBQTtZQUFBLE9BQ0csQ0FDUDtZQUdBakcsZUFBZSxrQkFDZGIsNkRBQUE7Z0JBQUswRyxTQUFTLEVBQUMsTUFBTTtnQkFBQUMsUUFBQTtvQkFBQSxjQUNuQjNHLDZEQUFBO3dCQUFJMEcsU0FBUyxFQUFDLDRCQUE0Qjt3QkFBQUMsUUFBQSxFQUFDO29CQUFtQjt3QkFBQUMsUUFBQSxFQUFBN0ksWUFBQTt3QkFBQThJLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsT0FBSSxDQUFDO29CQUFBLGNBQ25FOUcsNkRBQUE7d0JBQUswRyxTQUFTLEVBQUMsMkVBQTJFO3dCQUFBQyxRQUFBLGdCQUN4RjNHLDZEQUFBOzRCQUNFMEcsU0FBUyxFQUFDLDJCQUEyQjs0QkFDckNvQix1QkFBdUIsRUFBRTtnQ0FDdkJDLE1BQU0sRUFBRWxILGVBQWUsQ0FBQ21ILE9BQU8sQ0FBQyxLQUFLLEVBQUUsUUFBUTs0QkFDakQ7d0JBQUU7NEJBQUFwQixRQUFBLEVBQUE3SSxZQUFBOzRCQUFBOEksVUFBQTs0QkFBQUMsWUFBQTt3QkFBQSxPQUNIO29CQUFDO3dCQUFBRixRQUFBLEVBQUE3SSxZQUFBO3dCQUFBOEksVUFBQTt3QkFBQUMsWUFBQTtvQkFBQSxPQUNDLENBQUM7b0JBQUEsY0FDTjlHLDZEQUFBO3dCQUFHMEcsU0FBUyxFQUFDLDRCQUE0Qjt3QkFBQUMsUUFBQSxFQUFDO29CQUUxQzt3QkFBQUMsUUFBQSxFQUFBN0ksWUFBQTt3QkFBQThJLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsT0FBRyxDQUFDO2lCQUFBO1lBQUE7Z0JBQUFGLFFBQUEsRUFBQTdJLFlBQUE7Z0JBQUE4SSxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsT0FDRCxDQUNOO1NBQUE7SUFBQTtRQUFBRixRQUFBLEVBQUE3SSxZQUFBO1FBQUE4SSxVQUFBO1FBQUFDLFlBQUE7SUFBQSxPQUNFLENBQUM7QUFFVjs7Ozs7O1FBdFQyRGhILG1GQUF1QixDQUFDOzs7OztRQU83RU4scURBQU87OztNQVhXZ0IsZ0JBQWdCQTtBQTBUdkN4QyxFQUFBLEVBMVR1QndDLGdCQUFnQjtJQUFBO1FBSW1CVixtRkFBdUI7UUFPNUVOLHFEQUFPO0tBQUE7QUFBQTtBQUFBeUksRUFBQSxHQVhXekgsZ0JBQWdCO0FBQUEsSUFBQXlILEVBQUE7QUFBQUMsWUFBQSxDQUFBRCxFQUFBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcZmVhdHVyZXNcXHN1bW1hcmllc1xcY29tcG9uZW50c1xcU3VtbWFyeUdlbmVyYXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlRm9ybSB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSc7XG5pbXBvcnQgeyB6b2RSZXNvbHZlciB9IGZyb20gJ0Bob29rZm9ybS9yZXNvbHZlcnMvem9kJztcbmltcG9ydCB7IHogfSBmcm9tICd6b2QnO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnO1xuaW1wb3J0IHsgRG9jdW1lbnRvIH0gZnJvbSAnQC9saWIvc3VwYWJhc2Uvc3VwYWJhc2VDbGllbnQnO1xuaW1wb3J0IHsgZ3VhcmRhclJlc3VtZW4sIGV4aXN0ZVJlc3VtZW5QYXJhRG9jdW1lbnRvIH0gZnJvbSAnQC9saWIvc3VwYWJhc2UvcmVzdW1lbmVzU2VydmljZSc7XG5pbXBvcnQgeyB1c2VCYWNrZ3JvdW5kR2VuZXJhdGlvbiB9IGZyb20gJ0AvaG9va3MvdXNlQmFja2dyb3VuZEdlbmVyYXRpb24nO1xuXG5jb25zdCBzdW1tYXJ5U2NoZW1hID0gei5vYmplY3Qoe1xuICBpbnN0cnVjY2lvbmVzOiB6LnN0cmluZygpLm1pbigxMCwgJ0xhcyBpbnN0cnVjY2lvbmVzIGRlYmVuIHRlbmVyIGFsIG1lbm9zIDEwIGNhcmFjdGVyZXMnKSxcbn0pO1xuXG50eXBlIFN1bW1hcnlGb3JtRGF0YSA9IHouaW5mZXI8dHlwZW9mIHN1bW1hcnlTY2hlbWE+O1xuXG5pbnRlcmZhY2UgU3VtbWFyeUdlbmVyYXRvclByb3BzIHtcbiAgZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3M6IERvY3VtZW50b1tdO1xuICBvblN1bW1hcnlHZW5lcmF0ZWQ/OiAoc3VtbWFyeUlkOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFN1bW1hcnlHZW5lcmF0b3IoeyBkb2N1bWVudG9zU2VsZWNjaW9uYWRvcywgb25TdW1tYXJ5R2VuZXJhdGVkIH06IFN1bW1hcnlHZW5lcmF0b3JQcm9wcykge1xuICBjb25zdCBbcmVzdW1lbkdlbmVyYWRvLCBzZXRSZXN1bWVuR2VuZXJhZG9dID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFt0aWVtcG9Fc3RpbWFkbywgc2V0VGllbXBvRXN0aW1hZG9dID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcblxuICBjb25zdCB7IGdlbmVyYXRlUmVzdW1lbiwgaXNHZW5lcmF0aW5nLCBnZXRBY3RpdmVUYXNrIH0gPSB1c2VCYWNrZ3JvdW5kR2VuZXJhdGlvbigpO1xuXG4gIGNvbnN0IHtcbiAgICByZWdpc3RlcixcbiAgICBoYW5kbGVTdWJtaXQsXG4gICAgZm9ybVN0YXRlOiB7IGVycm9ycyB9LFxuICAgIHJlc2V0XG4gIH0gPSB1c2VGb3JtPFN1bW1hcnlGb3JtRGF0YT4oe1xuICAgIHJlc29sdmVyOiB6b2RSZXNvbHZlcihzdW1tYXJ5U2NoZW1hKSxcbiAgICBkZWZhdWx0VmFsdWVzOiB7XG4gICAgICBpbnN0cnVjY2lvbmVzOiAnQ3JlYSB1biByZXN1bWVuIGNvbXBsZXRvIHkgZXN0cnVjdHVyYWRvIGRlbCB0ZW1hLCBvcmdhbml6YW5kbyBsb3MgY29uY2VwdG9zIHByaW5jaXBhbGVzIGRlIG1hbmVyYSBjbGFyYSB5IGRpZMOhY3RpY2EuJ1xuICAgIH1cbiAgfSk7XG5cbiAgLy8gRnVuY2lvbmVzIGRlIHZhbGlkYWNpw7NuXG4gIGNvbnN0IHZhbGlkYXJEb2N1bWVudG9QYXJhUmVzdW1lbiA9IChkb2N1bWVudG86IGFueSk6IHsgdmFsaWRvOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9ID0+IHtcbiAgICBpZiAoIWRvY3VtZW50bykge1xuICAgICAgcmV0dXJuIHsgdmFsaWRvOiBmYWxzZSwgZXJyb3I6ICdObyBzZSBoYSBwcm9wb3JjaW9uYWRvIG5pbmfDum4gZG9jdW1lbnRvJyB9O1xuICAgIH1cblxuICAgIGlmICghZG9jdW1lbnRvLnRpdHVsbyB8fCBkb2N1bWVudG8udGl0dWxvLnRyaW0oKS5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiB7IHZhbGlkbzogZmFsc2UsIGVycm9yOiAnRWwgZG9jdW1lbnRvIGRlYmUgdGVuZXIgdW4gdMOtdHVsbycgfTtcbiAgICB9XG5cbiAgICBpZiAoIWRvY3VtZW50by5jb250ZW5pZG8gfHwgZG9jdW1lbnRvLmNvbnRlbmlkby50cmltKCkubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4geyB2YWxpZG86IGZhbHNlLCBlcnJvcjogJ0VsIGRvY3VtZW50byBkZWJlIHRlbmVyIGNvbnRlbmlkbycgfTtcbiAgICB9XG5cbiAgICBpZiAoZG9jdW1lbnRvLmNvbnRlbmlkby50cmltKCkubGVuZ3RoIDwgNTApIHtcbiAgICAgIHJldHVybiB7IHZhbGlkbzogZmFsc2UsIGVycm9yOiAnRWwgY29udGVuaWRvIGRlbCBkb2N1bWVudG8gZXMgZGVtYXNpYWRvIGNvcnRvIHBhcmEgZ2VuZXJhciB1biByZXN1bWVuIMO6dGlsJyB9O1xuICAgIH1cblxuICAgIHJldHVybiB7IHZhbGlkbzogdHJ1ZSB9O1xuICB9O1xuXG4gIGNvbnN0IGVzdGltYXJUaWVtcG9HZW5lcmFjaW9uID0gKGRvY3VtZW50bzogYW55KTogbnVtYmVyID0+IHtcbiAgICBpZiAoIWRvY3VtZW50byB8fCAhZG9jdW1lbnRvLmNvbnRlbmlkbykge1xuICAgICAgcmV0dXJuIDMwOyAvLyAzMCBzZWd1bmRvcyBwb3IgZGVmZWN0b1xuICAgIH1cblxuICAgIGNvbnN0IHBhbGFicmFzID0gZG9jdW1lbnRvLmNvbnRlbmlkby5zcGxpdCgvXFxzKy8pLmxlbmd0aDtcblxuICAgIC8vIEVzdGltYWNpw7NuOiB+MSBzZWd1bmRvIHBvciBjYWRhIDEwMCBwYWxhYnJhcywgbcOtbmltbyAxNSBzZWd1bmRvcywgbcOheGltbyAxMjAgc2VndW5kb3NcbiAgICBjb25zdCB0aWVtcG9Fc3RpbWFkbyA9IE1hdGgubWF4KDE1LCBNYXRoLm1pbigxMjAsIE1hdGguY2VpbChwYWxhYnJhcyAvIDEwMCkpKTtcblxuICAgIHJldHVybiB0aWVtcG9Fc3RpbWFkbztcbiAgfTtcblxuICAvLyBWYWxpZGFjaW9uZXNcbiAgY29uc3QgcHVlZGVHZW5lcmFyID0gZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MubGVuZ3RoID09PSAxO1xuICBjb25zdCBkb2N1bWVudG8gPSBkb2N1bWVudG9zU2VsZWNjaW9uYWRvc1swXTtcblxuICBjb25zdCB2YWxpZGFjaW9uRG9jdW1lbnRvID0gZG9jdW1lbnRvID8gdmFsaWRhckRvY3VtZW50b1BhcmFSZXN1bWVuKGRvY3VtZW50bykgOiB7IHZhbGlkbzogZmFsc2UsIGVycm9yOiAnTm8gaGF5IGRvY3VtZW50byBzZWxlY2Npb25hZG8nIH07XG5cbiAgY29uc3Qgb25TdWJtaXQgPSBhc3luYyAoZGF0YTogU3VtbWFyeUZvcm1EYXRhKSA9PiB7XG4gICAgaWYgKCFwdWVkZUdlbmVyYXIgfHwgIWRvY3VtZW50byB8fCAhdmFsaWRhY2lvbkRvY3VtZW50by52YWxpZG8pIHtcbiAgICAgIHRvYXN0LmVycm9yKCdObyBzZSBwdWVkZSBnZW5lcmFyIGVsIHJlc3VtZW4uIFZlcmlmaWNhIHF1ZSB0ZW5nYXMgZXhhY3RhbWVudGUgdW4gZG9jdW1lbnRvIHNlbGVjY2lvbmFkby4nKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CfmoAgSW5pY2lhbmRvIGdlbmVyYWNpw7NuIGRlIHJlc3VtZW4uLi4nKTtcbiAgICAgIHNldElzR2VuZXJhdGluZyh0cnVlKTtcbiAgICAgIHNldFRpZW1wb0VzdGltYWRvKGVzdGltYXJUaWVtcG9HZW5lcmFjaW9uKGRvY3VtZW50bykpO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+ThCBEb2N1bWVudG8gc2VsZWNjaW9uYWRvOicsIHtcbiAgICAgICAgaWQ6IGRvY3VtZW50by5pZCxcbiAgICAgICAgdGl0dWxvOiBkb2N1bWVudG8udGl0dWxvLFxuICAgICAgICBjYXRlZ29yaWE6IGRvY3VtZW50by5jYXRlZ29yaWEsXG4gICAgICAgIG51bWVyb190ZW1hOiBkb2N1bWVudG8ubnVtZXJvX3RlbWEsXG4gICAgICAgIGNvbnRlbmlkb0xlbmd0aDogZG9jdW1lbnRvLmNvbnRlbmlkbz8ubGVuZ3RoIHx8IDBcbiAgICAgIH0pO1xuXG4gICAgICAvLyBWZXJpZmljYXIgc2kgeWEgZXhpc3RlIHVuIHJlc3VtZW4gcGFyYSBlc3RlIGRvY3VtZW50b1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gVmVyaWZpY2FuZG8gc2kgeWEgZXhpc3RlIHJlc3VtZW4uLi4nKTtcbiAgICAgIGNvbnN0IGV4aXN0ZSA9IGF3YWl0IGV4aXN0ZVJlc3VtZW5QYXJhRG9jdW1lbnRvKGRvY3VtZW50by5pZCk7XG4gICAgICBpZiAoZXhpc3RlKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfimqDvuI8gWWEgZXhpc3RlIHVuIHJlc3VtZW4gcGFyYSBlc3RlIGRvY3VtZW50bycpO1xuICAgICAgICB0b2FzdC5lcnJvcignWWEgZXhpc3RlIHVuIHJlc3VtZW4gcGFyYSBlc3RlIGRvY3VtZW50by4gU29sbyBzZSBwZXJtaXRlIHVuIHJlc3VtZW4gcG9yIHRlbWEuJyk7XG4gICAgICAgIHNldElzR2VuZXJhdGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTm8gZXhpc3RlIHJlc3VtZW4gcHJldmlvLCBjb250aW51YW5kby4uLicpO1xuXG4gICAgICAvLyBQcmVwYXJhciBkYXRvcyBwYXJhIGxhIEFQSVxuICAgICAgY29uc3QgcmVxdWVzdERhdGEgPSB7XG4gICAgICAgIGFjdGlvbjogJ2dlbmVyYXJSZXN1bWVuJyxcbiAgICAgICAgcGV0aWNpb246IGAke2RvY3VtZW50by50aXR1bG99fCR7ZG9jdW1lbnRvLmNhdGVnb3JpYSB8fCAnJ318JHtkb2N1bWVudG8ubnVtZXJvX3RlbWEgfHwgJyd9fCR7ZGF0YS5pbnN0cnVjY2lvbmVzfWAsXG4gICAgICAgIGNvbnRleHRvczogW2RvY3VtZW50by5jb250ZW5pZG9dXG4gICAgICB9O1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+ToSBFbnZpYW5kbyBwZXRpY2nDs24gYSBsYSBBUEk6Jywge1xuICAgICAgICBhY3Rpb246IHJlcXVlc3REYXRhLmFjdGlvbixcbiAgICAgICAgcGV0aWNpb246IHJlcXVlc3REYXRhLnBldGljaW9uLFxuICAgICAgICBjb250ZXh0b3NMZW5ndGg6IHJlcXVlc3REYXRhLmNvbnRleHRvcy5sZW5ndGgsXG4gICAgICAgIHByaW1lckNvbnRleHRvTGVuZ3RoOiByZXF1ZXN0RGF0YS5jb250ZXh0b3NbMF0/Lmxlbmd0aCB8fCAwXG4gICAgICB9KTtcblxuICAgICAgLy8gR2VuZXJhciBlbCByZXN1bWVuIHVzYW5kbyBsYSBBUElcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYWknLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocmVxdWVzdERhdGEpLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKGDwn5OoIFJlc3B1ZXN0YSByZWNpYmlkYSAtIFN0YXR1czogJHtyZXNwb25zZS5zdGF0dXN9LCBPSzogJHtyZXNwb25zZS5va31gKTtcblxuICAgICAgLy8gQ2FwdHVyYXIgcmVzcHVlc3RhIGNvbW8gdGV4dG8gcHJpbWVybyBwYXJhIGRlcHVyYWNpw7NuXG4gICAgICBjb25zdCByZXNwb25zZVRleHQgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ThCBSZXNwdWVzdGEgY29tbyB0ZXh0byAocHJpbWVyb3MgMjAwIGNoYXJzKTonLCByZXNwb25zZVRleHQuc3Vic3RyaW5nKDAsIDIwMCkpO1xuXG4gICAgICAvLyBJbnRlbnRhciBwYXJzZWFyIGNvbW8gSlNPTlxuICAgICAgbGV0IHJlc3VsdDtcbiAgICAgIHRyeSB7XG4gICAgICAgIHJlc3VsdCA9IEpTT04ucGFyc2UocmVzcG9uc2VUZXh0KTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSBKU09OIHBhcnNlYWRvIGV4aXRvc2FtZW50ZScpO1xuICAgICAgfSBjYXRjaCAocGFyc2VFcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgYWwgcGFyc2VhciByZXNwdWVzdGEgSlNPTjonLCBwYXJzZUVycm9yKTtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+ThCBSZXNwdWVzdGEgY29tcGxldGE6JywgcmVzcG9uc2VUZXh0KTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvciBlbiBmb3JtYXRvIGRlIHJlc3B1ZXN0YTogJHtyZXNwb25zZVRleHQuc3Vic3RyaW5nKDAsIDEwMCl9Li4uYCk7XG4gICAgICB9XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGVuIHJlc3B1ZXN0YSBkZSBsYSBBUEk6JywgcmVzdWx0KTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3VsdC5kZXRhbGxlcyB8fCByZXN1bHQuZXJyb3IgfHwgJ0Vycm9yIGFsIGdlbmVyYXIgZWwgcmVzdW1lbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZygn8J+TiyBSZXN1bHRhZG8gZGUgbGEgQVBJOicsIHtcbiAgICAgICAgaGFzUmVzdWx0OiAhIXJlc3VsdC5yZXN1bHQsXG4gICAgICAgIHJlc3VsdExlbmd0aDogcmVzdWx0LnJlc3VsdD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgIHJlc3VsdFByZXZpZXc6IHJlc3VsdC5yZXN1bHQ/LnN1YnN0cmluZygwLCAxMDApIHx8ICdTaW4gY29udGVuaWRvJ1xuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IHJlc3VtZW5Db250ZW50ID0gcmVzdWx0LnJlc3VsdDtcblxuICAgICAgaWYgKCFyZXN1bWVuQ29udGVudCkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgTm8gc2UgcmVjaWJpw7MgY29udGVuaWRvIGRlbCByZXN1bWVuJyk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTm8gc2UgcmVjaWJpw7MgY29udGVuaWRvIGRlbCByZXN1bWVuJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinIUgQ29udGVuaWRvIGRlbCByZXN1bWVuIHJlY2liaWRvLCBsb25naXR1ZDonLCByZXN1bWVuQ29udGVudC5sZW5ndGgpO1xuICAgICAgc2V0UmVzdW1lbkdlbmVyYWRvKHJlc3VtZW5Db250ZW50KTtcblxuICAgICAgLy8gR3VhcmRhciBlbiBTdXBhYmFzZVxuICAgICAgY29uc29sZS5sb2coJ/Cfkr4gR3VhcmRhbmRvIHJlc3VtZW4gZW4gU3VwYWJhc2UuLi4nKTtcbiAgICAgIGNvbnN0IHJlc3VtZW5JZCA9IGF3YWl0IGd1YXJkYXJSZXN1bWVuKFxuICAgICAgICBkb2N1bWVudG8uaWQsXG4gICAgICAgIGBSZXN1bWVuOiAke2RvY3VtZW50by50aXR1bG99YCxcbiAgICAgICAgcmVzdW1lbkNvbnRlbnQsXG4gICAgICAgIGRhdGEuaW5zdHJ1Y2Npb25lc1xuICAgICAgKTtcblxuICAgICAgaWYgKHJlc3VtZW5JZCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFJlc3VtZW4gZ3VhcmRhZG8gZXhpdG9zYW1lbnRlIGNvbiBJRDonLCByZXN1bWVuSWQpO1xuICAgICAgICB0b2FzdC5zdWNjZXNzKCdSZXN1bWVuIGdlbmVyYWRvIHkgZ3VhcmRhZG8gZXhpdG9zYW1lbnRlJyk7XG4gICAgICAgIG9uU3VtbWFyeUdlbmVyYXRlZD8uKHJlc3VtZW5JZCk7XG4gICAgICAgIHJlc2V0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgYWwgZ3VhcmRhciBlbCByZXN1bWVuIC0gbm8gc2UgcmVjaWJpw7MgSUQnKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdFcnJvciBhbCBndWFyZGFyIGVsIHJlc3VtZW4gZW4gbGEgYmFzZSBkZSBkYXRvcycpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNvbXBsZXRvIGFsIGdlbmVyYXIgcmVzdW1lbjonLCBlcnJvcik7XG5cbiAgICAgIC8vIE1hbmVqbyBtZWpvcmFkbyBkZSBlcnJvcmVzIGNvbiBtw6FzIGRldGFsbGVzXG4gICAgICBsZXQgZXJyb3JNZXNzYWdlID0gJ0Vycm9yIGFsIGdlbmVyYXIgZWwgcmVzdW1lbic7XG5cbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgIGVycm9yTWVzc2FnZSA9IGVycm9yLm1lc3NhZ2U7XG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiBlcnJvciA9PT0gJ29iamVjdCcgJiYgZXJyb3IgIT09IG51bGwpIHtcbiAgICAgICAgLy8gU2kgZXMgdW4gb2JqZXRvLCBpbnRlbnRhciBleHRyYWVyIGluZm9ybWFjacOzbiDDunRpbFxuICAgICAgICBjb25zdCBlcnJvck9iaiA9IGVycm9yIGFzIGFueTtcbiAgICAgICAgaWYgKGVycm9yT2JqLm1lc3NhZ2UpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvck9iai5tZXNzYWdlO1xuICAgICAgICB9IGVsc2UgaWYgKGVycm9yT2JqLmVycm9yKSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3JPYmouZXJyb3I7XG4gICAgICAgIH0gZWxzZSBpZiAoZXJyb3JPYmouZGV0YWxsZXMpIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvck9iai5kZXRhbGxlcztcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBgRXJyb3IgZGVsIHNlcnZpZG9yOiAke0pTT04uc3RyaW5naWZ5KGVycm9yKX1gO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHR5cGVvZiBlcnJvciA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3I7XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUuZXJyb3IoJ01lbnNhamUgZGUgZXJyb3IgcHJvY2VzYWRvOicsIGVycm9yTWVzc2FnZSk7XG4gICAgICB0b2FzdC5lcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0dlbmVyYXRpbmcoZmFsc2UpO1xuICAgICAgc2V0VGllbXBvRXN0aW1hZG8oMCk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBJbmZvcm1hY2nDs24gZGVsIGRvY3VtZW50byBzZWxlY2Npb25hZG8gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsdWUtNTAgYm9yZGVyIGJvcmRlci1ibHVlLTIwMCByb3VuZGVkLWxnIHAtNFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtOTAwIG1iLTJcIj7wn5OEIEdlbmVyYWNpw7NuIGRlIFJlc8O6bWVuZXM8L2gzPlxuICAgICAgICBcbiAgICAgICAgeyFwdWVkZUdlbmVyYXIgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+4pqg77iPIFNlbGVjY2nDs24gaW5jb3JyZWN0YTwvcD5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gbXQtMVwiPlxuICAgICAgICAgICAgICB7ZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MubGVuZ3RoID09PSAwIFxuICAgICAgICAgICAgICAgID8gJ0RlYmVzIHNlbGVjY2lvbmFyIGV4YWN0YW1lbnRlIHVuIGRvY3VtZW50byBwYXJhIGdlbmVyYXIgdW4gcmVzdW1lbi4nXG4gICAgICAgICAgICAgICAgOiBgVGllbmVzICR7ZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MubGVuZ3RofSBkb2N1bWVudG9zIHNlbGVjY2lvbmFkb3MuIFNvbG8gc2UgcGVybWl0ZSBnZW5lcmFyIHVuIHJlc3VtZW4gcG9yIHRlbWEuYFxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAhdmFsaWRhY2lvbkRvY3VtZW50by52YWxpZG8gPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC03MDBcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+4pqg77iPIERvY3VtZW50byBubyB2w6FsaWRvPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0xXCI+e3ZhbGlkYWNpb25Eb2N1bWVudG8uZXJyb3J9PC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ibHVlLTcwMFwiPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj7inIUgRG9jdW1lbnRvIHNlbGVjY2lvbmFkbzo8L3A+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG10LTFcIj5cbiAgICAgICAgICAgICAgPHN0cm9uZz57ZG9jdW1lbnRvLnRpdHVsb308L3N0cm9uZz5cbiAgICAgICAgICAgICAge2RvY3VtZW50by5udW1lcm9fdGVtYSAmJiBgIChUZW1hICR7ZG9jdW1lbnRvLm51bWVyb190ZW1hfSlgfVxuICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyBtdC0xIHRleHQtYmx1ZS02MDBcIj5cbiAgICAgICAgICAgICAgQ29udGVuaWRvOiB+e2RvY3VtZW50by5jb250ZW5pZG8uc3BsaXQoL1xccysvKS5sZW5ndGh9IHBhbGFicmFzXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEZvcm11bGFyaW8gZGUgZ2VuZXJhY2nDs24gKi99XG4gICAgICB7cHVlZGVHZW5lcmFyICYmIHZhbGlkYWNpb25Eb2N1bWVudG8udmFsaWRvICYmIChcbiAgICAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChvblN1Ym1pdCl9IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgaHRtbEZvcj1cImluc3RydWNjaW9uZXNcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LWdyYXktNzAwIHRleHQtc20gZm9udC1ib2xkIG1iLTJcIj5cbiAgICAgICAgICAgICAgSW5zdHJ1Y2Npb25lcyBwYXJhIGVsIHJlc3VtZW46XG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgIGlkPVwiaW5zdHJ1Y2Npb25lc1wiXG4gICAgICAgICAgICAgIHsuLi5yZWdpc3RlcignaW5zdHJ1Y2Npb25lcycpfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nfVxuICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJzaGFkb3cgYXBwZWFyYW5jZS1ub25lIGJvcmRlciByb3VuZGVkIHctZnVsbCBweS0yIHB4LTMgdGV4dC1ncmF5LTcwMCBsZWFkaW5nLXRpZ2h0IGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpzaGFkb3ctb3V0bGluZSByZXNpemUtdmVydGljYWxcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkRlc2NyaWJlIGPDs21vIHF1aWVyZXMgcXVlIHNlYSBlbCByZXN1bWVuLi4uXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLmluc3RydWNjaW9uZXMgJiYgKFxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC01MDAgdGV4dC1zbVwiPntlcnJvcnMuaW5zdHJ1Y2Npb25lcy5tZXNzYWdlfTwvc3Bhbj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICBFc3BlY2lmaWNhIGVsIGVuZm9xdWUsIG5pdmVsIGRlIGRldGFsbGUsIG8gYXNwZWN0b3MgcGFydGljdWxhcmVzIHF1ZSBxdWllcmVzIHF1ZSBpbmNsdXlhIGVsIHJlc3VtZW4uXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgIGRpc2FibGVkPXtpc0dlbmVyYXRpbmd9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmVlbi02MDAgaG92ZXI6YmctZ3JlZW4tNzAwIGRpc2FibGVkOmJnLWdyYXktNDAwIHRleHQtd2hpdGUgZm9udC1ib2xkIHB5LTIgcHgtNCByb3VuZGVkIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpzaGFkb3ctb3V0bGluZSBmbGV4IGl0ZW1zLWNlbnRlclwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzR2VuZXJhdGluZyA/IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiAtbWwtMSBtci0zIGgtNSB3LTUgdGV4dC13aGl0ZVwiIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIiBmaWxsPVwibm9uZVwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxjaXJjbGUgY2xhc3NOYW1lPVwib3BhY2l0eS0yNVwiIGN4PVwiMTJcIiBjeT1cIjEyXCIgcj1cIjEwXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgc3Ryb2tlV2lkdGg9XCI0XCI+PC9jaXJjbGU+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBjbGFzc05hbWU9XCJvcGFjaXR5LTc1XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIGQ9XCJNNCAxMmE4IDggMCAwMTgtOFYwQzUuMzczIDAgMCA1LjM3MyAwIDEyaDR6bTIgNS4yOTFBNy45NjIgNy45NjIgMCAwMTQgMTJIMGMwIDMuMDQyIDEuMTM1IDUuODI0IDMgNy45MzhsMy0yLjY0N3pcIj48L3BhdGg+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgR2VuZXJhbmRvIHJlc3VtZW4uLi5cbiAgICAgICAgICAgICAgICB7dGllbXBvRXN0aW1hZG8gPiAwICYmIGAgKH4ke3RpZW1wb0VzdGltYWRvfXMpYH1cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxzdmcgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMlwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIiBmaWxsPVwiY3VycmVudENvbG9yXCI+XG4gICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTQgNGEyIDIgMCAwMTItMmg4YTIgMiAwIDAxMiAydjEyYTIgMiAwIDAxLTIgMkg2YTIgMiAwIDAxLTItMlY0em0yIDB2MTJoOFY0SDZ6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGxSdWxlPVwiZXZlbm9kZFwiIGQ9XCJNOCA2YTEgMSAwIDAxMS0xaDJhMSAxIDAgMTEwIDJIOWExIDEgMCAwMS0xLTF6TTggOWExIDEgMCAwMTEtMWgyYTEgMSAwIDExMCAySDlhMSAxIDAgMDEtMS0xek04IDEyYTEgMSAwIDAxMS0xaDJhMSAxIDAgMTEwIDJIOWExIDEgMCAwMS0xLTF6XCIgY2xpcFJ1bGU9XCJldmVub2RkXCIgLz5cbiAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICBHZW5lcmFyIFJlc3VtZW5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAge2lzR2VuZXJhdGluZyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXllbGxvdy01MCBib3JkZXIgYm9yZGVyLXllbGxvdy0yMDAgcm91bmRlZC1sZyBwLTNcIj5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctODAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICA8c3Ryb25nPuKPsyBHZW5lcmFuZG8gcmVzdW1lbi4uLjwvc3Ryb25nPjxiciAvPlxuICAgICAgICAgICAgICAgIExhIElBIGVzdMOhIGFuYWxpemFuZG8gZWwgY29udGVuaWRvIHkgY3JlYW5kbyB1biByZXN1bWVuIGVzdHJ1Y3R1cmFkby4gXG4gICAgICAgICAgICAgICAgRXN0ZSBwcm9jZXNvIHB1ZWRlIHRhcmRhciB7dGllbXBvRXN0aW1hZG8gPiAwID8gYGFwcm94aW1hZGFtZW50ZSAke3RpZW1wb0VzdGltYWRvfSBzZWd1bmRvc2AgOiAndW5vcyBtb21lbnRvcyd9LlxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Zvcm0+XG4gICAgICApfVxuXG4gICAgICB7LyogVmlzdGEgcHJldmlhIGRlbCByZXN1bWVuIGdlbmVyYWRvICovfVxuICAgICAge3Jlc3VtZW5HZW5lcmFkbyAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNlwiPlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgbWItM1wiPvCfk4sgUmVzdW1lbiBHZW5lcmFkbzwvaDM+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIGJvcmRlciBib3JkZXItZ3JheS0yMDAgcm91bmRlZC1sZyBwLTQgbWF4LWgtOTYgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8ZGl2IFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lXCJcbiAgICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgXG4gICAgICAgICAgICAgICAgX19odG1sOiByZXN1bWVuR2VuZXJhZG8ucmVwbGFjZSgvXFxuL2csICc8YnIgLz4nKSBcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTJcIj5cbiAgICAgICAgICAgIOKchSBSZXN1bWVuIGd1YXJkYWRvIGV4aXRvc2FtZW50ZS4gUHVlZGVzIGFjY2VkZXIgYSDDqWwgZGVzZGUgbGEgc2VjY2nDs24gZGUgcmVzw7ptZW5lcy5cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuXG4iXSwibmFtZXMiOlsiX2RlZmluZVByb3BlcnR5IiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJfanN4RmlsZU5hbWUiLCJfcyIsIiRSZWZyZXNoU2lnJCIsIm93bktleXMiLCJlIiwiciIsInQiLCJPYmplY3QiLCJrZXlzIiwiZ2V0T3duUHJvcGVydHlTeW1ib2xzIiwibyIsImZpbHRlciIsImdldE93blByb3BlcnR5RGVzY3JpcHRvciIsImVudW1lcmFibGUiLCJwdXNoIiwiYXBwbHkiLCJfb2JqZWN0U3ByZWFkIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiZm9yRWFjaCIsImdldE93blByb3BlcnR5RGVzY3JpcHRvcnMiLCJkZWZpbmVQcm9wZXJ0aWVzIiwiZGVmaW5lUHJvcGVydHkiLCJfcmVnZW5lcmF0b3JSdW50aW1lIiwidXNlU3RhdGUiLCJ1c2VGb3JtIiwiem9kUmVzb2x2ZXIiLCJ6IiwidG9hc3QiLCJndWFyZGFyUmVzdW1lbiIsImV4aXN0ZVJlc3VtZW5QYXJhRG9jdW1lbnRvIiwidXNlQmFja2dyb3VuZEdlbmVyYXRpb24iLCJqc3hERVYiLCJfanN4REVWIiwiRnJhZ21lbnQiLCJfRnJhZ21lbnQiLCJzdW1tYXJ5U2NoZW1hIiwib2JqZWN0IiwiaW5zdHJ1Y2Npb25lcyIsInN0cmluZyIsIm1pbiIsIlN1bW1hcnlHZW5lcmF0b3IiLCJfcmVmIiwiZG9jdW1lbnRvc1NlbGVjY2lvbmFkb3MiLCJvblN1bW1hcnlHZW5lcmF0ZWQiLCJfdXNlU3RhdGUiLCJyZXN1bWVuR2VuZXJhZG8iLCJzZXRSZXN1bWVuR2VuZXJhZG8iLCJfdXNlU3RhdGUyIiwidGllbXBvRXN0aW1hZG8iLCJzZXRUaWVtcG9Fc3RpbWFkbyIsIl91c2VCYWNrZ3JvdW5kR2VuZXJhdCIsImdlbmVyYXRlUmVzdW1lbiIsImlzR2VuZXJhdGluZyIsImdldEFjdGl2ZVRhc2siLCJfdXNlRm9ybSIsInJlc29sdmVyIiwiZGVmYXVsdFZhbHVlcyIsInJlZ2lzdGVyIiwiaGFuZGxlU3VibWl0IiwiZXJyb3JzIiwiZm9ybVN0YXRlIiwicmVzZXQiLCJ2YWxpZGFyRG9jdW1lbnRvUGFyYVJlc3VtZW4iLCJkb2N1bWVudG8iLCJ2YWxpZG8iLCJlcnJvciIsInRpdHVsbyIsInRyaW0iLCJjb250ZW5pZG8iLCJlc3RpbWFyVGllbXBvR2VuZXJhY2lvbiIsInBhbGFicmFzIiwic3BsaXQiLCJNYXRoIiwibWF4IiwiY2VpbCIsInB1ZWRlR2VuZXJhciIsInZhbGlkYWNpb25Eb2N1bWVudG8iLCJvblN1Ym1pdCIsIl9yZWYyIiwibWFyayIsIl9jYWxsZWUiLCJkYXRhIiwiX2RvY3VtZW50byRjb250ZW5pZG8iLCJfcmVxdWVzdERhdGEkY29udGV4dG8iLCJfcmVzdWx0JHJlc3VsdCIsIl9yZXN1bHQkcmVzdWx0MiIsImV4aXN0ZSIsInJlcXVlc3REYXRhIiwicmVzcG9uc2UiLCJyZXNwb25zZVRleHQiLCJyZXN1bHQiLCJyZXN1bWVuQ29udGVudCIsInJlc3VtZW5JZCIsImVycm9yTWVzc2FnZSIsImVycm9yT2JqIiwid3JhcCIsIl9jYWxsZWUkIiwiX2NvbnRleHQiLCJwcmV2IiwibmV4dCIsImFicnVwdCIsImNvbnNvbGUiLCJsb2ciLCJzZXRJc0dlbmVyYXRpbmciLCJpZCIsImNhdGVnb3JpYSIsIm51bWVyb190ZW1hIiwiY29udGVuaWRvTGVuZ3RoIiwic2VudCIsImFjdGlvbiIsInBldGljaW9uIiwiY29uY2F0IiwiY29udGV4dG9zIiwiY29udGV4dG9zTGVuZ3RoIiwicHJpbWVyQ29udGV4dG9MZW5ndGgiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInN0YXR1cyIsIm9rIiwidGV4dCIsInN1YnN0cmluZyIsInBhcnNlIiwidDAiLCJFcnJvciIsImRldGFsbGVzIiwiaGFzUmVzdWx0IiwicmVzdWx0TGVuZ3RoIiwicmVzdWx0UHJldmlldyIsInN1Y2Nlc3MiLCJ0MSIsIm1lc3NhZ2UiLCJmaW5pc2giLCJzdG9wIiwiX3giLCJjbGFzc05hbWUiLCJjaGlsZHJlbiIsImZpbGVOYW1lIiwibGluZU51bWJlciIsImNvbHVtbk51bWJlciIsImh0bWxGb3IiLCJkaXNhYmxlZCIsInJvd3MiLCJwbGFjZWhvbGRlciIsInR5cGUiLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94IiwiY3giLCJjeSIsInN0cm9rZSIsInN0cm9rZVdpZHRoIiwiZCIsImZpbGxSdWxlIiwiY2xpcFJ1bGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsInJlcGxhY2UiLCJfYyIsIiRSZWZyZXNoUmVnJCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\n"));

/***/ })

});