/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?4184\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQXlIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b63ad14717ba\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImI2M2FkMTQ3MTdiYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/shared/components/ClientLayout */ \"(rsc)/./src/features/shared/components/ClientLayout.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__);\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\layout.tsx\";\n\n\n\nconst metadata = {\n    title: 'OposiAI - Asistente IA para Oposiciones',\n    description: 'Aplicación de preguntas y respuestas con IA para temarios de oposiciones'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"body\", {\n            className: \"font-sans bg-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_features_shared_components_ClientLayout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUNzQjtBQUM4QztBQUFDO0FBRTlELE1BQU1HLFFBQWtCLEdBQUc7SUFDaENDLEtBQUssRUFBRSx5Q0FBeUM7SUFDaERDLFdBQVcsRUFBRTtBQUNmLENBQUM7QUFFYyxTQUFTQyxVQUFVQSxDQUFDLEVBQ2pDQyxRQUFBQSxFQUdBLEVBQUU7SUFDRixxQkFDRUwsNkRBQUE7UUFBTU0sSUFBSSxFQUFDLElBQUk7UUFBQUQsUUFBQSxnQkFDYkwsNkRBQUE7WUFBTU8sU0FBUyxFQUFDLHVCQUF1QjtZQUFBRixRQUFBLGdCQUNyQ0wsNkRBQUEsQ0FBQ0YsZ0ZBQVk7Z0JBQUFPLFFBQUEsRUFDVkE7WUFBUTtnQkFBQUcsUUFBQSxFQUFBQyxZQUFBO2dCQUFBQyxVQUFBO2dCQUFBQyxZQUFBO1lBQUEsT0FDRztRQUFDO1lBQUFILFFBQUEsRUFBQUMsWUFBQTtZQUFBQyxVQUFBO1lBQUFDLFlBQUE7UUFBQSxPQUNYO0lBQUM7UUFBQUgsUUFBQSxFQUFBQyxZQUFBO1FBQUFDLFVBQUE7UUFBQUMsWUFBQTtJQUFBLE9BQ0gsQ0FBQztBQUVYIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0JztcbmltcG9ydCAnLi9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgQ2xpZW50TGF5b3V0IGZyb20gJ0AvZmVhdHVyZXMvc2hhcmVkL2NvbXBvbmVudHMvQ2xpZW50TGF5b3V0JztcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdPcG9zaUFJIC0gQXNpc3RlbnRlIElBIHBhcmEgT3Bvc2ljaW9uZXMnLFxuICBkZXNjcmlwdGlvbjogJ0FwbGljYWNpw7NuIGRlIHByZWd1bnRhcyB5IHJlc3B1ZXN0YXMgY29uIElBIHBhcmEgdGVtYXJpb3MgZGUgb3Bvc2ljaW9uZXMnLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZXNcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT1cImZvbnQtc2FucyBiZy1ncmF5LTEwMFwiPlxuICAgICAgICA8Q2xpZW50TGF5b3V0PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9DbGllbnRMYXlvdXQ+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkNsaWVudExheW91dCIsImpzeERFViIsIl9qc3hERVYiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJsYW5nIiwiY2xhc3NOYW1lIiwiZmlsZU5hbWUiLCJfanN4RmlsZU5hbWUiLCJsaW5lTnVtYmVyIiwiY29sdW1uTnVtYmVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\app\\login\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\augment-projects\\OposI\\v16\\src\\features\\shared\\components\\ClientLayout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/features/shared/components/ClientLayout.tsx */ \"(ssr)/./src/features/shared/components/ClientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbmFhdGElNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDT3Bvc0klNUMlNUN2MTYlNUMlNUNzcmMlNUMlNUNmZWF0dXJlcyU1QyU1Q3NoYXJlZCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNDbGllbnRMYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsOE1BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcbmFhdGFcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcT3Bvc0lcXFxcdjE2XFxcXHNyY1xcXFxmZWF0dXJlc1xcXFxzaGFyZWRcXFxcY29tcG9uZW50c1xcXFxDbGllbnRMYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Cfeatures%5C%5Cshared%5C%5Ccomponents%5C%5CClientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q25hYXRhJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q09wb3NJJTVDJTVDdjE2JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDbG9naW4lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQXlIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxuYWF0YVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxPcG9zSVxcXFx2MTZcXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cnaata%5C%5CDocuments%5C%5Caugment-projects%5C%5COposI%5C%5Cv16%5C%5Csrc%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\login\\\\page.tsx\";\n\n\n\n\nfunction LoginPage() {\n    const { 0: email, 1: setEmail } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const { 0: password, 1: setPassword } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const { 0: formError, 1: setFormError } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const { iniciarSesion, error, isLoading, user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // El middleware y AuthContext ya manejan la redirección si hay una sesión activa\n    // No necesitamos un useEffect adicional para esto\n    // Actualizar el error del formulario cuando cambia el error de autenticación\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"LoginPage.useEffect\": ()=>{\n            if (error) {\n                // Los mensajes de error ya vienen traducidos desde authService.ts\n                setFormError(error);\n                // Si es un error de sincronización de tiempo, mostrar información adicional\n                if (error.includes('sincronización de tiempo')) {\n                    console.info('Sugerencia: Verifica que la hora de tu dispositivo esté correctamente configurada y sincronizada con un servidor de tiempo.');\n                }\n            }\n        }\n    }[\"LoginPage.useEffect\"], [\n        error\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setFormError('');\n        // Validaciones básicas\n        if (!email.trim()) {\n            setFormError('Por favor, ingresa tu email');\n            return;\n        }\n        if (!password.trim()) {\n            setFormError('Por favor, ingresa tu contraseña');\n            return;\n        }\n        // Intentar iniciar sesión\n        await iniciarSesion(email, password);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                        children: \"OposiAI\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"h2\", {\n                        className: \"mt-2 text-center text-xl font-semibold text-gray-900\",\n                        children: \"Inicia sesi\\xF3n en tu cuenta\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-center text-sm text-gray-600\",\n                        children: \"Accede a tu asistente inteligente para oposiciones\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 51,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                className: \"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                    className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"form\", {\n                            className: \"space-y-6\",\n                            onSubmit: handleSubmit,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"input\", {\n                                                id: \"email\",\n                                                name: \"email\",\n                                                type: \"email\",\n                                                autoComplete: \"email\",\n                                                required: true,\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700\",\n                                            children: \"Contrase\\xF1a\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                            className: \"mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"input\", {\n                                                id: \"password\",\n                                                name: \"password\",\n                                                type: \"password\",\n                                                autoComplete: \"current-password\",\n                                                required: true,\n                                                value: password,\n                                                onChange: (e)=>setPassword(e.target.value),\n                                                className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm\",\n                                                disabled: isLoading\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                formError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                    className: \"text-red-500 text-sm bg-red-50 p-3 rounded-md border border-red-200\",\n                                    children: [\n                                        formError,\n                                        formError.includes('sincronización de tiempo') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-gray-600 text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"p\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"strong\", {\n                                                            children: \"Sugerencia:\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 109,\n                                                            columnNumber: 24\n                                                        }, this),\n                                                        \" Este error puede ocurrir cuando la hora de tu dispositivo no est\\xE1 sincronizada correctamente.\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 109,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"ol\", {\n                                                    className: \"list-decimal pl-5 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"li\", {\n                                                            children: \"Verifica que la fecha y hora de tu dispositivo est\\xE9n configuradas correctamente\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 111,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"li\", {\n                                                            children: \"Activa la sincronizaci\\xF3n autom\\xE1tica de hora en tu sistema\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 112,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"li\", {\n                                                            children: \"Reinicia el navegador e intenta nuevamente\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 113,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 110,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isLoading,\n                                        className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"svg\", {\n                                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    fill: \"none\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"circle\", {\n                                                            className: \"opacity-25\",\n                                                            cx: \"12\",\n                                                            cy: \"12\",\n                                                            r: \"10\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 129,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"path\", {\n                                                            className: \"opacity-75\",\n                                                            fill: \"currentColor\",\n                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 130,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 128,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Iniciando sesi\\xF3n...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 127,\n                                            columnNumber: 19\n                                        }, this) : 'Iniciar sesión'\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxDEV)(\"p\", {\n                                className: \"text-center text-sm text-gray-600\",\n                                children: \"\\xBFNo tienes una cuenta? Contacta con el administrador para solicitar acceso.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _lib_supabase_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/authService */ \"(ssr)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../features/auth/hooks/useInactivityTimer */ \"(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\");\n/* harmony import */ var _features_auth_components_InactivityWarning__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../features/auth/components/InactivityWarning */ \"(ssr)/./src/features/auth/components/InactivityWarning.tsx\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__);\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\AuthContext.tsx\";\n\n\n\n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nconst AuthProvider = ({ children })=>{\n    const { 0: user, 1: setUser } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { 0: session, 1: setSession } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { 0: isLoading, 1: setIsLoading } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true); // Start true: loading initial auth state\n    const { 0: error, 1: setError } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { 0: showInactivityWarning, 1: setShowInactivityWarning } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { 0: warningTimeRemaining, 1: setWarningTimeRemaining } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(60); // 60 segundos de advertencia\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    // Effect for auth state listener and initial session check\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            setIsLoading(true); // Explicitly set loading true at the start of auth setup\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": (event, currentSession)=>{\n                    setSession(currentSession);\n                    setUser(currentSession?.user ?? null);\n                    setError(null); // Clear previous errors on any auth state change\n                    // Centralize setIsLoading(false) after processing the event.\n                    if (event === 'INITIAL_SESSION' || event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED' || event === 'USER_UPDATED' || event === 'PASSWORD_RECOVERY') {\n                        setIsLoading(false);\n                    }\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Initial session fetch. onAuthStateChange with INITIAL_SESSION will also fire.\n            _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session: initialSessionCheck }, error: getSessionError })=>{\n                    if (getSessionError) {\n                        setError(getSessionError.message);\n                        setIsLoading(false); // Ensure loading is false if initial getSession fails\n                    }\n                    // Para dispositivos móviles, verificar también localStorage si no hay sesión\n                    if (!initialSessionCheck && false) {}\n                // If INITIAL_SESSION hasn't fired and set loading to false, and this fails, we ensure it's false.\n                // Note: if getSession is successful, `setIsLoading(false)` is primarily handled by INITIAL_SESSION event.\n                }\n            }[\"AuthProvider.useEffect\"]).catch({\n                \"AuthProvider.useEffect\": (error)=>{\n                    setError(error.message);\n                    setIsLoading(false); // Ensure loading is false if initial getSession throws\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>{\n                    authListener?.subscription.unsubscribe();\n                }\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []); // Runs once on mount\n    // Effect for handling redirections based on auth state and pathname\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // No realizar redirecciones mientras se está cargando\n            if (isLoading) {\n                return;\n            }\n            // No aplicar redirecciones a rutas de API o recursos estáticos\n            if (pathname.startsWith('/api') || pathname.startsWith('/_next')) {\n                return;\n            }\n            // Definir rutas públicas (mantener sincronizado con middleware)\n            // src/contexts/AuthContext.tsx (CORREGIDO)\n            const publicPaths = [\n                '/',\n                '/login',\n                '/payment',\n                '/thank-you',\n                '/auth/callback',\n                '/auth/confirmed',\n                // Ya debería estar\n                '/auth/unauthorized',\n                // Ya debería estar\n                '/auth/reset-password',\n                // Importante para el flujo de establecimiento de contraseña\n                '/auth/confirm-reset',\n                // Importante para el flujo de establecimiento de contraseña\n                '/auth/confirm-invitation' // <-- **AÑADIR ESTA LÍNEA**\n            ];\n            // Si hay sesión y estamos en /login, redirigir a la aplicación\n            // Esta es una salvaguarda del lado del cliente.\n            if (session && pathname === '/login') {\n                router.replace('/app'); // router.replace es mejor aquí para evitar entradas en el historial\n                return; // Importante retornar para no evaluar la siguiente condición\n            }\n            // Si NO hay sesión y NO estamos en una ruta pública (y no es una ruta API/interna)\n            // Esta lógica es para cuando el estado cambia en el cliente (ej. logout)\n            if (!session && !publicPaths.includes(pathname) && !pathname.startsWith('/api') && !pathname.startsWith('/_next')) {\n                router.replace('/login');\n                return;\n            }\n        }\n    }[\"AuthProvider.useEffect\"], [\n        session,\n        isLoading,\n        pathname,\n        router\n    ]);\n    const iniciarSesion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"AuthProvider.useCallback[iniciarSesion]\": async (email, password_provided)=>{\n            setIsLoading(true);\n            setError(null);\n            try {\n                const { user: loggedInUser, session: currentAuthSession, error: loginError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_3__.iniciarSesion)(email, password_provided);\n                if (loginError) {\n                    setError(loginError);\n                    setIsLoading(false); // Ensure loading is false on error\n                    return {\n                        user: null,\n                        session: null,\n                        error: loginError\n                    };\n                }\n                // Verificar que la sesión se haya establecido correctamente antes de redirigir\n                if (currentAuthSession) {\n                    // Esperar un momento adicional para asegurar que las cookies se propaguen\n                    // antes de la redirección\n                    await new Promise({\n                        \"AuthProvider.useCallback[iniciarSesion]\": (resolve)=>setTimeout(resolve, 300)\n                    }[\"AuthProvider.useCallback[iniciarSesion]\"]);\n                    // Redirigir a la aplicación usando replace para evitar entradas en el historial\n                    router.replace('/app');\n                }\n                // If successful, onAuthStateChange (SIGNED_IN) will set user, session, and isLoading to false.\n                return {\n                    user: loggedInUser,\n                    session: currentAuthSession,\n                    error: null\n                };\n            } catch (e) {\n                const errorMessage = e instanceof Error && e.message ? e.message : 'Error desconocido durante el inicio de sesión.';\n                setError(errorMessage);\n                setIsLoading(false); // Ensure loading is false on exception\n                return {\n                    user: null,\n                    session: null,\n                    error: errorMessage\n                };\n            }\n        }\n    }[\"AuthProvider.useCallback[iniciarSesion]\"], [\n        router\n    ]); // Added router dependency\n    const cerrarSesion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"AuthProvider.useCallback[cerrarSesion]\": async ()=>{\n            setIsLoading(true);\n            setError(null);\n            const { error: logoutError } = await (0,_lib_supabase_authService__WEBPACK_IMPORTED_MODULE_3__.cerrarSesion)();\n            if (logoutError) {\n                setError(logoutError);\n                setIsLoading(false); // Ensure loading is false on error\n            }\n        // If successful, onAuthStateChange (SIGNED_OUT) handles state updates and isLoading.\n        // The redirection useEffect will then handle redirecting to /login.\n        }\n    }[\"AuthProvider.useCallback[cerrarSesion]\"], []); // Assuming cerrarSesionService is a stable import\n    const estaAutenticado = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"AuthProvider.useCallback[estaAutenticado]\": ()=>!!user && !!session && !isLoading\n    }[\"AuthProvider.useCallback[estaAutenticado]\"], [\n        user,\n        session,\n        isLoading\n    ]);\n    // Función para manejar el logout por inactividad\n    const handleInactivityLogout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"AuthProvider.useCallback[handleInactivityLogout]\": async ()=>{\n            // Cerrar sesión directamente sin mostrar advertencia\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleInactivityLogout]\"], [\n        cerrarSesion\n    ]);\n    // Función para extender la sesión\n    const handleExtendSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"AuthProvider.useCallback[handleExtendSession]\": ()=>{\n            setShowInactivityWarning(false);\n        // El hook useAutoLogout se reiniciará automáticamente con la actividad\n        }\n    }[\"AuthProvider.useCallback[handleExtendSession]\"], []);\n    // Función para cerrar sesión desde la advertencia\n    const handleLogoutFromWarning = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"AuthProvider.useCallback[handleLogoutFromWarning]\": async ()=>{\n            setShowInactivityWarning(false);\n            await cerrarSesion();\n        }\n    }[\"AuthProvider.useCallback[handleLogoutFromWarning]\"], [\n        cerrarSesion\n    ]);\n    // Hook para manejar la inactividad (solo si el usuario está autenticado)\n    const { resetTimer } = (0,_features_auth_hooks_useInactivityTimer__WEBPACK_IMPORTED_MODULE_4__.useAutoLogout)(5, // 5 minutos de inactividad antes de cerrar sesión directamente\n    handleInactivityLogout, estaAutenticado() // Solo activo si está autenticado\n    );\n    const value = {\n        user,\n        session,\n        isLoading,\n        error,\n        iniciarSesion,\n        cerrarSesion,\n        estaAutenticado\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: [\n            children,\n             false && /*#__PURE__*/ 0\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 5\n    }, undefined);\n};\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth debe ser utilizado dentro de un AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/BackgroundTasksContext.tsx":
/*!*************************************************!*\
  !*** ./src/contexts/BackgroundTasksContext.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BackgroundTasksProvider: () => (/* binding */ BackgroundTasksProvider),\n/* harmony export */   useBackgroundTasks: () => (/* binding */ useBackgroundTasks)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ BackgroundTasksProvider,useBackgroundTasks auto */ var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\contexts\\\\BackgroundTasksContext.tsx\";\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            _defineProperty(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\nfunction _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n        Object.defineProperty(obj, key, {\n            value: value,\n            enumerable: true,\n            configurable: true,\n            writable: true\n        });\n    } else {\n        obj[key] = value;\n    }\n    return obj;\n}\nfunction _toPropertyKey(arg) {\n    var key = _toPrimitive(arg, \"string\");\n    return typeof key === \"symbol\" ? key : String(key);\n}\nfunction _toPrimitive(input, hint) {\n    if (typeof input !== \"object\" || input === null) return input;\n    var prim = input[Symbol.toPrimitive];\n    if (prim !== undefined) {\n        var res = prim.call(input, hint || \"default\");\n        if (typeof res !== \"object\") return res;\n        throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (hint === \"string\" ? String : Number)(input);\n}\n\n\n\nconst BackgroundTasksContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nconst BackgroundTasksProvider = ({ children })=>{\n    const { 0: tasks, 1: setTasks } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const addTask = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[addTask]\": (taskData)=>{\n            const id = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n            const newTask = _objectSpread(_objectSpread({}, taskData), {}, {\n                id,\n                status: 'pending',\n                createdAt: new Date()\n            });\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[addTask]\": (prev)=>[\n                        ...prev,\n                        newTask\n                    ]\n            }[\"BackgroundTasksProvider.useCallback[addTask]\"]);\n            // Mostrar notificación de inicio\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.loading(`Iniciando: ${newTask.title}`, {\n                id: `task_start_${id}`,\n                duration: 2000\n            });\n            return id;\n        }\n    }[\"BackgroundTasksProvider.useCallback[addTask]\"], []);\n    const updateTask = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[updateTask]\": (id, updates)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[updateTask]\": (prev)=>prev.map({\n                        \"BackgroundTasksProvider.useCallback[updateTask]\": (task)=>{\n                            if (task.id === id) {\n                                const updatedTask = _objectSpread(_objectSpread({}, task), updates);\n                                // Manejar notificaciones según el estado\n                                if (updates.status === 'processing' && task.status === 'pending') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_start_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.loading(`Procesando: ${task.title}`, {\n                                        id: `task_processing_${id}`\n                                    });\n                                } else if (updates.status === 'completed' && task.status !== 'completed') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.success(`Completado: ${task.title}`, {\n                                        id: `task_completed_${id}`,\n                                        duration: 4000\n                                    });\n                                    updatedTask.completedAt = new Date();\n                                } else if (updates.status === 'error' && task.status !== 'error') {\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_processing_${id}`);\n                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(`Error: ${task.title}`, {\n                                        id: `task_error_${id}`,\n                                        duration: 5000\n                                    });\n                                }\n                                return updatedTask;\n                            }\n                            return task;\n                        }\n                    }[\"BackgroundTasksProvider.useCallback[updateTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[updateTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[updateTask]\"], []);\n    const removeTask = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[removeTask]\": (id)=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[removeTask]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[removeTask]\": (task)=>task.id !== id\n                    }[\"BackgroundTasksProvider.useCallback[removeTask]\"])\n            }[\"BackgroundTasksProvider.useCallback[removeTask]\"]);\n            // Limpiar notificaciones relacionadas\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_start_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_processing_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_completed_${id}`);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.dismiss(`task_error_${id}`);\n        }\n    }[\"BackgroundTasksProvider.useCallback[removeTask]\"], []);\n    const getTask = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTask]\": (id)=>{\n            return tasks.find({\n                \"BackgroundTasksProvider.useCallback[getTask]\": (task)=>task.id === id\n            }[\"BackgroundTasksProvider.useCallback[getTask]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTask]\"], [\n        tasks\n    ]);\n    const getTasksByType = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[getTasksByType]\": (type)=>{\n            return tasks.filter({\n                \"BackgroundTasksProvider.useCallback[getTasksByType]\": (task)=>task.type === type\n            }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[getTasksByType]\"], [\n        tasks\n    ]);\n    const clearCompletedTasks = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": ()=>{\n            setTasks({\n                \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (prev)=>prev.filter({\n                        \"BackgroundTasksProvider.useCallback[clearCompletedTasks]\": (task)=>task.status !== 'completed' && task.status !== 'error'\n                    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"])\n            }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"]);\n        }\n    }[\"BackgroundTasksProvider.useCallback[clearCompletedTasks]\"], []);\n    const activeTasks = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[activeTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[activeTasks]\": (task)=>task.status === 'pending' || task.status === 'processing'\n            }[\"BackgroundTasksProvider.useMemo[activeTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[activeTasks]\"], [\n        tasks\n    ]);\n    const completedTasks = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[completedTasks]\": ()=>tasks.filter({\n                \"BackgroundTasksProvider.useMemo[completedTasks]\": (task)=>task.status === 'completed' || task.status === 'error'\n            }[\"BackgroundTasksProvider.useMemo[completedTasks]\"])\n    }[\"BackgroundTasksProvider.useMemo[completedTasks]\"], [\n        tasks\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"BackgroundTasksProvider.useMemo[value]\": ()=>({\n                tasks,\n                addTask,\n                updateTask,\n                removeTask,\n                getTask,\n                getTasksByType,\n                clearCompletedTasks,\n                activeTasks,\n                completedTasks\n            })\n    }[\"BackgroundTasksProvider.useMemo[value]\"], [\n        tasks,\n        addTask,\n        updateTask,\n        removeTask,\n        getTask,\n        getTasksByType,\n        clearCompletedTasks,\n        activeTasks,\n        completedTasks\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(BackgroundTasksContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 5\n    }, undefined);\n};\nconst useBackgroundTasks = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BackgroundTasksContext);\n    if (context === undefined) {\n        throw new Error('useBackgroundTasks must be used within a BackgroundTasksProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/BackgroundTasksContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/AuthManager.tsx":
/*!******************************************************!*\
  !*** ./src/features/auth/components/AuthManager.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthManager)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\n * Componente para manejar errores comunes de autenticación\n * y sincronización de tiempo en Supabase\n */ function AuthManager() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"AuthManager.useEffect\": ()=>{\n            // Verificar si hay problemas de sincronización de tiempo\n            const checkTimeSync = {\n                \"AuthManager.useEffect.checkTimeSync\": async ()=>{\n                    try {\n                        // Usar variables de entorno para la configuración de Supabase\n                        const supabaseUrl = \"https://fxnhpxjijinfuxxxplzj.supabase.co\";\n                        const supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\";\n                        if (!supabaseUrl || !supabaseKey) {\n                            console.warn('Variables de entorno de Supabase no configuradas');\n                            return;\n                        }\n                        // Obtener la hora del servidor de Supabase\n                        const response = await fetch(`${supabaseUrl}/rest/v1/`, {\n                            method: 'GET',\n                            headers: {\n                                'Content-Type': 'application/json',\n                                'apikey': supabaseKey\n                            }\n                        });\n                        // Obtener la fecha del servidor desde las cabeceras\n                        const serverDate = new Date(response.headers.get('date') || '');\n                        const clientDate = new Date();\n                        // Calcular la diferencia en segundos\n                        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);\n                        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia\n                        if (timeDiff > 60) {\n                            console.warn(`Posible problema de sincronización de tiempo detectado. ` + `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` + `Esto puede causar problemas de autenticación.`);\n                        }\n                    } catch (error) {\n                        console.error('Error al verificar sincronización de tiempo:', error);\n                    }\n                }\n            }[\"AuthManager.useEffect.checkTimeSync\"];\n            // Ejecutar la verificación\n            checkTimeSync();\n            // Configurar un listener para eventos de autenticación\n            const { data: authListener } = _lib_supabase_supabaseClient__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.onAuthStateChange({\n                \"AuthManager.useEffect\": (event, session)=>{\n                    if (event === 'SIGNED_OUT') {\n                    // Supabase ya maneja la limpieza de tokens internamente\n                    } else if (event === 'SIGNED_IN') {}\n                }\n            }[\"AuthManager.useEffect\"]);\n            return ({\n                \"AuthManager.useEffect\": ()=>{\n                    authListener.subscription.unsubscribe();\n                }\n            })[\"AuthManager.useEffect\"];\n        }\n    }[\"AuthManager.useEffect\"], []);\n    // Este componente no renderiza nada visible\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/AuthManager.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/components/InactivityWarning.tsx":
/*!************************************************************!*\
  !*** ./src/features/auth/components/InactivityWarning.tsx ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiClock,FiRefreshCw!=!react-icons/fi */ \"(ssr)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\auth\\\\components\\\\InactivityWarning.tsx\";\n\n\n\nconst InactivityWarning = ({ isVisible, timeRemaining, onExtendSession, onLogout })=>{\n    const { 0: countdown, 1: setCountdown } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(timeRemaining);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            setCountdown(timeRemaining);\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        timeRemaining\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"InactivityWarning.useEffect\": ()=>{\n            if (!isVisible || countdown <= 0) return;\n            const interval = setInterval({\n                \"InactivityWarning.useEffect.interval\": ()=>{\n                    setCountdown({\n                        \"InactivityWarning.useEffect.interval\": (prev)=>{\n                            if (prev <= 1) {\n                                onLogout();\n                                return 0;\n                            }\n                            return prev - 1;\n                        }\n                    }[\"InactivityWarning.useEffect.interval\"]);\n                }\n            }[\"InactivityWarning.useEffect.interval\"], 1000);\n            return ({\n                \"InactivityWarning.useEffect\": ()=>clearInterval(interval)\n            })[\"InactivityWarning.useEffect\"];\n        }\n    }[\"InactivityWarning.useEffect\"], [\n        isVisible,\n        countdown,\n        onLogout\n    ]);\n    if (!isVisible) return null;\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, '0')}`;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 max-w-md w-full mx-4 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"bg-yellow-100 rounded-full p-3 mr-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiAlertTriangle, {\n                                className: \"text-yellow-600 text-xl\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Sesi\\xF3n por expirar\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Tu sesi\\xF3n expirar\\xE1 por inactividad\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 rounded-lg p-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiClock, {\n                                    className: \"text-gray-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-mono font-bold text-gray-900\",\n                                    children: formatTime(countdown)\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                            className: \"text-center text-sm text-gray-600 mt-2\",\n                            children: \"Tiempo restante antes del cierre autom\\xE1tico\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                            onClick: onExtendSession,\n                            className: \"flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg flex items-center justify-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiClock_FiRefreshCw_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiRefreshCw, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Continuar sesi\\xF3n\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"button\", {\n                            onClick: onLogout,\n                            className: \"flex-1 bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors\",\n                            children: \"Cerrar sesi\\xF3n\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500\",\n                        children: \"Por seguridad, tu sesi\\xF3n se cerrar\\xE1 autom\\xE1ticamente tras 10 minutos de inactividad\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InactivityWarning);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZmVhdHVyZXMvYXV0aC9jb21wb25lbnRzL0luYWN0aXZpdHlXYXJuaW5nLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQWtEO0FBQ29CO0FBQUM7QUFTdkUsTUFBTVEsaUJBQW1ELEdBQUdBLENBQUMsRUFDM0RDLFNBQVMsRUFDVEMsYUFBYSxFQUNiQyxlQUFlLEVBQ2ZDLFFBQUFBLEVBQ0Q7SUFDQyxNQUFNLEtBQUNDLFNBQVMsS0FBRUMsWUFBQUEsRUFBWSxHQUFJYiwrQ0FBUSxDQUFDUyxhQUFhLENBQUM7SUFFekRSLGdEQUFTO3VDQUFDO1lBQ1JZLFlBQVksQ0FBQ0osYUFBYSxDQUFDO1FBQzdCLENBQUM7c0NBQUU7UUFBQ0EsYUFBYTtLQUFDLENBQUM7SUFFbkJSLGdEQUFTO3VDQUFDO1lBQ1IsSUFBSSxDQUFDTyxTQUFTLElBQUlJLFNBQVMsSUFBSSxDQUFDLEVBQUU7WUFFbEMsTUFBTUUsUUFBUSxHQUFHQyxXQUFXO3dEQUFDO29CQUMzQkYsWUFBWTtpRUFBQ0csSUFBSSxJQUFJOzRCQUNuQixJQUFJQSxJQUFJLElBQUksQ0FBQyxFQUFFO2dDQUNiTCxRQUFRLENBQUMsQ0FBQztnQ0FDVixPQUFPLENBQUM7NEJBQ1Y7NEJBQ0EsT0FBT0ssSUFBSSxHQUFHLENBQUM7d0JBQ2pCLENBQUM7O2dCQUNILENBQUM7dURBQUUsSUFBSSxDQUFDO1lBRVI7K0NBQU8sSUFBTUMsYUFBYSxDQUFDSCxRQUFRLENBQUM7O1FBQ3RDLENBQUM7c0NBQUU7UUFBQ04sU0FBUztRQUFFSSxTQUFTO1FBQUVELFFBQVE7S0FBQyxDQUFDO0lBRXBDLElBQUksQ0FBQ0gsU0FBUyxFQUFFLE9BQU8sSUFBSTtJQUUzQixNQUFNVSxVQUFVLElBQUlDLE9BQWUsSUFBSztRQUN0QyxNQUFNQyxJQUFJLEdBQUdDLElBQUksQ0FBQ0MsS0FBSyxDQUFDSCxPQUFPLEdBQUcsRUFBRSxDQUFDO1FBQ3JDLE1BQU1JLElBQUksR0FBR0osT0FBTyxHQUFHLEVBQUU7UUFDekIsT0FBUSxHQUFFQyxJQUFLLElBQUdHLElBQUksQ0FBQ0MsUUFBUSxDQUFDLENBQUMsQ0FBQ0MsUUFBUSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUUsRUFBQztJQUN0RCxDQUFDO0lBRUQscUJBQ0VuQiw2REFBQTtRQUFLb0IsU0FBUyxFQUFDLDRFQUE0RTtRQUFBQyxRQUFBLGdCQUN6RnJCLDZEQUFBO1lBQUtvQixTQUFTLEVBQUMsc0VBQXNFO1lBQUFDLFFBQUE7Z0JBQUEsY0FDbkZyQiw2REFBQTtvQkFBS29CLFNBQVMsRUFBQyx3QkFBd0I7b0JBQUFDLFFBQUE7d0JBQUEsY0FDckNyQiw2REFBQTs0QkFBS29CLFNBQVMsRUFBQyxxQ0FBcUM7NEJBQUFDLFFBQUEsZ0JBQ2xEckIsNkRBQUEsQ0FBQ0gsc0hBQWU7Z0NBQUN1QixTQUFTLEVBQUM7NEJBQXlCO2dDQUFBRSxRQUFBLEVBQUFDLFlBQUE7Z0NBQUFDLFVBQUE7Z0NBQUFDLFlBQUE7NEJBQUEsWUFBRTt3QkFBQzs0QkFBQUgsUUFBQSxFQUFBQyxZQUFBOzRCQUFBQyxVQUFBOzRCQUFBQyxZQUFBO3dCQUFBLFlBQ3BELENBQUM7d0JBQUEsY0FDTnpCLDZEQUFBOzRCQUFBcUIsUUFBQTtnQ0FBQSxjQUNFckIsNkRBQUE7b0NBQUlvQixTQUFTLEVBQUMscUNBQXFDO29DQUFBQyxRQUFBLEVBQUM7Z0NBRXBEO29DQUFBQyxRQUFBLEVBQUFDLFlBQUE7b0NBQUFDLFVBQUE7b0NBQUFDLFlBQUE7Z0NBQUEsWUFBSSxDQUFDO2dDQUFBLGNBQ0x6Qiw2REFBQTtvQ0FBR29CLFNBQVMsRUFBQyx1QkFBdUI7b0NBQUFDLFFBQUEsRUFBQztnQ0FFckM7b0NBQUFDLFFBQUEsRUFBQUMsWUFBQTtvQ0FBQUMsVUFBQTtvQ0FBQUMsWUFBQTtnQ0FBQSxZQUFHLENBQUM7NkJBQUE7d0JBQUE7NEJBQUFILFFBQUEsRUFBQUMsWUFBQTs0QkFBQUMsVUFBQTs0QkFBQUMsWUFBQTt3QkFBQSxZQUNELENBQUM7cUJBQUE7Z0JBQUE7b0JBQUFILFFBQUEsRUFBQUMsWUFBQTtvQkFBQUMsVUFBQTtvQkFBQUMsWUFBQTtnQkFBQSxZQUNILENBQUM7Z0JBQUEsY0FFTnpCLDZEQUFBO29CQUFLb0IsU0FBUyxFQUFDLGdDQUFnQztvQkFBQUMsUUFBQTt3QkFBQSxjQUM3Q3JCLDZEQUFBOzRCQUFLb0IsU0FBUyxFQUFDLGtDQUFrQzs0QkFBQUMsUUFBQTtnQ0FBQSxjQUMvQ3JCLDZEQUFBLENBQUNKLDhHQUFPO29DQUFDd0IsU0FBUyxFQUFDO2dDQUFvQjtvQ0FBQUUsUUFBQSxFQUFBQyxZQUFBO29DQUFBQyxVQUFBO29DQUFBQyxZQUFBO2dDQUFBLFlBQUUsQ0FBQztnQ0FBQSxjQUMxQ3pCLDZEQUFBO29DQUFNb0IsU0FBUyxFQUFDLDRDQUE0QztvQ0FBQUMsUUFBQSxFQUN6RFQsVUFBVSxDQUFDTixTQUFTO2dDQUFDO29DQUFBZ0IsUUFBQSxFQUFBQyxZQUFBO29DQUFBQyxVQUFBO29DQUFBQyxZQUFBO2dDQUFBLFlBQ2xCLENBQUM7NkJBQUE7d0JBQUE7NEJBQUFILFFBQUEsRUFBQUMsWUFBQTs0QkFBQUMsVUFBQTs0QkFBQUMsWUFBQTt3QkFBQSxZQUNKLENBQUM7d0JBQUEsY0FDTnpCLDZEQUFBOzRCQUFHb0IsU0FBUyxFQUFDLHdDQUF3Qzs0QkFBQUMsUUFBQSxFQUFDO3dCQUV0RDs0QkFBQUMsUUFBQSxFQUFBQyxZQUFBOzRCQUFBQyxVQUFBOzRCQUFBQyxZQUFBO3dCQUFBLFlBQUcsQ0FBQztxQkFBQTtnQkFBQTtvQkFBQUgsUUFBQSxFQUFBQyxZQUFBO29CQUFBQyxVQUFBO29CQUFBQyxZQUFBO2dCQUFBLFlBQ0QsQ0FBQztnQkFBQSxjQUVOekIsNkRBQUE7b0JBQUtvQixTQUFTLEVBQUMsZ0JBQWdCO29CQUFBQyxRQUFBO3dCQUFBLGNBQzdCckIsNkRBQUE7NEJBQ0UwQixPQUFPLEVBQUV0QixlQUFnQjs0QkFDekJnQixTQUFTLEVBQUMscUlBQXFJOzRCQUFBQyxRQUFBO2dDQUFBLGNBRS9JckIsNkRBQUEsQ0FBQ0Ysa0hBQVc7b0NBQUNzQixTQUFTLEVBQUM7Z0NBQU07b0NBQUFFLFFBQUEsRUFBQUMsWUFBQTtvQ0FBQUMsVUFBQTtvQ0FBQUMsWUFBQTtnQ0FBQSxZQUFFLENBQUM7Z0NBQUEscUJBRWxDOzZCQUFBO3dCQUFBOzRCQUFBSCxRQUFBLEVBQUFDLFlBQUE7NEJBQUFDLFVBQUE7NEJBQUFDLFlBQUE7d0JBQUEsWUFBUSxDQUFDO3dCQUFBLGNBQ1R6Qiw2REFBQTs0QkFDRTBCLE9BQU8sRUFBRXJCLFFBQVM7NEJBQ2xCZSxTQUFTLEVBQUMsb0dBQW9HOzRCQUFBQyxRQUFBLEVBQy9HO3dCQUVEOzRCQUFBQyxRQUFBLEVBQUFDLFlBQUE7NEJBQUFDLFVBQUE7NEJBQUFDLFlBQUE7d0JBQUEsWUFBUSxDQUFDO3FCQUFBO2dCQUFBO29CQUFBSCxRQUFBLEVBQUFDLFlBQUE7b0JBQUFDLFVBQUE7b0JBQUFDLFlBQUE7Z0JBQUEsWUFDTixDQUFDO2dCQUFBLGNBRU56Qiw2REFBQTtvQkFBS29CLFNBQVMsRUFBQyxrQkFBa0I7b0JBQUFDLFFBQUEsZ0JBQy9CckIsNkRBQUE7d0JBQUdvQixTQUFTLEVBQUMsdUJBQXVCO3dCQUFBQyxRQUFBLEVBQUM7b0JBRXJDO3dCQUFBQyxRQUFBLEVBQUFDLFlBQUE7d0JBQUFDLFVBQUE7d0JBQUFDLFlBQUE7b0JBQUEsWUFBRztnQkFBQztvQkFBQUgsUUFBQSxFQUFBQyxZQUFBO29CQUFBQyxVQUFBO29CQUFBQyxZQUFBO2dCQUFBLFlBQ0QsQ0FBQzthQUFBO1FBQUE7WUFBQUgsUUFBQSxFQUFBQyxZQUFBO1lBQUFDLFVBQUE7WUFBQUMsWUFBQTtRQUFBLFlBQ0g7SUFBQztRQUFBSCxRQUFBLEVBQUFDLFlBQUE7UUFBQUMsVUFBQTtRQUFBQyxZQUFBO0lBQUEsWUFDSCxDQUFDO0FBRVYsQ0FBQztBQUVELGlFQUFleEIsaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcZmVhdHVyZXNcXGF1dGhcXGNvbXBvbmVudHNcXEluYWN0aXZpdHlXYXJuaW5nLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEZpQ2xvY2ssIEZpQWxlcnRUcmlhbmdsZSwgRmlSZWZyZXNoQ3cgfSBmcm9tICdyZWFjdC1pY29ucy9maSc7XG5cbmludGVyZmFjZSBJbmFjdGl2aXR5V2FybmluZ1Byb3BzIHtcbiAgaXNWaXNpYmxlOiBib29sZWFuO1xuICB0aW1lUmVtYWluaW5nOiBudW1iZXI7IC8vIGVuIHNlZ3VuZG9zXG4gIG9uRXh0ZW5kU2Vzc2lvbjogKCkgPT4gdm9pZDtcbiAgb25Mb2dvdXQ6ICgpID0+IHZvaWQ7XG59XG5cbmNvbnN0IEluYWN0aXZpdHlXYXJuaW5nOiBSZWFjdC5GQzxJbmFjdGl2aXR5V2FybmluZ1Byb3BzPiA9ICh7XG4gIGlzVmlzaWJsZSxcbiAgdGltZVJlbWFpbmluZyxcbiAgb25FeHRlbmRTZXNzaW9uLFxuICBvbkxvZ291dFxufSkgPT4ge1xuICBjb25zdCBbY291bnRkb3duLCBzZXRDb3VudGRvd25dID0gdXNlU3RhdGUodGltZVJlbWFpbmluZyk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRDb3VudGRvd24odGltZVJlbWFpbmluZyk7XG4gIH0sIFt0aW1lUmVtYWluaW5nXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzVmlzaWJsZSB8fCBjb3VudGRvd24gPD0gMCkgcmV0dXJuO1xuXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXRDb3VudGRvd24ocHJldiA9PiB7XG4gICAgICAgIGlmIChwcmV2IDw9IDEpIHtcbiAgICAgICAgICBvbkxvZ291dCgpO1xuICAgICAgICAgIHJldHVybiAwO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBwcmV2IC0gMTtcbiAgICAgIH0pO1xuICAgIH0sIDEwMDApO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbaXNWaXNpYmxlLCBjb3VudGRvd24sIG9uTG9nb3V0XSk7XG5cbiAgaWYgKCFpc1Zpc2libGUpIHJldHVybiBudWxsO1xuXG4gIGNvbnN0IGZvcm1hdFRpbWUgPSAoc2Vjb25kczogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgbWlucyA9IE1hdGguZmxvb3Ioc2Vjb25kcyAvIDYwKTtcbiAgICBjb25zdCBzZWNzID0gc2Vjb25kcyAlIDYwO1xuICAgIHJldHVybiBgJHttaW5zfToke3NlY3MudG9TdHJpbmcoKS5wYWRTdGFydCgyLCAnMCcpfWA7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS01MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB6LTUwXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LXhsIHAtNiBtYXgtdy1tZCB3LWZ1bGwgbXgtNCBhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmcteWVsbG93LTEwMCByb3VuZGVkLWZ1bGwgcC0zIG1yLTRcIj5cbiAgICAgICAgICAgIDxGaUFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwidGV4dC15ZWxsb3ctNjAwIHRleHQteGxcIiAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgU2VzacOzbiBwb3IgZXhwaXJhclxuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICBUdSBzZXNpw7NuIGV4cGlyYXLDoSBwb3IgaW5hY3RpdmlkYWRcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgcC00IG1iLTZcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8RmlDbG9jayBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIG1yLTJcIiAvPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1tb25vIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICAgIHtmb3JtYXRUaW1lKGNvdW50ZG93bil9XG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwIG10LTJcIj5cbiAgICAgICAgICAgIFRpZW1wbyByZXN0YW50ZSBhbnRlcyBkZWwgY2llcnJlIGF1dG9tw6F0aWNvXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zXCI+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25FeHRlbmRTZXNzaW9ufVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWJsdWUtNTAwIGhvdmVyOmJnLWJsdWUtNjAwIHRleHQtd2hpdGUgZm9udC1tZWRpdW0gcHktMiBweC00IHJvdW5kZWQtbGcgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxGaVJlZnJlc2hDdyBjbGFzc05hbWU9XCJtci0yXCIgLz5cbiAgICAgICAgICAgIENvbnRpbnVhciBzZXNpw7NuXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17b25Mb2dvdXR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4LTEgYmctZ3JheS01MDAgaG92ZXI6YmctZ3JheS02MDAgdGV4dC13aGl0ZSBmb250LW1lZGl1bSBweS0yIHB4LTQgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ2VycmFyIHNlc2nDs25cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICBQb3Igc2VndXJpZGFkLCB0dSBzZXNpw7NuIHNlIGNlcnJhcsOhIGF1dG9tw6F0aWNhbWVudGUgdHJhcyAxMCBtaW51dG9zIGRlIGluYWN0aXZpZGFkXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgSW5hY3Rpdml0eVdhcm5pbmc7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkZpQ2xvY2siLCJGaUFsZXJ0VHJpYW5nbGUiLCJGaVJlZnJlc2hDdyIsImpzeERFViIsIl9qc3hERVYiLCJJbmFjdGl2aXR5V2FybmluZyIsImlzVmlzaWJsZSIsInRpbWVSZW1haW5pbmciLCJvbkV4dGVuZFNlc3Npb24iLCJvbkxvZ291dCIsImNvdW50ZG93biIsInNldENvdW50ZG93biIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJwcmV2IiwiY2xlYXJJbnRlcnZhbCIsImZvcm1hdFRpbWUiLCJzZWNvbmRzIiwibWlucyIsIk1hdGgiLCJmbG9vciIsInNlY3MiLCJ0b1N0cmluZyIsInBhZFN0YXJ0IiwiY2xhc3NOYW1lIiwiY2hpbGRyZW4iLCJmaWxlTmFtZSIsIl9qc3hGaWxlTmFtZSIsImxpbmVOdW1iZXIiLCJjb2x1bW5OdW1iZXIiLCJvbkNsaWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/components/InactivityWarning.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/auth/hooks/useInactivityTimer.ts":
/*!*******************************************************!*\
  !*** ./src/features/auth/hooks/useInactivityTimer.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAutoLogout: () => (/* binding */ useAutoLogout),\n/* harmony export */   useInactivityTimer: () => (/* binding */ useInactivityTimer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n\n\n/**\n * Hook para manejar la desconexión automática por inactividad\n */ const useInactivityTimer = ({ timeout, onTimeout, enabled = true })=>{\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const lastActivityRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Date.now());\n    const { tasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    // Verificar si hay tareas de IA activas que deberían pausar el timer\n    const hasActiveAITasks = tasks.some((task)=>task.status === 'pending' || task.status === 'processing');\n    // Función para resetear el timer\n    const resetTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n            if (!enabled) return;\n            // Limpiar el timer anterior si existe\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n            }\n            // Actualizar la última actividad\n            lastActivityRef.current = Date.now();\n            // Siempre crear un nuevo timer, independientemente de las tareas de IA\n            // La actividad del usuario siempre reinicia el timer\n            timeoutRef.current = setTimeout({\n                \"useInactivityTimer.useCallback[resetTimer]\": ()=>{\n                    // Solo ejecutar logout si no hay tareas de IA activas en el momento del timeout\n                    const currentTasks = tasks.filter({\n                        \"useInactivityTimer.useCallback[resetTimer].currentTasks\": (task)=>task.status === 'pending' || task.status === 'processing'\n                    }[\"useInactivityTimer.useCallback[resetTimer].currentTasks\"]);\n                    if (currentTasks.length === 0) {\n                        onTimeout();\n                    } else {\n                        console.log('🔄 Logout diferido: hay tareas de IA en progreso, reintentando en 1 minuto');\n                        // Reintentar en 1 minuto si hay tareas activas\n                        setTimeout({\n                            \"useInactivityTimer.useCallback[resetTimer]\": ()=>resetTimer()\n                        }[\"useInactivityTimer.useCallback[resetTimer]\"], 60000);\n                    }\n                }\n            }[\"useInactivityTimer.useCallback[resetTimer]\"], timeout);\n        }\n    }[\"useInactivityTimer.useCallback[resetTimer]\"], [\n        timeout,\n        onTimeout,\n        enabled,\n        tasks\n    ]);\n    // Función para limpiar el timer\n    const clearTimer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[clearTimer]\": ()=>{\n            if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n                timeoutRef.current = null;\n            }\n        }\n    }[\"useInactivityTimer.useCallback[clearTimer]\"], []);\n    // Función para obtener el tiempo restante\n    const getTimeRemaining = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useInactivityTimer.useCallback[getTimeRemaining]\": ()=>{\n            if (!enabled || !timeoutRef.current) return 0;\n            const elapsed = Date.now() - lastActivityRef.current;\n            const remaining = Math.max(0, timeout - elapsed);\n            return remaining;\n        }\n    }[\"useInactivityTimer.useCallback[getTimeRemaining]\"], [\n        timeout,\n        enabled\n    ]);\n    // Eventos que consideramos como actividad del usuario\n    const events = [\n        'mousedown',\n        'mousemove',\n        'keypress',\n        'scroll',\n        'touchstart',\n        'click',\n        'keydown'\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useInactivityTimer.useEffect\": ()=>{\n            if (!enabled) {\n                clearTimer();\n                return;\n            }\n            // Función que maneja los eventos de actividad\n            const handleActivity = {\n                \"useInactivityTimer.useEffect.handleActivity\": ()=>{\n                    resetTimer();\n                }\n            }[\"useInactivityTimer.useEffect.handleActivity\"];\n            // Agregar event listeners\n            events.forEach({\n                \"useInactivityTimer.useEffect\": (event)=>{\n                    document.addEventListener(event, handleActivity, true);\n                }\n            }[\"useInactivityTimer.useEffect\"]);\n            // Iniciar el timer\n            resetTimer();\n            // Cleanup\n            return ({\n                \"useInactivityTimer.useEffect\": ()=>{\n                    events.forEach({\n                        \"useInactivityTimer.useEffect\": (event)=>{\n                            document.removeEventListener(event, handleActivity, true);\n                        }\n                    }[\"useInactivityTimer.useEffect\"]);\n                    clearTimer();\n                }\n            })[\"useInactivityTimer.useEffect\"];\n        }\n    }[\"useInactivityTimer.useEffect\"], [\n        enabled,\n        resetTimer,\n        clearTimer\n    ]);\n    return {\n        resetTimer,\n        clearTimer,\n        getTimeRemaining\n    };\n};\n/**\n * Hook simplificado para desconexión automática\n */ const useAutoLogout = (timeoutMinutes = 5, onLogout, enabled = true)=>{\n    const timeoutMs = timeoutMinutes * 60 * 1000; // Convertir minutos a milisegundos\n    return useInactivityTimer({\n        timeout: timeoutMs,\n        onTimeout: onLogout,\n        enabled\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZmVhdHVyZXMvYXV0aC9ob29rcy91c2VJbmFjdGl2aXR5VGltZXIudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0Q7QUFDZ0I7QUFRdEU7O0NBRUEsR0FDTyxNQUFNSSxrQkFBa0IsR0FBR0EsQ0FBQyxFQUNqQ0MsT0FBTyxFQUNQQyxTQUFTLEVBQ1RDLE9BQU8sR0FBRyxNQUNjO0lBQ3hCLE1BQU1DLFVBQVUsR0FBR1AsNkNBQU0sQ0FBd0IsSUFBSSxDQUFDO0lBQ3RELE1BQU1RLGVBQWUsR0FBR1IsNkNBQU0sQ0FBU1MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQ2xELE1BQU0sRUFBRUMsS0FBQUEsRUFBTyxHQUFHVCxvRkFBa0IsQ0FBQyxDQUFDO0lBRXRDO0lBQ0EsTUFBTVUsZ0JBQWdCLEdBQUdELEtBQUssQ0FBQ0UsSUFBSSxFQUFDQyxJQUFJLEdBQ3RDQSxJQUFJLENBQUNDLE1BQU0sS0FBSyxTQUFTLElBQUlELElBQUksQ0FBQ0MsTUFBTSxLQUFLLFlBQy9DLENBQUM7SUFFRDtJQUNBLE1BQU1DLFVBQVUsR0FBR2Ysa0RBQVc7c0RBQUM7WUFDN0IsSUFBSSxDQUFDSyxPQUFPLEVBQUU7WUFFZDtZQUNBLElBQUlDLFVBQVUsQ0FBQ1UsT0FBTyxFQUFFO2dCQUN0QkMsWUFBWSxDQUFDWCxVQUFVLENBQUNVLE9BQU8sQ0FBQztZQUNsQztZQUVBO1lBQ0FULGVBQWUsQ0FBQ1MsT0FBTyxHQUFHUixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO1lBRXBDO1lBQ0E7WUFDQUgsVUFBVSxDQUFDVSxPQUFPLEdBQUdFLFVBQVU7OERBQUM7b0JBQzlCO29CQUNBLE1BQU1DLFlBQVksR0FBR1QsS0FBSyxDQUFDVSxNQUFNO29GQUFDUCxJQUFJLEdBQ3BDQSxJQUFJLENBQUNDLE1BQU0sS0FBSyxTQUFTLElBQUlELElBQUksQ0FBQ0MsTUFBTSxLQUFLLFlBQy9DOztvQkFFQSxJQUFJSyxZQUFZLENBQUNFLE1BQU0sS0FBSyxDQUFDLEVBQUU7d0JBQzdCakIsU0FBUyxDQUFDLENBQUM7b0JBQ2IsQ0FBQyxNQUFNO3dCQUNMa0IsT0FBTyxDQUFDQyxHQUFHLENBQUMsNEVBQTRFLENBQUM7d0JBQ3pGO3dCQUNBTCxVQUFVOzBFQUFDLElBQU1ILFVBQVUsQ0FBQyxDQUFDO3lFQUFFLEtBQUssQ0FBQztvQkFDdkM7Z0JBQ0YsQ0FBQzs2REFBRVosT0FBTyxDQUFDO1FBQ2IsQ0FBQztxREFBRTtRQUFDQSxPQUFPO1FBQUVDLFNBQVM7UUFBRUMsT0FBTztRQUFFSyxLQUFLO0tBQUMsQ0FBQztJQUV4QztJQUNBLE1BQU1jLFVBQVUsR0FBR3hCLGtEQUFXO3NEQUFDO1lBQzdCLElBQUlNLFVBQVUsQ0FBQ1UsT0FBTyxFQUFFO2dCQUN0QkMsWUFBWSxDQUFDWCxVQUFVLENBQUNVLE9BQU8sQ0FBQztnQkFDaENWLFVBQVUsQ0FBQ1UsT0FBTyxHQUFHLElBQUk7WUFDM0I7UUFDRixDQUFDO3FEQUFFLEVBQUUsQ0FBQztJQUVOO0lBQ0EsTUFBTVMsZ0JBQWdCLEdBQUd6QixrREFBVzs0REFBQztZQUNuQyxJQUFJLENBQUNLLE9BQU8sSUFBSSxDQUFDQyxVQUFVLENBQUNVLE9BQU8sRUFBRSxPQUFPLENBQUM7WUFFN0MsTUFBTVUsT0FBTyxHQUFHbEIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHRixlQUFlLENBQUNTLE9BQU87WUFDcEQsTUFBTVcsU0FBUyxHQUFHQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEVBQUUxQixPQUFPLEdBQUd1QixPQUFPLENBQUM7WUFDaEQsT0FBT0MsU0FBUztRQUNsQixDQUFDOzJEQUFFO1FBQUN4QixPQUFPO1FBQUVFLE9BQU87S0FBQyxDQUFDO0lBRXRCO0lBQ0EsTUFBTXlCLE1BQU0sR0FBRztRQUNiLFdBQVc7UUFDWCxXQUFXO1FBQ1gsVUFBVTtRQUNWLFFBQVE7UUFDUixZQUFZO1FBQ1osT0FBTztRQUNQLFNBQVM7S0FDVjtJQUlEaEMsZ0RBQVM7d0NBQUM7WUFDUixJQUFJLENBQUNPLE9BQU8sRUFBRTtnQkFDWm1CLFVBQVUsQ0FBQyxDQUFDO2dCQUNaO1lBQ0Y7WUFFQTtZQUNBLE1BQU1PLGNBQWM7K0RBQUdBLENBQUE7b0JBQ3JCaEIsVUFBVSxDQUFDLENBQUM7Z0JBQ2QsQ0FBQzs7WUFFRDtZQUNBZSxNQUFNLENBQUNFLE9BQU87aURBQUNDLEtBQUssSUFBSTtvQkFDdEJDLFFBQVEsQ0FBQ0MsZ0JBQWdCLENBQUNGLEtBQUssRUFBRUYsY0FBYyxFQUFFLElBQUksQ0FBQztnQkFDeEQsQ0FBQzs7WUFFRDtZQUNBaEIsVUFBVSxDQUFDLENBQUM7WUFFWjtZQUNBO2dEQUFPO29CQUNMZSxNQUFNLENBQUNFLE9BQU87eURBQUNDLEtBQUssSUFBSTs0QkFDdEJDLFFBQVEsQ0FBQ0UsbUJBQW1CLENBQUNILEtBQUssRUFBRUYsY0FBYyxFQUFFLElBQUksQ0FBQzt3QkFDM0QsQ0FBQzs7b0JBQ0RQLFVBQVUsQ0FBQyxDQUFDO2dCQUNkLENBQUM7O1FBQ0gsQ0FBQzt1Q0FBRTtRQUFDbkIsT0FBTztRQUFFVSxVQUFVO1FBQUVTLFVBQVU7S0FBQyxDQUFDO0lBRXJDLE9BQU87UUFDTFQsVUFBVTtRQUNWUyxVQUFVO1FBQ1ZDO0lBQ0YsQ0FBQztBQUNILENBQUM7QUFFRDs7Q0FFQSxHQUNPLE1BQU1ZLGFBQWEsR0FBR0EsQ0FDM0JDLGNBQXNCLEdBQUcsQ0FBQyxFQUMxQkMsUUFBb0IsRUFDcEJsQyxPQUFnQixHQUFHLElBQUk7SUFFdkIsTUFBTW1DLFNBQVMsR0FBR0YsY0FBYyxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUMsQ0FBQztJQUU5QyxPQUFPcEMsa0JBQWtCLENBQUM7UUFDeEJDLE9BQU8sRUFBRXFDLFNBQVM7UUFDbEJwQyxTQUFTLEVBQUVtQyxRQUFRO1FBQ25CbEM7SUFDRixDQUFDLENBQUM7QUFDSixDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcZmVhdHVyZXNcXGF1dGhcXGhvb2tzXFx1c2VJbmFjdGl2aXR5VGltZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlQmFja2dyb3VuZFRhc2tzIH0gZnJvbSAnQC9jb250ZXh0cy9CYWNrZ3JvdW5kVGFza3NDb250ZXh0JztcblxuaW50ZXJmYWNlIFVzZUluYWN0aXZpdHlUaW1lclByb3BzIHtcbiAgdGltZW91dDogbnVtYmVyOyAvLyBUaWVtcG8gZW4gbWlsaXNlZ3VuZG9zXG4gIG9uVGltZW91dDogKCkgPT4gdm9pZDsgLy8gRnVuY2nDs24gYSBlamVjdXRhciBjdWFuZG8gc2UgYWdvdGUgZWwgdGllbXBvXG4gIGVuYWJsZWQ/OiBib29sZWFuOyAvLyBTaSBlbCB0aW1lciBlc3TDoSBoYWJpbGl0YWRvXG59XG5cbi8qKlxuICogSG9vayBwYXJhIG1hbmVqYXIgbGEgZGVzY29uZXhpw7NuIGF1dG9tw6F0aWNhIHBvciBpbmFjdGl2aWRhZFxuICovXG5leHBvcnQgY29uc3QgdXNlSW5hY3Rpdml0eVRpbWVyID0gKHtcbiAgdGltZW91dCxcbiAgb25UaW1lb3V0LFxuICBlbmFibGVkID0gdHJ1ZVxufTogVXNlSW5hY3Rpdml0eVRpbWVyUHJvcHMpID0+IHtcbiAgY29uc3QgdGltZW91dFJlZiA9IHVzZVJlZjxOb2RlSlMuVGltZW91dCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBsYXN0QWN0aXZpdHlSZWYgPSB1c2VSZWY8bnVtYmVyPihEYXRlLm5vdygpKTtcbiAgY29uc3QgeyB0YXNrcyB9ID0gdXNlQmFja2dyb3VuZFRhc2tzKCk7XG5cbiAgLy8gVmVyaWZpY2FyIHNpIGhheSB0YXJlYXMgZGUgSUEgYWN0aXZhcyBxdWUgZGViZXLDrWFuIHBhdXNhciBlbCB0aW1lclxuICBjb25zdCBoYXNBY3RpdmVBSVRhc2tzID0gdGFza3Muc29tZSh0YXNrID0+XG4gICAgdGFzay5zdGF0dXMgPT09ICdwZW5kaW5nJyB8fCB0YXNrLnN0YXR1cyA9PT0gJ3Byb2Nlc3NpbmcnXG4gICk7XG5cbiAgLy8gRnVuY2nDs24gcGFyYSByZXNldGVhciBlbCB0aW1lclxuICBjb25zdCByZXNldFRpbWVyID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICghZW5hYmxlZCkgcmV0dXJuO1xuXG4gICAgLy8gTGltcGlhciBlbCB0aW1lciBhbnRlcmlvciBzaSBleGlzdGVcbiAgICBpZiAodGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dFJlZi5jdXJyZW50KTtcbiAgICB9XG5cbiAgICAvLyBBY3R1YWxpemFyIGxhIMO6bHRpbWEgYWN0aXZpZGFkXG4gICAgbGFzdEFjdGl2aXR5UmVmLmN1cnJlbnQgPSBEYXRlLm5vdygpO1xuXG4gICAgLy8gU2llbXByZSBjcmVhciB1biBudWV2byB0aW1lciwgaW5kZXBlbmRpZW50ZW1lbnRlIGRlIGxhcyB0YXJlYXMgZGUgSUFcbiAgICAvLyBMYSBhY3RpdmlkYWQgZGVsIHVzdWFyaW8gc2llbXByZSByZWluaWNpYSBlbCB0aW1lclxuICAgIHRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgLy8gU29sbyBlamVjdXRhciBsb2dvdXQgc2kgbm8gaGF5IHRhcmVhcyBkZSBJQSBhY3RpdmFzIGVuIGVsIG1vbWVudG8gZGVsIHRpbWVvdXRcbiAgICAgIGNvbnN0IGN1cnJlbnRUYXNrcyA9IHRhc2tzLmZpbHRlcih0YXNrID0+XG4gICAgICAgIHRhc2suc3RhdHVzID09PSAncGVuZGluZycgfHwgdGFzay5zdGF0dXMgPT09ICdwcm9jZXNzaW5nJ1xuICAgICAgKTtcblxuICAgICAgaWYgKGN1cnJlbnRUYXNrcy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgb25UaW1lb3V0KCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBMb2dvdXQgZGlmZXJpZG86IGhheSB0YXJlYXMgZGUgSUEgZW4gcHJvZ3Jlc28sIHJlaW50ZW50YW5kbyBlbiAxIG1pbnV0bycpO1xuICAgICAgICAvLyBSZWludGVudGFyIGVuIDEgbWludXRvIHNpIGhheSB0YXJlYXMgYWN0aXZhc1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHJlc2V0VGltZXIoKSwgNjAwMDApO1xuICAgICAgfVxuICAgIH0sIHRpbWVvdXQpO1xuICB9LCBbdGltZW91dCwgb25UaW1lb3V0LCBlbmFibGVkLCB0YXNrc10pO1xuXG4gIC8vIEZ1bmNpw7NuIHBhcmEgbGltcGlhciBlbCB0aW1lclxuICBjb25zdCBjbGVhclRpbWVyID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlmICh0aW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0UmVmLmN1cnJlbnQpO1xuICAgICAgdGltZW91dFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyBGdW5jacOzbiBwYXJhIG9idGVuZXIgZWwgdGllbXBvIHJlc3RhbnRlXG4gIGNvbnN0IGdldFRpbWVSZW1haW5pbmcgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFlbmFibGVkIHx8ICF0aW1lb3V0UmVmLmN1cnJlbnQpIHJldHVybiAwO1xuICAgIFxuICAgIGNvbnN0IGVsYXBzZWQgPSBEYXRlLm5vdygpIC0gbGFzdEFjdGl2aXR5UmVmLmN1cnJlbnQ7XG4gICAgY29uc3QgcmVtYWluaW5nID0gTWF0aC5tYXgoMCwgdGltZW91dCAtIGVsYXBzZWQpO1xuICAgIHJldHVybiByZW1haW5pbmc7XG4gIH0sIFt0aW1lb3V0LCBlbmFibGVkXSk7XG5cbiAgLy8gRXZlbnRvcyBxdWUgY29uc2lkZXJhbW9zIGNvbW8gYWN0aXZpZGFkIGRlbCB1c3VhcmlvXG4gIGNvbnN0IGV2ZW50cyA9IFtcbiAgICAnbW91c2Vkb3duJyxcbiAgICAnbW91c2Vtb3ZlJyxcbiAgICAna2V5cHJlc3MnLFxuICAgICdzY3JvbGwnLFxuICAgICd0b3VjaHN0YXJ0JyxcbiAgICAnY2xpY2snLFxuICAgICdrZXlkb3duJ1xuICBdO1xuXG5cblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZW5hYmxlZCkge1xuICAgICAgY2xlYXJUaW1lcigpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8vIEZ1bmNpw7NuIHF1ZSBtYW5lamEgbG9zIGV2ZW50b3MgZGUgYWN0aXZpZGFkXG4gICAgY29uc3QgaGFuZGxlQWN0aXZpdHkgPSAoKSA9PiB7XG4gICAgICByZXNldFRpbWVyKCk7XG4gICAgfTtcblxuICAgIC8vIEFncmVnYXIgZXZlbnQgbGlzdGVuZXJzXG4gICAgZXZlbnRzLmZvckVhY2goZXZlbnQgPT4ge1xuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihldmVudCwgaGFuZGxlQWN0aXZpdHksIHRydWUpO1xuICAgIH0pO1xuXG4gICAgLy8gSW5pY2lhciBlbCB0aW1lclxuICAgIHJlc2V0VGltZXIoKTtcblxuICAgIC8vIENsZWFudXBcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgZXZlbnRzLmZvckVhY2goZXZlbnQgPT4ge1xuICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKGV2ZW50LCBoYW5kbGVBY3Rpdml0eSwgdHJ1ZSk7XG4gICAgICB9KTtcbiAgICAgIGNsZWFyVGltZXIoKTtcbiAgICB9O1xuICB9LCBbZW5hYmxlZCwgcmVzZXRUaW1lciwgY2xlYXJUaW1lcl0pO1xuXG4gIHJldHVybiB7XG4gICAgcmVzZXRUaW1lcixcbiAgICBjbGVhclRpbWVyLFxuICAgIGdldFRpbWVSZW1haW5pbmdcbiAgfTtcbn07XG5cbi8qKlxuICogSG9vayBzaW1wbGlmaWNhZG8gcGFyYSBkZXNjb25leGnDs24gYXV0b23DoXRpY2FcbiAqL1xuZXhwb3J0IGNvbnN0IHVzZUF1dG9Mb2dvdXQgPSAoXG4gIHRpbWVvdXRNaW51dGVzOiBudW1iZXIgPSA1LFxuICBvbkxvZ291dDogKCkgPT4gdm9pZCxcbiAgZW5hYmxlZDogYm9vbGVhbiA9IHRydWVcbikgPT4ge1xuICBjb25zdCB0aW1lb3V0TXMgPSB0aW1lb3V0TWludXRlcyAqIDYwICogMTAwMDsgLy8gQ29udmVydGlyIG1pbnV0b3MgYSBtaWxpc2VndW5kb3NcbiAgXG4gIHJldHVybiB1c2VJbmFjdGl2aXR5VGltZXIoe1xuICAgIHRpbWVvdXQ6IHRpbWVvdXRNcyxcbiAgICBvblRpbWVvdXQ6IG9uTG9nb3V0LFxuICAgIGVuYWJsZWRcbiAgfSk7XG59O1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwidXNlQmFja2dyb3VuZFRhc2tzIiwidXNlSW5hY3Rpdml0eVRpbWVyIiwidGltZW91dCIsIm9uVGltZW91dCIsImVuYWJsZWQiLCJ0aW1lb3V0UmVmIiwibGFzdEFjdGl2aXR5UmVmIiwiRGF0ZSIsIm5vdyIsInRhc2tzIiwiaGFzQWN0aXZlQUlUYXNrcyIsInNvbWUiLCJ0YXNrIiwic3RhdHVzIiwicmVzZXRUaW1lciIsImN1cnJlbnQiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiY3VycmVudFRhc2tzIiwiZmlsdGVyIiwibGVuZ3RoIiwiY29uc29sZSIsImxvZyIsImNsZWFyVGltZXIiLCJnZXRUaW1lUmVtYWluaW5nIiwiZWxhcHNlZCIsInJlbWFpbmluZyIsIk1hdGgiLCJtYXgiLCJldmVudHMiLCJoYW5kbGVBY3Rpdml0eSIsImZvckVhY2giLCJldmVudCIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJ1c2VBdXRvTG9nb3V0IiwidGltZW91dE1pbnV0ZXMiLCJvbkxvZ291dCIsInRpbWVvdXRNcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/features/auth/hooks/useInactivityTimer.ts\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx":
/*!*****************************************************************!*\
  !*** ./src/features/shared/components/BackgroundTasksPanel.tsx ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChevronUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ChevronDownIcon,ChevronUpIcon,ClockIcon,Cog6ToothIcon,ExclamationCircleIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\BackgroundTasksPanel.tsx\";\n\n\n\n\nconst BackgroundTasksPanel = ()=>{\n    const { activeTasks, completedTasks, removeTask, clearCompletedTasks } = (0,_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_1__.useBackgroundTasks)();\n    const { 0: isExpanded, 1: setIsExpanded } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { 0: showCompleted, 1: setShowCompleted } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const totalTasks = activeTasks.length + completedTasks.length;\n    if (totalTasks === 0) {\n        return null;\n    }\n    const getTaskIcon = (task)=>{\n        switch(task.status){\n            case 'pending':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 29,\n                    columnNumber: 16\n                }, undefined);\n            case 'processing':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 31,\n                    columnNumber: 16\n                }, undefined);\n            case 'completed':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 33,\n                    columnNumber: 16\n                }, undefined);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 35,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 37,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getTaskTypeLabel = (type)=>{\n        switch(type){\n            case 'mapa-mental':\n                return 'Mapa Mental';\n            case 'test':\n                return 'Test';\n            case 'flashcards':\n                return 'Flashcards';\n            default:\n                return 'Tarea';\n        }\n    };\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('es-ES', {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 z-50 max-w-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 cursor-pointer flex items-center justify-between\",\n                    onClick: ()=>setIsExpanded(!isExpanded),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: [\n                                        \"Tareas (\",\n                                        activeTasks.length,\n                                        \" activas)\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, undefined),\n                        isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 77,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined),\n                isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                    className: \"max-h-96 overflow-y-auto\",\n                    children: [\n                        activeTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                            className: \"p-3 border-b border-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-semibold text-gray-700 mb-2\",\n                                    children: \"Tareas Activas\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: activeTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 103,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        task.progress !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                                            className: \"mt-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gray-200 rounded-full h-1.5\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-500 h-1.5 rounded-full transition-all duration-300\",\n                                                                    style: {\n                                                                        width: `${task.progress}%`\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 108,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 107,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 99,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: formatTime(task.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 117,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                            className: \"p-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowCompleted(!showCompleted),\n                                            className: \"text-sm font-semibold text-gray-700 hover:text-gray-900 flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Completadas (\",\n                                                        completedTasks.length,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 134,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                showCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 136,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        completedTasks.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"button\", {\n                                            onClick: clearCompletedTasks,\n                                            className: \"text-xs text-gray-500 hover:text-red-600 transition-colors\",\n                                            children: \"Limpiar\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 142,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 129,\n                                    columnNumber: 17\n                                }, undefined),\n                                showCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: completedTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 p-2 bg-gray-50 rounded-md\",\n                                            children: [\n                                                getTaskIcon(task),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900 truncate\",\n                                                            children: getTaskTypeLabel(task.type)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 truncate\",\n                                                            children: task.title\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 163,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        task.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-500 truncate\",\n                                                            children: task.error\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 167,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 159,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-400\",\n                                                            children: task.completedAt ? formatTime(task.completedAt) : formatTime(task.createdAt)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 173,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeTask(task.id),\n                                                            className: \"text-gray-400 hover:text-red-500 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ChevronDownIcon_ChevronUpIcon_ClockIcon_Cog6ToothIcon_ExclamationCircleIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 180,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 176,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 172,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 154,\n                                            columnNumber: 23\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 152,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BackgroundTasksPanel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/features/shared/components/ClientLayout.tsx":
/*!*********************************************************!*\
  !*** ./src/features/shared/components/ClientLayout.tsx ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/BackgroundTasksContext */ \"(ssr)/./src/contexts/BackgroundTasksContext.tsx\");\n/* harmony import */ var _auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../auth/components/AuthManager */ \"(ssr)/./src/features/auth/components/AuthManager.tsx\");\n/* harmony import */ var _BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./BackgroundTasksPanel */ \"(ssr)/./src/features/shared/components/BackgroundTasksPanel.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ default auto */ var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\shared\\\\components\\\\ClientLayout.tsx\";\n\n\n\n\n\n // Importar Toaster\n\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(_contexts_BackgroundTasksContext__WEBPACK_IMPORTED_MODULE_2__.BackgroundTasksProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(_auth_components_AuthManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                    position: \"top-right\" // Posición de los toasts\n                    ,\n                    toastOptions: {\n                        // Opciones por defecto para los toasts\n                        duration: 5000,\n                        // Duración de 5 segundos\n                        style: {\n                            background: '#363636',\n                            // Estilo oscuro\n                            color: '#fff'\n                        },\n                        success: {\n                            duration: 3000,\n                            style: {\n                                background: '#10b981',\n                                color: '#fff'\n                            }\n                        },\n                        error: {\n                            duration: 5000,\n                            style: {\n                                background: '#ef4444',\n                                color: '#fff'\n                            }\n                        }\n                    }\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxDEV)(_BackgroundTasksPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                children\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/features/shared/components/ClientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/authService.ts":
/*!*****************************************!*\
  !*** ./src/lib/supabase/authService.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cerrarSesion: () => (/* binding */ cerrarSesion),\n/* harmony export */   estaAutenticado: () => (/* binding */ estaAutenticado),\n/* harmony export */   iniciarSesion: () => (/* binding */ iniciarSesion),\n/* harmony export */   obtenerSesion: () => (/* binding */ obtenerSesion),\n/* harmony export */   obtenerUsuarioActual: () => (/* binding */ obtenerUsuarioActual)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(ssr)/./src/lib/supabase/supabaseClient.ts\");\n\n/**\r\n * Inicia sesión con email y contraseña\r\n */ async function iniciarSesion(email, password) {\n    try {\n        // Verificar que el email y la contraseña no estén vacíos\n        if (!email || !password) {\n            return {\n                user: null,\n                session: null,\n                // Added session\n                error: 'Por favor, ingresa tu email y contraseña'\n            };\n        }\n        // No cerramos la sesión antes de iniciar una nueva, esto causa un ciclo\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email: email.trim(),\n            password: password\n        });\n        if (error) {\n            // Manejar específicamente el error de sincronización de tiempo\n            if (error.message.includes('issued in the future') || error.message.includes('clock for skew')) {\n                return {\n                    user: null,\n                    session: null,\n                    // Added session\n                    error: 'Error de sincronización de tiempo. Por favor, verifica que la hora de tu dispositivo esté correctamente configurada.'\n                };\n            }\n            // Manejar error de credenciales inválidas de forma más amigable\n            if (error.message.includes('Invalid login credentials')) {\n                return {\n                    user: null,\n                    session: null,\n                    // Added session\n                    error: 'Email o contraseña incorrectos. Por favor, verifica tus credenciales.'\n                };\n            }\n            return {\n                user: null,\n                session: null,\n                error: error.message\n            }; // Added session\n        }\n        // Ensure data.user and data.session exist before returning\n        if (data && data.user && data.session) {\n            // Esperar un momento para asegurar que las cookies se establezcan\n            // Esto es importante para que el middleware pueda detectar la sesión\n            await new Promise((resolve)=>setTimeout(resolve, 800));\n            // Verificar que la sesión esté disponible después de establecer las cookies\n            await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            return {\n                user: data.user,\n                session: data.session,\n                error: null\n            }; // Added session\n        } else {\n            // This case should ideally not be reached if Supabase call is successful\n            // but provides a fallback if data or its properties are unexpectedly null/undefined.\n            return {\n                user: null,\n                session: null,\n                error: 'Respuesta inesperada del servidor al iniciar sesión.'\n            };\n        }\n    } catch (e) {\n        // Changed 'error' to 'e' to avoid conflict with 'error' from signInWithPassword\n        // Check if 'e' is an Error object and has a message property\n        const errorMessage = e instanceof Error && e.message ? e.message : 'Ha ocurrido un error inesperado al iniciar sesión';\n        return {\n            user: null,\n            session: null,\n            // Added session\n            error: errorMessage\n        };\n    }\n}\n/**\r\n * Cierra la sesión del usuario actual\r\n */ async function cerrarSesion() {\n    try {\n        console.log('🔓 Iniciando proceso de logout...');\n        // Cerrar sesión con scope 'global' para limpiar tanto local como servidor\n        const { error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut({\n            scope: 'global'\n        });\n        if (error) {\n            console.error('❌ Error en signOut:', error.message);\n            return {\n                error: error.message\n            };\n        }\n        console.log('✅ SignOut exitoso');\n        // Limpiar cualquier dato de sesión residual del localStorage/sessionStorage\n        if (false) {}\n        return {\n            error: null\n        };\n    } catch (error) {\n        console.error('❌ Error inesperado en logout:', error);\n        return {\n            error: 'Ha ocurrido un error inesperado al cerrar sesión'\n        };\n    }\n}\n/**\r\n * Obtiene la sesión actual del usuario\r\n */ async function obtenerSesion() {\n    try {\n        const { data, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    session: null,\n                    error: null\n                };\n            }\n            return {\n                session: null,\n                error: error.message\n            };\n        }\n        return {\n            session: data.session,\n            error: null\n        };\n    } catch (error) {\n        return {\n            session: null,\n            error: 'Ha ocurrido un error inesperado al obtener la sesión'\n        };\n    }\n}\n/**\r\n * Obtiene el usuario actual\r\n */ async function obtenerUsuarioActual() {\n    try {\n        const { data: { user }, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (error) {\n            // Si el error es \"Auth session missing\", es un caso esperado cuando no hay sesión\n            if (error.message === 'Auth session missing!') {\n                return {\n                    user: null,\n                    error: null\n                };\n            }\n            return {\n                user: null,\n                error: error.message\n            };\n        }\n        return {\n            user,\n            error: null\n        };\n    } catch (error) {\n        return {\n            user: null,\n            error: 'Ha ocurrido un error inesperado al obtener el usuario actual'\n        };\n    }\n}\n/**\r\n * Verifica si el usuario está autenticado\r\n */ async function estaAutenticado() {\n    const { session } = await obtenerSesion();\n    return session !== null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/authService.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/client.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/client.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n// Cliente para el navegador (componentes del cliente)\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        auth: {\n            persistSession: true,\n            // Persistir sesión en el navegador\n            autoRefreshToken: true,\n            // Refrescar token automáticamente\n            detectSessionInUrl: true // ESENCIAL: Detectar y procesar tokens de URL\n        }\n    });\n}\n// Mantener compatibilidad con código existente\nconst supabase = createClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3N1cGFiYXNlL2NsaWVudC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBbUQ7QUFFbkQ7QUFDTyxTQUFTQyxZQUFZQSxDQUFBLEVBQUc7SUFDN0IsT0FBT0Qsa0VBQW1CLENBQ3hCRSwwQ0FBb0MsRUFDcENBLGtOQUF5QyxFQUN6QztRQUNFSSxJQUFJLEVBQUU7WUFDSkMsY0FBYyxFQUFFLElBQUk7WUFBUTtZQUM1QkMsZ0JBQWdCLEVBQUUsSUFBSTtZQUFNO1lBQzVCQyxrQkFBa0IsRUFBRSxJQUFJLENBQUk7UUFDOUI7SUFDRixDQUNGLENBQUM7QUFDSDtBQUVBO0FBQ08sTUFBTUMsUUFBUSxHQUFHVCxZQUFZLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxuYWF0YVxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxPcG9zSVxcdjE2XFxzcmNcXGxpYlxcc3VwYWJhc2VcXGNsaWVudC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3Nzcic7XG5cbi8vIENsaWVudGUgcGFyYSBlbCBuYXZlZ2Fkb3IgKGNvbXBvbmVudGVzIGRlbCBjbGllbnRlKVxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUJyb3dzZXJDbGllbnQoXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMISxcbiAgICBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSEsXG4gICAge1xuICAgICAgYXV0aDoge1xuICAgICAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSwgICAgICAgLy8gUGVyc2lzdGlyIHNlc2nDs24gZW4gZWwgbmF2ZWdhZG9yXG4gICAgICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsICAgICAvLyBSZWZyZXNjYXIgdG9rZW4gYXV0b23DoXRpY2FtZW50ZVxuICAgICAgICBkZXRlY3RTZXNzaW9uSW5Vcmw6IHRydWUgICAgLy8gRVNFTkNJQUw6IERldGVjdGFyIHkgcHJvY2VzYXIgdG9rZW5zIGRlIFVSTFxuICAgICAgfVxuICAgIH1cbiAgKTtcbn1cblxuLy8gTWFudGVuZXIgY29tcGF0aWJpbGlkYWQgY29uIGPDs2RpZ28gZXhpc3RlbnRlXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoKTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlQ2xpZW50IiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiYXV0aCIsInBlcnNpc3RTZXNzaW9uIiwiYXV0b1JlZnJlc2hUb2tlbiIsImRldGVjdFNlc3Npb25JblVybCIsInN1cGFiYXNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase/supabaseClient.ts":
/*!********************************************!*\
  !*** ./src/lib/supabase/supabaseClient.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.createClient),\n/* harmony export */   supabase: () => (/* reexport safe */ _client__WEBPACK_IMPORTED_MODULE_0__.supabase)\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(ssr)/./src/lib/supabase/client.ts\");\n// Solo re-exportar el cliente del navegador para mantener compatibilidad\n // NOTA: Para usar el cliente del servidor, importar directamente desde './server'\n // import { createServerSupabaseClient } from '@/lib/supabase/server';\n // Tipos comunes\n // Tipos del sistema de pagos y perfiles extendidos\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase/supabaseClient.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/react-icons","vendor-chunks/react-hot-toast","vendor-chunks/@heroicons","vendor-chunks/goober","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();