"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TokenStatsModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenStatsModal)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiActivity,FiArrowUp,FiBarChart,FiLock,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _lib_supabase_tokenUsageService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase/tokenUsageService */ \"(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts\");\n/* harmony import */ var _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePlanLimits */ \"(app-pages-browser)/./src/hooks/usePlanLimits.ts\");\n/* harmony import */ var _TokenProgressBar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./TokenProgressBar */ \"(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\");\n/* harmony import */ var _TokenPurchaseButton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./TokenPurchaseButton */ \"(app-pages-browser)/./src/components/ui/TokenPurchaseButton.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenStatsModal.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction TokenStatsModal(_ref) {\n    _s();\n    _s1();\n    var _this = this;\n    var isOpen = _ref.isOpen, onClose = _ref.onClose, _ref$shouldRefreshOnO = _ref.shouldRefreshOnOpen, shouldRefreshOnOpen = _ref$shouldRefreshOnO === void 0 ? false : _ref$shouldRefreshOnO;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), stats = _useState[0], setStats = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), loading = _useState2[0], setLoading = _useState2[1];\n    var planLimits = (0,_hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits)();\n    // Refrescar datos cuando se abre el modal si es necesario\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && shouldRefreshOnOpen) {\n                planLimits.refresh();\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        shouldRefreshOnOpen,\n        planLimits\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && planLimits.userPlan && planLimits.userPlan !== 'free') {\n                loadStats();\n            } else if (isOpen && planLimits.userPlan === 'free') {\n                setStats(null);\n                setLoading(false);\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        planLimits.userPlan\n    ]);\n    var loadStats = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {\n            var currentStats;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        setLoading(true);\n                        _context.next = 4;\n                        return (0,_lib_supabase_tokenUsageService__WEBPACK_IMPORTED_MODULE_4__.getUserTokenStats)();\n                    case 4:\n                        currentStats = _context.sent;\n                        setStats(currentStats);\n                        _context.next = 11;\n                        break;\n                    case 8:\n                        _context.prev = 8;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error al cargar estadísticas:', _context.t0);\n                    case 11:\n                        _context.prev = 11;\n                        setLoading(false);\n                        return _context.finish(11);\n                    case 14:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    8,\n                    11,\n                    14\n                ]\n            ]);\n        }));\n        return function loadStats() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    if (!isOpen) return null;\n    var formatTokens = function formatTokens(tokens) {\n        return tokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Estad\\xEDsticas de Uso de IA\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiX, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 67,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: planLimits.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Verificando plan...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this) : planLimits.userPlan === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiLock, {\n                                className: \"mx-auto h-16 w-16 text-gray-400 mb-6\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Estad\\xEDsticas de Tokens no disponibles\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                            children: \"Plan Gratuito:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Las estad\\xEDsticas avanzadas y la compra de tokens est\\xE1n disponibles solo para usuarios con planes de pago.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Actualiza tu plan para acceder a:\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-700 space-y-2 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                                        className: \"w-4 h-4 text-blue-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 95,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Estad\\xEDsticas detalladas de uso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 94,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity, {\n                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"An\\xE1lisis por actividad y modelo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiArrowUp, {\n                                                        className: \"w-4 h-4 text-purple-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seguimiento de progreso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_8___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiArrowUp, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ver Planes Disponibles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 108,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this) : loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 120,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Cargando estad\\xEDsticas...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 119,\n                        columnNumber: 13\n                    }, this) : stats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                        children: [\n                            planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_TokenProgressBar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    used: planLimits.tokenUsage.current || 0,\n                                    limit: planLimits.tokenUsage.limit || 0,\n                                    percentage: planLimits.tokenUsage.percentage || 0,\n                                    remaining: planLimits.tokenUsage.remaining || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 128,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 127,\n                                columnNumber: 17\n                            }, this),\n                            planLimits.tokenUsage && (planLimits.tokenUsage.percentage || 0) >= 80 && planLimits.userPlan && planLimits.userPlan !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_TokenPurchaseButton__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    userPlan: planLimits.userPlan,\n                                    currentTokens: planLimits.tokenUsage.current || 0,\n                                    tokenLimit: planLimits.tokenUsage.limit || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 140,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 139,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiActivity, {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 152,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-blue-900\",\n                                                        children: \"Total Sesiones\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900 mt-1\",\n                                                children: stats.totalSessions\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(_barrel_optimize_names_FiActivity_FiArrowUp_FiBarChart_FiLock_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart, {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-900\",\n                                                        children: \"Tokens Consumidos (Hist\\xF3rico)\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900 mt-1\",\n                                                children: formatTokens(stats.totalTokens)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Actividad\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Actividad\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 178,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 177,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byActivity).map(function(_ref3) {\n                                                        var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref3, 2), activity = _ref4[0], data = _ref4[1];\n                                                        var activityData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                    children: activity\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: activityData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(activityData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, activity, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Modelo\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Modelo\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byModel).map(function(_ref5) {\n                                                        var _ref6 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref5, 2), model = _ref6[0], data = _ref6[1];\n                                                        var modelData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900 font-mono\",\n                                                                    children: model\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: modelData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(modelData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, model, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 215,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                            children: \"Nota:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 230,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Los datos de uso de tokens se almacenan en Supabase. Los tokens de entrada y salida se registran autom\\xE1ticamente para cada actividad de IA.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 229,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No hay datos de uso disponibles.\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenStatsModal, \"WYDFYwW2tqIbkxDwNiPAn5eB8Ws=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits\n    ];\n});\n_c1 = TokenStatsModal;\n_s1(TokenStatsModal, \"1F00vAQqTrEahcnC0EvNidQjiF0=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_5__.usePlanLimits\n    ];\n});\n_c = TokenStatsModal;\nvar _c;\n$RefreshReg$(_c, \"TokenStatsModal\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenStatsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\n"));

/***/ })

});