"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx":
/*!****************************************************************!*\
  !*** ./src/features/summaries/components/SummaryGenerator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryGenerator)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/resumenesService */ \"(app-pages-browser)/./src/lib/supabase/resumenesService.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\summaries\\\\components\\\\SummaryGenerator.tsx\", _s1 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\nvar summarySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    instrucciones: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres')\n});\nfunction SummaryGenerator(_ref) {\n    _s();\n    _s1();\n    var documentosSeleccionados = _ref.documentosSeleccionados, onSummaryGenerated = _ref.onSummaryGenerated;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false), isGenerating = _useState[0], setIsGenerating = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), resumenGenerado = _useState2[0], setResumenGenerado = _useState2[1];\n    var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), tiempoEstimado = _useState3[0], setTiempoEstimado = _useState3[1];\n    var _useForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(summarySchema),\n        defaultValues: {\n            instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'\n        }\n    }), register = _useForm.register, handleSubmit = _useForm.handleSubmit, errors = _useForm.formState.errors, reset = _useForm.reset;\n    // Funciones de validación\n    var validarDocumentoParaResumen = function validarDocumentoParaResumen(documento) {\n        if (!documento) {\n            return {\n                valido: false,\n                error: 'No se ha proporcionado ningún documento'\n            };\n        }\n        if (!documento.titulo || documento.titulo.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener un título'\n            };\n        }\n        if (!documento.contenido || documento.contenido.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener contenido'\n            };\n        }\n        if (documento.contenido.trim().length < 50) {\n            return {\n                valido: false,\n                error: 'El contenido del documento es demasiado corto para generar un resumen útil'\n            };\n        }\n        return {\n            valido: true\n        };\n    };\n    var estimarTiempoGeneracion = function estimarTiempoGeneracion(documento) {\n        if (!documento || !documento.contenido) {\n            return 30; // 30 segundos por defecto\n        }\n        var palabras = documento.contenido.split(/\\s+/).length;\n        // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos\n        var tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));\n        return tiempoEstimado;\n    };\n    // Validaciones\n    var puedeGenerar = documentosSeleccionados.length === 1;\n    var documento = documentosSeleccionados[0];\n    var validacionDocumento = documento ? validarDocumentoParaResumen(documento) : {\n        valido: false,\n        error: 'No hay documento seleccionado'\n    };\n    var onSubmit = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee(data) {\n            var _documento$contenido, _requestData$contexto, _result$result, _result$result2, existe, requestData, response, responseText, result, resumenContent, resumenId, errorMessage, errorObj;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        if (!(!puedeGenerar || !documento || !validacionDocumento.valido)) {\n                            _context.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');\n                        return _context.abrupt(\"return\");\n                    case 3:\n                        _context.prev = 3;\n                        console.log('🚀 Iniciando generación de resumen...');\n                        setIsGenerating(true);\n                        setTiempoEstimado(estimarTiempoGeneracion(documento));\n                        console.log('📄 Documento seleccionado:', {\n                            id: documento.id,\n                            titulo: documento.titulo,\n                            categoria: documento.categoria,\n                            numero_tema: documento.numero_tema,\n                            contenidoLength: ((_documento$contenido = documento.contenido) === null || _documento$contenido === void 0 ? void 0 : _documento$contenido.length) || 0\n                        });\n                        // Verificar si ya existe un resumen para este documento\n                        console.log('🔍 Verificando si ya existe resumen...');\n                        _context.next = 11;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.existeResumenParaDocumento)(documento.id);\n                    case 11:\n                        existe = _context.sent;\n                        if (!existe) {\n                            _context.next = 17;\n                            break;\n                        }\n                        console.log('⚠️ Ya existe un resumen para este documento');\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');\n                        setIsGenerating(false);\n                        return _context.abrupt(\"return\");\n                    case 17:\n                        console.log('✅ No existe resumen previo, continuando...');\n                        // Preparar datos para la API\n                        requestData = {\n                            action: 'generarResumen',\n                            peticion: \"\".concat(documento.titulo, \"|\").concat(documento.categoria || '', \"|\").concat(documento.numero_tema || '', \"|\").concat(data.instrucciones),\n                            contextos: [\n                                documento.contenido\n                            ]\n                        };\n                        console.log('📡 Enviando petición a la API:', {\n                            action: requestData.action,\n                            peticion: requestData.peticion,\n                            contextosLength: requestData.contextos.length,\n                            primerContextoLength: ((_requestData$contexto = requestData.contextos[0]) === null || _requestData$contexto === void 0 ? void 0 : _requestData$contexto.length) || 0\n                        });\n                        // Generar el resumen usando la API\n                        _context.next = 22;\n                        return fetch('/api/ai', {\n                            method: 'POST',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify(requestData)\n                        });\n                    case 22:\n                        response = _context.sent;\n                        console.log(\"\\uD83D\\uDCE8 Respuesta recibida - Status: \".concat(response.status, \", OK: \").concat(response.ok));\n                        // Capturar respuesta como texto primero para depuración\n                        _context.next = 26;\n                        return response.text();\n                    case 26:\n                        responseText = _context.sent;\n                        console.log('📄 Respuesta como texto (primeros 200 chars):', responseText.substring(0, 200));\n                        // Intentar parsear como JSON\n                        _context.prev = 28;\n                        result = JSON.parse(responseText);\n                        console.log('✅ JSON parseado exitosamente');\n                        _context.next = 38;\n                        break;\n                    case 33:\n                        _context.prev = 33;\n                        _context.t0 = _context[\"catch\"](28);\n                        console.error('❌ Error al parsear respuesta JSON:', _context.t0);\n                        console.error('📄 Respuesta completa:', responseText);\n                        throw new Error(\"Error en formato de respuesta: \".concat(responseText.substring(0, 100), \"...\"));\n                    case 38:\n                        if (response.ok) {\n                            _context.next = 41;\n                            break;\n                        }\n                        console.error('❌ Error en respuesta de la API:', result);\n                        throw new Error(result.detalles || result.error || 'Error al generar el resumen');\n                    case 41:\n                        console.log('📋 Resultado de la API:', {\n                            hasResult: !!result.result,\n                            resultLength: ((_result$result = result.result) === null || _result$result === void 0 ? void 0 : _result$result.length) || 0,\n                            resultPreview: ((_result$result2 = result.result) === null || _result$result2 === void 0 ? void 0 : _result$result2.substring(0, 100)) || 'Sin contenido'\n                        });\n                        resumenContent = result.result;\n                        if (resumenContent) {\n                            _context.next = 46;\n                            break;\n                        }\n                        console.error('❌ No se recibió contenido del resumen');\n                        throw new Error('No se recibió contenido del resumen');\n                    case 46:\n                        console.log('✅ Contenido del resumen recibido, longitud:', resumenContent.length);\n                        setResumenGenerado(resumenContent);\n                        // Guardar en Supabase\n                        console.log('💾 Guardando resumen en Supabase...');\n                        _context.next = 51;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.guardarResumen)(documento.id, \"Resumen: \".concat(documento.titulo), resumenContent, data.instrucciones);\n                    case 51:\n                        resumenId = _context.sent;\n                        if (!resumenId) {\n                            _context.next = 59;\n                            break;\n                        }\n                        console.log('✅ Resumen guardado exitosamente con ID:', resumenId);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success('Resumen generado y guardado exitosamente');\n                        onSummaryGenerated === null || onSummaryGenerated === void 0 || onSummaryGenerated(resumenId);\n                        reset();\n                        _context.next = 61;\n                        break;\n                    case 59:\n                        console.error('❌ Error al guardar el resumen - no se recibió ID');\n                        throw new Error('Error al guardar el resumen en la base de datos');\n                    case 61:\n                        _context.next = 70;\n                        break;\n                    case 63:\n                        _context.prev = 63;\n                        _context.t1 = _context[\"catch\"](3);\n                        console.error('Error completo al generar resumen:', _context.t1);\n                        // Manejo mejorado de errores con más detalles\n                        errorMessage = 'Error al generar el resumen';\n                        if (_context.t1 instanceof Error) {\n                            errorMessage = _context.t1.message;\n                        } else if (typeof _context.t1 === 'object' && _context.t1 !== null) {\n                            // Si es un objeto, intentar extraer información útil\n                            errorObj = _context.t1;\n                            if (errorObj.message) {\n                                errorMessage = errorObj.message;\n                            } else if (errorObj.error) {\n                                errorMessage = errorObj.error;\n                            } else if (errorObj.detalles) {\n                                errorMessage = errorObj.detalles;\n                            } else {\n                                errorMessage = \"Error del servidor: \".concat(JSON.stringify(_context.t1));\n                            }\n                        } else if (typeof _context.t1 === 'string') {\n                            errorMessage = _context.t1;\n                        }\n                        console.error('Mensaje de error procesado:', errorMessage);\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(errorMessage);\n                    case 70:\n                        _context.prev = 70;\n                        setIsGenerating(false);\n                        setTiempoEstimado(0);\n                        return _context.finish(70);\n                    case 74:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    3,\n                    63,\n                    70,\n                    74\n                ],\n                [\n                    28,\n                    33\n                ]\n            ]);\n        }));\n        return function onSubmit(_x) {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCC4 Generaci\\xF3n de Res\\xFAmenes\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    !puedeGenerar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Selecci\\xF3n incorrecta\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 230,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: documentosSeleccionados.length === 0 ? 'Debes seleccionar exactamente un documento para generar un resumen.' : \"Tienes \".concat(documentosSeleccionados.length, \" documentos seleccionados. Solo se permite generar un resumen por tema.\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 11\n                    }, this) : !validacionDocumento.valido ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Documento no v\\xE1lido\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: validacionDocumento.error\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u2705 Documento seleccionado:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"strong\", {\n                                        children: documento.titulo\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    documento.numero_tema && \" (Tema \".concat(documento.numero_tema, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-blue-600\",\n                                children: [\n                                    \"Contenido: ~\",\n                                    documento.contenido.split(/\\s+/).length,\n                                    \" palabras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            puedeGenerar && validacionDocumento.valido && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"label\", {\n                                htmlFor: \"instrucciones\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Instrucciones para el resumen:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"textarea\", _objectSpread(_objectSpread({\n                                id: \"instrucciones\"\n                            }, register('instrucciones')), {}, {\n                                disabled: isGenerating,\n                                rows: 4,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical\",\n                                placeholder: \"Describe c\\xF3mo quieres que sea el resumen...\"\n                            }), void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this),\n                            errors.instrucciones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.instrucciones.message\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isGenerating,\n                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generando resumen...\",\n                                tiempoEstimado > 0 && \" (~\".concat(tiempoEstimado, \"s)\")\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 297,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generar Resumen\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, this),\n                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"strong\", {\n                                    children: \"\\u23F3 Generando resumen...\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 308,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 308,\n                                    columnNumber: 56\n                                }, this),\n                                \"La IA est\\xE1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar \",\n                                tiempoEstimado > 0 ? \"aproximadamente \".concat(tiempoEstimado, \" segundos\") : 'unos momentos',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 307,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 9\n            }, this),\n            resumenGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-3\",\n                        children: \"\\uD83D\\uDCCB Resumen Generado\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: resumenGenerado.replace(/\\n/g, '<br />')\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-2\",\n                        children: \"\\u2705 Resumen guardado exitosamente. Puedes acceder a \\xE9l desde la secci\\xF3n de res\\xFAmenes.\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(SummaryGenerator, \"04yMkZpkFH9RQMV2ABeTq2/S+lc=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm\n    ];\n});\n_c1 = SummaryGenerator;\n_s1(SummaryGenerator, \"z3q7X9+XFpGJEKOBIbI78sdfPTY=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_9__.useForm\n    ];\n});\n_c = SummaryGenerator;\nvar _c;\n$RefreshReg$(_c, \"SummaryGenerator\");\nvar _c1;\n$RefreshReg$(_c1, \"SummaryGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\n"));

/***/ })

});