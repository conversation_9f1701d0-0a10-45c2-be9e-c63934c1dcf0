"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx":
/*!***********************************************!*\
  !*** ./src/components/ui/TokenStatsModal.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TokenStatsModal)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/slicedToArray.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePlanLimits */ \"(app-pages-browser)/./src/hooks/usePlanLimits.ts\");\n/* harmony import */ var _TokenProgressBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TokenProgressBar */ \"(app-pages-browser)/./src/components/ui/TokenProgressBar.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\components\\\\ui\\\\TokenStatsModal.tsx\", _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction TokenStatsModal(_ref) {\n    _s();\n    _s1();\n    var _this = this;\n    var isOpen = _ref.isOpen, onClose = _ref.onClose, _ref$shouldRefreshOnO = _ref.shouldRefreshOnOpen, shouldRefreshOnOpen = _ref$shouldRefreshOnO === void 0 ? false : _ref$shouldRefreshOnO;\n    var _useState = useState(null), _useState2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useState, 2), stats = _useState2[0], setStats = _useState2[1];\n    var _useState3 = useState(false), _useState4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_useState3, 2), loading = _useState4[0], setLoading = _useState4[1];\n    var planLimits = (0,_hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_4__.usePlanLimits)();\n    // Refrescar datos cuando se abre el modal si es necesario\n    useEffect({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && shouldRefreshOnOpen) {\n                planLimits.refresh();\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        shouldRefreshOnOpen,\n        planLimits\n    ]);\n    useEffect({\n        \"TokenStatsModal.useEffect\": function() {\n            if (isOpen && planLimits.userPlan && planLimits.userPlan !== 'free') {\n                loadStats();\n            } else if (isOpen && planLimits.userPlan === 'free') {\n                setStats(null);\n                setLoading(false);\n            }\n        }\n    }[\"TokenStatsModal.useEffect\"], [\n        isOpen,\n        planLimits.userPlan\n    ]);\n    var loadStats = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee() {\n            var currentStats;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                while(1)switch(_context.prev = _context.next){\n                    case 0:\n                        _context.prev = 0;\n                        setLoading(true);\n                        _context.next = 4;\n                        return getUserTokenStats();\n                    case 4:\n                        currentStats = _context.sent;\n                        setStats(currentStats);\n                        _context.next = 11;\n                        break;\n                    case 8:\n                        _context.prev = 8;\n                        _context.t0 = _context[\"catch\"](0);\n                        console.error('Error al cargar estadísticas:', _context.t0);\n                    case 11:\n                        _context.prev = 11;\n                        setLoading(false);\n                        return _context.finish(11);\n                    case 14:\n                    case \"end\":\n                        return _context.stop();\n                }\n            }, _callee, null, [\n                [\n                    0,\n                    8,\n                    11,\n                    14\n                ]\n            ]);\n        }));\n        return function loadStats() {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    if (!isOpen) return null;\n    var formatTokens = function formatTokens(tokens) {\n        var validTokens = tokens || 0;\n        return validTokens.toLocaleString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBarChart, {\n                                    className: \"w-6 h-6 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 61,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"Estad\\xEDsticas de Uso de IA\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"p-2 hover:bg-gray-100 rounded-lg transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiX, {\n                                className: \"w-5 h-5 text-gray-500\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: planLimits.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Verificando plan...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 75,\n                        columnNumber: 13\n                    }, this) : planLimits.userPlan === 'free' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(FiLock, {\n                                className: \"mx-auto h-16 w-16 text-gray-400 mb-6\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 81,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                children: \"Estad\\xEDsticas de Tokens no disponibles\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 82,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-yellow-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"strong\", {\n                                            children: \"Plan Gratuito:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 87,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Las estad\\xEDsticas avanzadas y la compra de tokens est\\xE1n disponibles solo para usuarios con planes de pago.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 86,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Actualiza tu plan para acceder a:\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-gray-700 space-y-2 max-w-sm mx-auto\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBarChart, {\n                                                        className: \"w-4 h-4 text-blue-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 96,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Estad\\xEDsticas detalladas de uso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(FiActivity, {\n                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 100,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"An\\xE1lisis por actividad y modelo\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"li\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(FiArrowUp, {\n                                                        className: \"w-4 h-4 text-purple-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 104,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Seguimiento de progreso\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 103,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                        className: \"pt-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                                            href: \"/\",\n                                            className: \"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(FiArrowUp, {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Ver Planes Disponibles\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this) : loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"span\", {\n                                className: \"ml-2 text-gray-600\",\n                                children: \"Cargando estad\\xEDsticas...\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 122,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 120,\n                        columnNumber: 13\n                    }, this) : stats ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.Fragment, {\n                        children: [\n                            planLimits.tokenUsage && planLimits.tokenUsage.limit > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(_TokenProgressBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    used: planLimits.tokenUsage.current || 0,\n                                    limit: planLimits.tokenUsage.limit || 0,\n                                    percentage: planLimits.tokenUsage.percentage || 0,\n                                    remaining: planLimits.tokenUsage.remaining || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 129,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 128,\n                                columnNumber: 17\n                            }, this),\n                            planLimits.tokenUsage && (planLimits.tokenUsage.percentage || 0) >= 80 && planLimits.userPlan && planLimits.userPlan !== 'free' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(TokenPurchaseButton, {\n                                    userPlan: planLimits.userPlan,\n                                    currentTokens: planLimits.tokenUsage.current || 0,\n                                    tokenLimit: planLimits.tokenUsage.limit || 0\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 141,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 140,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(FiActivity, {\n                                                        className: \"w-5 h-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-blue-900\",\n                                                        children: \"Total Sesiones\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 154,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-900 mt-1\",\n                                                children: stats.totalSessions\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 rounded-lg p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBarChart, {\n                                                        className: \"w-5 h-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-green-900\",\n                                                        children: \"Tokens Consumidos (Hist\\xF3rico)\"\n                                                    }, void 0, false, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-900 mt-1\",\n                                                children: formatTokens(stats.totalTokens)\n                                            }, void 0, false, {\n                                                fileName: _jsxFileName,\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Actividad\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Actividad\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 179,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 180,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 181,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byActivity).map(function(_ref3) {\n                                                        var _ref4 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref3, 2), activity = _ref4[0], data = _ref4[1];\n                                                        var activityData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900\",\n                                                                    children: activity\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: activityData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(activityData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, activity, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Uso por Modelo\"\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 rounded-lg overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"table\", {\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"thead\", {\n                                                    className: \"bg-gray-100\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"tr\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-left text-sm font-medium text-gray-700\",\n                                                                children: \"Modelo\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Sesiones\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"th\", {\n                                                                className: \"px-4 py-3 text-right text-sm font-medium text-gray-700\",\n                                                                children: \"Tokens\"\n                                                            }, void 0, false, {\n                                                                fileName: _jsxFileName,\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: _jsxFileName,\n                                                        lineNumber: 206,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"tbody\", {\n                                                    className: \"divide-y divide-gray-200\",\n                                                    children: Object.entries(stats.byModel).map(function(_ref5) {\n                                                        var _ref6 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref5, 2), model = _ref6[0], data = _ref6[1];\n                                                        var modelData = data;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"tr\", {\n                                                            className: \"hover:bg-gray-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-900 font-mono\",\n                                                                    children: model\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: modelData.count\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, _this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-3 text-sm text-gray-600 text-right\",\n                                                                    children: formatTokens(modelData.tokens)\n                                                                }, void 0, false, {\n                                                                    fileName: _jsxFileName,\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, _this)\n                                                            ]\n                                                        }, model, true, {\n                                                            fileName: _jsxFileName,\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, _this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: _jsxFileName,\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 204,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 203,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"strong\", {\n                                            children: \"Nota:\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, this),\n                                        \" Los datos de uso de tokens se almacenan en Supabase. Los tokens de entrada y salida se registran autom\\xE1ticamente para cada actividad de IA.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"No hay datos de uso disponibles.\"\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 238,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\",\n                        children: \"Cerrar\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(TokenStatsModal, \"UoupJE0iLB2qnmfBJqK34UDeB5k=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_4__.usePlanLimits\n    ];\n});\n_c1 = TokenStatsModal;\n_s1(TokenStatsModal, \"1F00vAQqTrEahcnC0EvNidQjiF0=\", false, function() {\n    return [\n        _hooks_usePlanLimits__WEBPACK_IMPORTED_MODULE_4__.usePlanLimits\n    ];\n});\n_c = TokenStatsModal;\nvar _c;\n$RefreshReg$(_c, \"TokenStatsModal\");\nvar _c1;\n$RefreshReg$(_c1, \"TokenStatsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\n"));

/***/ })

});