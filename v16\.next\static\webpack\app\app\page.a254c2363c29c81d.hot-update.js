"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx":
/*!****************************************************************!*\
  !*** ./src/features/summaries/components/SummaryGenerator.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SummaryGenerator)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/defineProperty.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase/resumenesService */ \"(app-pages-browser)/./src/lib/supabase/resumenesService.ts\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nvar _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\summaries\\\\components\\\\SummaryGenerator.tsx\", _s1 = $RefreshSig$();\nfunction ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n        var o = Object.getOwnPropertySymbols(e);\n        r && (o = o.filter(function(r) {\n            return Object.getOwnPropertyDescriptor(e, r).enumerable;\n        })), t.push.apply(t, o);\n    }\n    return t;\n}\nfunction _objectSpread(e) {\n    for(var r = 1; r < arguments.length; r++){\n        var t = null != arguments[r] ? arguments[r] : {};\n        r % 2 ? ownKeys(Object(t), !0).forEach(function(r) {\n            (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(e, r, t[r]);\n        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {\n            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n        });\n    }\n    return e;\n}\n\n\n\n\n\n\n\n\n\n\nvar summarySchema = zod__WEBPACK_IMPORTED_MODULE_5__.z.object({\n    instrucciones: zod__WEBPACK_IMPORTED_MODULE_5__.z.string().min(10, 'Las instrucciones deben tener al menos 10 caracteres')\n});\nfunction SummaryGenerator(_ref) {\n    _s();\n    _s1();\n    var documentosSeleccionados = _ref.documentosSeleccionados, onSummaryGenerated = _ref.onSummaryGenerated;\n    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null), resumenGenerado = _useState[0], setResumenGenerado = _useState[1];\n    var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0), tiempoEstimado = _useState2[0], setTiempoEstimado = _useState2[1];\n    var _useBackgroundGenerat = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration)(), generateResumen = _useBackgroundGenerat.generateResumen, isGenerating = _useBackgroundGenerat.isGenerating, getActiveTask = _useBackgroundGenerat.getActiveTask;\n    var _useForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_4__.zodResolver)(summarySchema),\n        defaultValues: {\n            instrucciones: 'Crea un resumen completo y estructurado del tema, organizando los conceptos principales de manera clara y didáctica.'\n        }\n    }), register = _useForm.register, handleSubmit = _useForm.handleSubmit, errors = _useForm.formState.errors, reset = _useForm.reset;\n    // Funciones de validación\n    var validarDocumentoParaResumen = function validarDocumentoParaResumen(documento) {\n        if (!documento) {\n            return {\n                valido: false,\n                error: 'No se ha proporcionado ningún documento'\n            };\n        }\n        if (!documento.titulo || documento.titulo.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener un título'\n            };\n        }\n        if (!documento.contenido || documento.contenido.trim().length === 0) {\n            return {\n                valido: false,\n                error: 'El documento debe tener contenido'\n            };\n        }\n        if (documento.contenido.trim().length < 50) {\n            return {\n                valido: false,\n                error: 'El contenido del documento es demasiado corto para generar un resumen útil'\n            };\n        }\n        return {\n            valido: true\n        };\n    };\n    var estimarTiempoGeneracion = function estimarTiempoGeneracion(documento) {\n        if (!documento || !documento.contenido) {\n            return 30; // 30 segundos por defecto\n        }\n        var palabras = documento.contenido.split(/\\s+/).length;\n        // Estimación: ~1 segundo por cada 100 palabras, mínimo 15 segundos, máximo 120 segundos\n        var tiempoEstimado = Math.max(15, Math.min(120, Math.ceil(palabras / 100)));\n        return tiempoEstimado;\n    };\n    // Validaciones\n    var puedeGenerar = documentosSeleccionados.length === 1;\n    var documento = documentosSeleccionados[0];\n    var validacionDocumento = documento ? validarDocumentoParaResumen(documento) : {\n        valido: false,\n        error: 'No hay documento seleccionado'\n    };\n    // Estado de la tarea en segundo plano\n    var activeTask = getActiveTask('resumen');\n    var isCurrentlyGenerating = isGenerating('resumen');\n    // Observar estado de la tarea para mostrar resultado\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"SummaryGenerator.useEffect\": function() {\n            if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'completed' && activeTask.result) {\n                setResumenGenerado(activeTask.result);\n                setTiempoEstimado(0);\n            } else if ((activeTask === null || activeTask === void 0 ? void 0 : activeTask.status) === 'error') {\n                setTiempoEstimado(0);\n            }\n        }\n    }[\"SummaryGenerator.useEffect\"], [\n        activeTask\n    ]);\n    var onSubmit = /*#__PURE__*/ function() {\n        var _ref2 = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee2(data) {\n            var existe, errorMessage;\n            return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee2$(_context2) {\n                while(1)switch(_context2.prev = _context2.next){\n                    case 0:\n                        if (!(!puedeGenerar || !documento || !validacionDocumento.valido)) {\n                            _context2.next = 3;\n                            break;\n                        }\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('No se puede generar el resumen. Verifica que tengas exactamente un documento seleccionado.');\n                        return _context2.abrupt(\"return\");\n                    case 3:\n                        _context2.prev = 3;\n                        console.log('🚀 Iniciando generación de resumen...');\n                        setTiempoEstimado(estimarTiempoGeneracion(documento));\n                        // Verificar si ya existe un resumen para este documento\n                        console.log('🔍 Verificando si ya existe resumen...');\n                        _context2.next = 9;\n                        return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.existeResumenParaDocumento)(documento.id);\n                    case 9:\n                        existe = _context2.sent;\n                        if (!existe) {\n                            _context2.next = 14;\n                            break;\n                        }\n                        console.log('⚠️ Ya existe un resumen para este documento');\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Ya existe un resumen para este documento. Solo se permite un resumen por tema.');\n                        return _context2.abrupt(\"return\");\n                    case 14:\n                        console.log('✅ No existe resumen previo, continuando...');\n                        // Usar el sistema de tareas en segundo plano\n                        _context2.next = 17;\n                        return generateResumen({\n                            documento: documento,\n                            instrucciones: data.instrucciones,\n                            onComplete: function() {\n                                var _onComplete = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().mark(function _callee(resumenContent) {\n                                    var resumenId;\n                                    return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_2___default().wrap(function _callee$(_context) {\n                                        while(1)switch(_context.prev = _context.next){\n                                            case 0:\n                                                console.log('✅ Resumen generado, guardando en Supabase...');\n                                                // Guardar en Supabase\n                                                _context.next = 3;\n                                                return (0,_lib_supabase_resumenesService__WEBPACK_IMPORTED_MODULE_7__.guardarResumen)(documento.id, \"Resumen: \".concat(documento.titulo), resumenContent, data.instrucciones);\n                                            case 3:\n                                                resumenId = _context.sent;\n                                                if (resumenId) {\n                                                    console.log('✅ Resumen guardado exitosamente con ID:', resumenId);\n                                                    onSummaryGenerated === null || onSummaryGenerated === void 0 || onSummaryGenerated(resumenId);\n                                                    reset();\n                                                } else {\n                                                    console.error('❌ Error al guardar el resumen - no se recibió ID');\n                                                    react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error('Error al guardar el resumen en la base de datos');\n                                                }\n                                            case 5:\n                                            case \"end\":\n                                                return _context.stop();\n                                        }\n                                    }, _callee);\n                                }));\n                                function onComplete(_x2) {\n                                    return _onComplete.apply(this, arguments);\n                                }\n                                return onComplete;\n                            }(),\n                            onError: function onError(error) {\n                                console.error('❌ Error en generación de resumen:', error);\n                                setTiempoEstimado(0);\n                            }\n                        });\n                    case 17:\n                        _context2.next = 25;\n                        break;\n                    case 19:\n                        _context2.prev = 19;\n                        _context2.t0 = _context2[\"catch\"](3);\n                        console.error('Error al iniciar generación de resumen:', _context2.t0);\n                        errorMessage = _context2.t0 instanceof Error ? _context2.t0.message : 'Error al generar el resumen';\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(errorMessage);\n                        setTiempoEstimado(0);\n                    case 25:\n                    case \"end\":\n                        return _context2.stop();\n                }\n            }, _callee2, null, [\n                [\n                    3,\n                    19\n                ]\n            ]);\n        }));\n        return function onSubmit(_x) {\n            return _ref2.apply(this, arguments);\n        };\n    }();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-900 mb-2\",\n                        children: \"\\uD83D\\uDCC4 Generaci\\xF3n de Res\\xFAmenes\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    !puedeGenerar ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Selecci\\xF3n incorrecta\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: documentosSeleccionados.length === 0 ? 'Debes seleccionar exactamente un documento para generar un resumen.' : \"Tienes \".concat(documentosSeleccionados.length, \" documentos seleccionados. Solo se permite generar un resumen por tema.\")\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 161,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this) : !validacionDocumento.valido ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u26A0\\uFE0F Documento no v\\xE1lido\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: validacionDocumento.error\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"\\u2705 Documento seleccionado:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                        children: documento.titulo\n                                    }, void 0, false, {\n                                        fileName: _jsxFileName,\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    documento.numero_tema && \" (Tema \".concat(documento.numero_tema, \")\")\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-xs mt-1 text-blue-600\",\n                                children: [\n                                    \"Contenido: ~\",\n                                    documento.contenido.split(/\\s+/).length,\n                                    \" palabras\"\n                                ]\n                            }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            puedeGenerar && validacionDocumento.valido && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"label\", {\n                                htmlFor: \"instrucciones\",\n                                className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                children: \"Instrucciones para el resumen:\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"textarea\", _objectSpread(_objectSpread({\n                                id: \"instrucciones\"\n                            }, register('instrucciones')), {}, {\n                                disabled: isGenerating,\n                                rows: 4,\n                                className: \"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline resize-vertical\",\n                                placeholder: \"Describe c\\xF3mo quieres que sea el resumen...\"\n                            }), void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            errors.instrucciones && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"span\", {\n                                className: \"text-red-500 text-sm\",\n                                children: errors.instrucciones.message\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Especifica el enfoque, nivel de detalle, o aspectos particulares que quieres que incluya el resumen.\"\n                            }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: isCurrentlyGenerating,\n                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                        children: isCurrentlyGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    className: \"animate-spin -ml-1 mr-3 h-5 w-5 text-white\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    fill: \"none\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"circle\", {\n                                            className: \"opacity-25\",\n                                            cx: \"12\",\n                                            cy: \"12\",\n                                            r: \"10\",\n                                            stroke: \"currentColor\",\n                                            strokeWidth: \"4\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            className: \"opacity-75\",\n                                            fill: \"currentColor\",\n                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 219,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 217,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generando resumen...\",\n                                tiempoEstimado > 0 && \" (~\".concat(tiempoEstimado, \"s)\")\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    className: \"h-5 w-5 mr-2\",\n                                    viewBox: \"0 0 20 20\",\n                                    fill: \"currentColor\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 227,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M8 6a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 9a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1zM8 12a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: _jsxFileName,\n                                            lineNumber: 228,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 226,\n                                    columnNumber: 17\n                                }, this),\n                                \"Generar Resumen\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this),\n                    isGenerating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                            className: \"text-yellow-800 text-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"strong\", {\n                                    children: \"\\u23F3 Generando resumen...\"\n                                }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 238,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 238,\n                                    columnNumber: 56\n                                }, this),\n                                \"La IA est\\xE1 analizando el contenido y creando un resumen estructurado. Este proceso puede tardar \",\n                                tiempoEstimado > 0 ? \"aproximadamente \".concat(tiempoEstimado, \" segundos\") : 'unos momentos',\n                                \".\"\n                            ]\n                        }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 237,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 9\n            }, this),\n            resumenGenerado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                className: \"mt-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold mb-3\",\n                        children: \"\\uD83D\\uDCCB Resumen Generado\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"div\", {\n                            className: \"prose prose-sm max-w-none\",\n                            dangerouslySetInnerHTML: {\n                                __html: resumenGenerado.replace(/\\n/g, '<br />')\n                            }\n                        }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-500 mt-2\",\n                        children: \"\\u2705 Resumen guardado exitosamente. Puedes acceder a \\xE9l desde la secci\\xF3n de res\\xFAmenes.\"\n                    }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(SummaryGenerator, \"/vRDUXc96KupzW4DGJlc//3RXKo=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c1 = SummaryGenerator;\n_s1(SummaryGenerator, \"PSDV3WtsHhTMSACB9kYGm0nxOZM=\", false, function() {\n    return [\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_8__.useBackgroundGeneration,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm\n    ];\n});\n_c = SummaryGenerator;\nvar _c;\n$RefreshReg$(_c, \"SummaryGenerator\");\nvar _c1;\n$RefreshReg$(_c1, \"SummaryGenerator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\n"));

/***/ })

});