"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts":
/*!***********************************************!*\
  !*** ./src/lib/supabase/tokenUsageService.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   canPerformActivity: () => (/* binding */ canPerformActivity),\n/* harmony export */   checkTokenLimit: () => (/* binding */ checkTokenLimit),\n/* harmony export */   getTokenPurchaseHistory: () => (/* binding */ getTokenPurchaseHistory),\n/* harmony export */   getTokenUsageProgress: () => (/* binding */ getTokenUsageProgress),\n/* harmony export */   getUserPlanInfo: () => (/* binding */ getUserPlanInfo),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   getUserTokenStats: () => (/* binding */ getUserTokenStats),\n/* harmony export */   saveTokenUsage: () => (/* binding */ saveTokenUsage)\n/* harmony export */ });\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/@babel/runtime/helpers/esm/asyncToGenerator.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node_modules/@babel/runtime/regenerator/index.js */ \"(app-pages-browser)/./node_modules/@babel/runtime/regenerator/index.js\");\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\");\n/* harmony import */ var _lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/planLimits */ \"(app-pages-browser)/./src/lib/utils/planLimits.ts\");\n\n\n\n\n/**\n * Guarda el uso de tokens en Supabase con validación de plan\n */ function saveTokenUsage(_x) {\n    return _saveTokenUsage.apply(this, arguments);\n}\n/**\n * Actualiza el contador mensual de tokens del usuario\n */ function _saveTokenUsage() {\n    _saveTokenUsage = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee(data) {\n        var supabase, _yield$supabase$auth$, user, userError, accessValidation, usageRecord, _yield$supabase$from$, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee$(_context) {\n            while(1)switch(_context.prev = _context.next){\n                case 0:\n                    _context.prev = 0;\n                    console.log('🔄 saveTokenUsage (cliente) iniciado con data:', data);\n                    // Este servicio solo funciona en el cliente\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    console.log('✅ Cliente Supabase creado');\n                    _context.next = 6;\n                    return supabase.auth.getUser();\n                case 6:\n                    _yield$supabase$auth$ = _context.sent;\n                    user = _yield$supabase$auth$.data.user;\n                    userError = _yield$supabase$auth$.error;\n                    console.log('👤 Usuario obtenido:', user ? \"ID: \".concat(user.id, \", Email: \").concat(user.email) : 'No autenticado');\n                    if (!(userError || !user)) {\n                        _context.next = 13;\n                        break;\n                    }\n                    console.warn('❌ No hay usuario autenticado para guardar tokens:', userError === null || userError === void 0 ? void 0 : userError.message);\n                    return _context.abrupt(\"return\");\n                case 13:\n                    _context.next = 15;\n                    return validateActivityAccess(user.id, data.activity, data.usage.totalTokens);\n                case 15:\n                    accessValidation = _context.sent;\n                    if (accessValidation.allowed) {\n                        _context.next = 19;\n                        break;\n                    }\n                    console.warn('❌ Acceso denegado para actividad:', accessValidation.reason);\n                    throw new Error(accessValidation.reason);\n                case 19:\n                    usageRecord = {\n                        user_id: user.id,\n                        activity_type: data.activity,\n                        model_name: data.model,\n                        prompt_tokens: data.usage.promptTokens,\n                        completion_tokens: data.usage.completionTokens,\n                        total_tokens: data.usage.totalTokens,\n                        estimated_cost: data.usage.estimatedCost || 0,\n                        usage_month: new Date().toISOString().slice(0, 7) + '-01' // YYYY-MM-01\n                    };\n                    console.log('📝 Registro a insertar:', usageRecord);\n                    _context.next = 23;\n                    return supabase.from('user_token_usage').insert([\n                        usageRecord\n                    ]);\n                case 23:\n                    _yield$supabase$from$ = _context.sent;\n                    error = _yield$supabase$from$.error;\n                    if (!error) {\n                        _context.next = 28;\n                        break;\n                    }\n                    console.error('❌ Error al guardar uso de tokens:', error);\n                    return _context.abrupt(\"return\");\n                case 28:\n                    console.log('✅ Registro insertado exitosamente en user_token_usage');\n                    // Actualizar contador mensual del usuario\n                    _context.next = 31;\n                    return updateMonthlyTokenCount(user.id, data.usage.totalTokens);\n                case 31:\n                    _context.next = 36;\n                    break;\n                case 33:\n                    _context.prev = 33;\n                    _context.t0 = _context[\"catch\"](0);\n                    console.error('Error en saveTokenUsage:', _context.t0);\n                case 36:\n                case \"end\":\n                    return _context.stop();\n            }\n        }, _callee, null, [\n            [\n                0,\n                33\n            ]\n        ]);\n    }));\n    return _saveTokenUsage.apply(this, arguments);\n}\nfunction updateMonthlyTokenCount(_x2, _x3) {\n    return _updateMonthlyTokenCount.apply(this, arguments);\n}\n/**\n * Obtiene estadísticas de uso de tokens del usuario actual\n */ function _updateMonthlyTokenCount() {\n    _updateMonthlyTokenCount = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee2(userId, tokens) {\n        var supabase, currentMonth, _yield$supabase$from$2, profile, profileError, _yield$supabase$from$3, insertError, newTokenCount, _yield$supabase$from$4, updateError;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee2$(_context2) {\n            while(1)switch(_context2.prev = _context2.next){\n                case 0:\n                    _context2.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    currentMonth = new Date().toISOString().slice(0, 7) + '-01'; // Obtener o crear perfil del usuario\n                    _context2.next = 5;\n                    return supabase.from('user_profiles').select('*').eq('user_id', userId).single();\n                case 5:\n                    _yield$supabase$from$2 = _context2.sent;\n                    profile = _yield$supabase$from$2.data;\n                    profileError = _yield$supabase$from$2.error;\n                    if (!(profileError && profileError.code !== 'PGRST116')) {\n                        _context2.next = 11;\n                        break;\n                    }\n                    console.error('Error al obtener perfil:', profileError);\n                    return _context2.abrupt(\"return\");\n                case 11:\n                    if (profile) {\n                        _context2.next = 19;\n                        break;\n                    }\n                    _context2.next = 14;\n                    return supabase.from('user_profiles').insert([\n                        {\n                            user_id: userId,\n                            subscription_plan: 'free',\n                            monthly_token_limit: 50000,\n                            current_month_tokens: tokens,\n                            current_month: currentMonth\n                        }\n                    ]);\n                case 14:\n                    _yield$supabase$from$3 = _context2.sent;\n                    insertError = _yield$supabase$from$3.error;\n                    if (insertError) {\n                        console.error('Error al crear perfil:', insertError);\n                    }\n                    _context2.next = 25;\n                    break;\n                case 19:\n                    // Actualizar perfil existente\n                    newTokenCount = profile.current_month === currentMonth ? profile.current_month_tokens + tokens : tokens; // Reset si es nuevo mes\n                    _context2.next = 22;\n                    return supabase.from('user_profiles').update({\n                        current_month_tokens: newTokenCount,\n                        current_month: currentMonth,\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', userId);\n                case 22:\n                    _yield$supabase$from$4 = _context2.sent;\n                    updateError = _yield$supabase$from$4.error;\n                    if (updateError) {\n                        console.error('Error al actualizar perfil:', updateError);\n                    }\n                case 25:\n                    _context2.next = 30;\n                    break;\n                case 27:\n                    _context2.prev = 27;\n                    _context2.t0 = _context2[\"catch\"](0);\n                    console.error('Error en updateMonthlyTokenCount:', _context2.t0);\n                case 30:\n                case \"end\":\n                    return _context2.stop();\n            }\n        }, _callee2, null, [\n            [\n                0,\n                27\n            ]\n        ]);\n    }));\n    return _updateMonthlyTokenCount.apply(this, arguments);\n}\nfunction getUserTokenStats() {\n    return _getUserTokenStats.apply(this, arguments);\n}\n/**\n * Calcula estadísticas a partir de los registros\n */ function _getUserTokenStats() {\n    _getUserTokenStats = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee3() {\n        var supabase, _yield$supabase$auth$2, user, userError, _yield$supabase$from$5, records, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee3$(_context3) {\n            while(1)switch(_context3.prev = _context3.next){\n                case 0:\n                    _context3.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context3.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$2 = _context3.sent;\n                    user = _yield$supabase$auth$2.data.user;\n                    userError = _yield$supabase$auth$2.error;\n                    if (!(userError || !user)) {\n                        _context3.next = 9;\n                        break;\n                    }\n                    return _context3.abrupt(\"return\", getEmptyStats());\n                case 9:\n                    _context3.next = 11;\n                    return supabase.from('user_token_usage').select('*').eq('user_id', user.id).order('created_at', {\n                        ascending: false\n                    });\n                case 11:\n                    _yield$supabase$from$5 = _context3.sent;\n                    records = _yield$supabase$from$5.data;\n                    error = _yield$supabase$from$5.error;\n                    if (!error) {\n                        _context3.next = 17;\n                        break;\n                    }\n                    console.error('Error al obtener estadísticas:', error);\n                    return _context3.abrupt(\"return\", getEmptyStats());\n                case 17:\n                    return _context3.abrupt(\"return\", calculateStats(records || []));\n                case 20:\n                    _context3.prev = 20;\n                    _context3.t0 = _context3[\"catch\"](0);\n                    console.error('Error en getUserTokenStats:', _context3.t0);\n                    return _context3.abrupt(\"return\", getEmptyStats());\n                case 24:\n                case \"end\":\n                    return _context3.stop();\n            }\n        }, _callee3, null, [\n            [\n                0,\n                20\n            ]\n        ]);\n    }));\n    return _getUserTokenStats.apply(this, arguments);\n}\nfunction calculateStats(records) {\n    var stats = {\n        totalSessions: records.length,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n    records.forEach(function(record) {\n        var tokens = record.total_tokens;\n        var cost = record.estimated_cost;\n        stats.totalTokens += tokens;\n        stats.totalCost += cost;\n        // Por actividad\n        if (!stats.byActivity[record.activity_type]) {\n            stats.byActivity[record.activity_type] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byActivity[record.activity_type].tokens += tokens;\n        stats.byActivity[record.activity_type].cost += cost;\n        stats.byActivity[record.activity_type].count += 1;\n        // Por modelo\n        if (!stats.byModel[record.model_name]) {\n            stats.byModel[record.model_name] = {\n                tokens: 0,\n                cost: 0,\n                count: 0\n            };\n        }\n        stats.byModel[record.model_name].tokens += tokens;\n        stats.byModel[record.model_name].cost += cost;\n        stats.byModel[record.model_name].count += 1;\n    });\n    return stats;\n}\n/**\n * Retorna estadísticas vacías\n */ function getEmptyStats() {\n    return {\n        totalSessions: 0,\n        totalTokens: 0,\n        totalCost: 0,\n        byActivity: {},\n        byModel: {}\n    };\n}\n/**\n * Obtiene el perfil del usuario actual\n */ function getUserProfile() {\n    return _getUserProfile.apply(this, arguments);\n}\n/**\n * Verifica si el usuario ha alcanzado su límite mensual\n */ function _getUserProfile() {\n    _getUserProfile = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee4() {\n        var supabase, _yield$supabase$auth$3, user, userError, _yield$supabase$from$6, profile, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee4$(_context4) {\n            while(1)switch(_context4.prev = _context4.next){\n                case 0:\n                    _context4.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context4.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$3 = _context4.sent;\n                    user = _yield$supabase$auth$3.data.user;\n                    userError = _yield$supabase$auth$3.error;\n                    if (!(userError || !user)) {\n                        _context4.next = 9;\n                        break;\n                    }\n                    return _context4.abrupt(\"return\", null);\n                case 9:\n                    _context4.next = 11;\n                    return supabase.from('user_profiles').select('*').eq('user_id', user.id).single();\n                case 11:\n                    _yield$supabase$from$6 = _context4.sent;\n                    profile = _yield$supabase$from$6.data;\n                    error = _yield$supabase$from$6.error;\n                    if (!(error && error.code !== 'PGRST116')) {\n                        _context4.next = 17;\n                        break;\n                    }\n                    console.error('Error al obtener perfil:', error);\n                    return _context4.abrupt(\"return\", null);\n                case 17:\n                    return _context4.abrupt(\"return\", profile);\n                case 20:\n                    _context4.prev = 20;\n                    _context4.t0 = _context4[\"catch\"](0);\n                    console.error('Error en getUserProfile:', _context4.t0);\n                    return _context4.abrupt(\"return\", null);\n                case 24:\n                case \"end\":\n                    return _context4.stop();\n            }\n        }, _callee4, null, [\n            [\n                0,\n                20\n            ]\n        ]);\n    }));\n    return _getUserProfile.apply(this, arguments);\n}\nfunction checkTokenLimit() {\n    return _checkTokenLimit.apply(this, arguments);\n}\n/**\n * Valida si un usuario tiene acceso a una actividad específica\n */ function _checkTokenLimit() {\n    _checkTokenLimit = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee5() {\n        var profile, currentTokens, monthlyLimit, percentage, hasReachedLimit;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee5$(_context5) {\n            while(1)switch(_context5.prev = _context5.next){\n                case 0:\n                    _context5.prev = 0;\n                    _context5.next = 3;\n                    return getUserProfile();\n                case 3:\n                    profile = _context5.sent;\n                    if (profile) {\n                        _context5.next = 6;\n                        break;\n                    }\n                    return _context5.abrupt(\"return\", {\n                        hasReachedLimit: false,\n                        currentTokens: 0,\n                        limit: 50000,\n                        percentage: 0\n                    });\n                case 6:\n                    currentTokens = profile.current_month_tokens || 0;\n                    monthlyLimit = profile.monthly_token_limit || 0;\n                    percentage = monthlyLimit > 0 ? currentTokens / monthlyLimit * 100 : 0;\n                    hasReachedLimit = currentTokens >= monthlyLimit;\n                    return _context5.abrupt(\"return\", {\n                        hasReachedLimit: hasReachedLimit,\n                        currentTokens: currentTokens,\n                        limit: monthlyLimit,\n                        percentage: percentage\n                    });\n                case 13:\n                    _context5.prev = 13;\n                    _context5.t0 = _context5[\"catch\"](0);\n                    console.error('Error en checkTokenLimit:', _context5.t0);\n                    return _context5.abrupt(\"return\", {\n                        hasReachedLimit: false,\n                        currentTokens: 0,\n                        limit: 50000,\n                        percentage: 0\n                    });\n                case 17:\n                case \"end\":\n                    return _context5.stop();\n            }\n        }, _callee5, null, [\n            [\n                0,\n                13\n            ]\n        ]);\n    }));\n    return _checkTokenLimit.apply(this, arguments);\n}\nfunction validateActivityAccess(_x4, _x5, _x6) {\n    return _validateActivityAccess.apply(this, arguments);\n}\n/**\n * Obtiene información detallada del plan del usuario\n */ function _validateActivityAccess() {\n    _validateActivityAccess = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee6(userId, activity, tokensToUse) {\n        var supabase, _yield$supabase$from$7, profile, error, activityToFeature, featureName, currentMonth, currentTokens;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee6$(_context6) {\n            while(1)switch(_context6.prev = _context6.next){\n                case 0:\n                    _context6.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)(); // Obtener perfil del usuario\n                    _context6.next = 4;\n                    return supabase.from('user_profiles').select('subscription_plan, payment_verified, current_month_tokens, monthly_token_limit, current_month').eq('user_id', userId).single();\n                case 4:\n                    _yield$supabase$from$7 = _context6.sent;\n                    profile = _yield$supabase$from$7.data;\n                    error = _yield$supabase$from$7.error;\n                    if (!(error || !profile)) {\n                        _context6.next = 9;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Perfil de usuario no encontrado'\n                    });\n                case 9:\n                    // Mapear actividades a características\n                    activityToFeature = {\n                        'test_generation': 'test_generation',\n                        'flashcard_generation': 'flashcard_generation',\n                        'mind_map_generation': 'mind_map_generation',\n                        'ai_chat': 'ai_tutor_chat',\n                        'study_planning': 'study_planning',\n                        'summary_generation': 'summary_a1_a2',\n                        'document_analysis': 'document_upload'\n                    };\n                    featureName = activityToFeature[activity] || activity; // Verificar acceso a la característica según el plan\n                    if ((0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_3__.hasFeatureAccess)(profile.subscription_plan, featureName)) {\n                        _context6.next = 13;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: \"La actividad \".concat(activity, \" no est\\xE1 disponible en el plan \").concat(profile.subscription_plan)\n                    });\n                case 13:\n                    if (!(profile.subscription_plan !== 'free' && !profile.payment_verified)) {\n                        _context6.next = 15;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Pago no verificado. Complete el proceso de pago para usar esta función.'\n                    });\n                case 15:\n                    // Verificar límites de tokens\n                    currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n                    currentTokens = profile.current_month_tokens; // Reset si es nuevo mes\n                    if (profile.current_month !== currentMonth) {\n                        currentTokens = 0;\n                    }\n                    if (!(currentTokens + tokensToUse > profile.monthly_token_limit)) {\n                        _context6.next = 20;\n                        break;\n                    }\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: \"L\\xEDmite mensual de tokens alcanzado. Usado: \".concat(currentTokens, \"/\").concat(profile.monthly_token_limit)\n                    });\n                case 20:\n                    return _context6.abrupt(\"return\", {\n                        allowed: true\n                    });\n                case 23:\n                    _context6.prev = 23;\n                    _context6.t0 = _context6[\"catch\"](0);\n                    console.error('Error validating activity access:', _context6.t0);\n                    return _context6.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Error interno de validación'\n                    });\n                case 27:\n                case \"end\":\n                    return _context6.stop();\n            }\n        }, _callee6, null, [\n            [\n                0,\n                23\n            ]\n        ]);\n    }));\n    return _validateActivityAccess.apply(this, arguments);\n}\nfunction getUserPlanInfo() {\n    return _getUserPlanInfo.apply(this, arguments);\n}\n/**\n * Verifica si el usuario puede realizar una actividad específica antes de ejecutarla\n */ function _getUserPlanInfo() {\n    _getUserPlanInfo = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee7() {\n        var profile, planConfig, percentage;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee7$(_context7) {\n            while(1)switch(_context7.prev = _context7.next){\n                case 0:\n                    _context7.prev = 0;\n                    _context7.next = 3;\n                    return getUserProfile();\n                case 3:\n                    profile = _context7.sent;\n                    if (profile) {\n                        _context7.next = 6;\n                        break;\n                    }\n                    return _context7.abrupt(\"return\", null);\n                case 6:\n                    planConfig = (0,_lib_utils_planLimits__WEBPACK_IMPORTED_MODULE_3__.getPlanConfiguration)(profile.subscription_plan);\n                    if (planConfig) {\n                        _context7.next = 9;\n                        break;\n                    }\n                    return _context7.abrupt(\"return\", null);\n                case 9:\n                    percentage = profile.current_month_tokens / profile.monthly_token_limit * 100;\n                    return _context7.abrupt(\"return\", {\n                        plan: profile.subscription_plan,\n                        planName: planConfig.name,\n                        features: planConfig.features,\n                        tokenUsage: {\n                            current: profile.current_month_tokens,\n                            limit: profile.monthly_token_limit,\n                            percentage: Math.round(percentage),\n                            remaining: profile.monthly_token_limit - profile.current_month_tokens\n                        },\n                        paymentVerified: profile.payment_verified || profile.subscription_plan === 'free'\n                    });\n                case 13:\n                    _context7.prev = 13;\n                    _context7.t0 = _context7[\"catch\"](0);\n                    console.error('Error getting user plan info:', _context7.t0);\n                    return _context7.abrupt(\"return\", null);\n                case 17:\n                case \"end\":\n                    return _context7.stop();\n            }\n        }, _callee7, null, [\n            [\n                0,\n                13\n            ]\n        ]);\n    }));\n    return _getUserPlanInfo.apply(this, arguments);\n}\nfunction canPerformActivity(_x7) {\n    return _canPerformActivity.apply(this, arguments);\n}\n/**\n * Obtiene datos de progreso de tokens para estadísticas avanzadas\n */ function _canPerformActivity() {\n    _canPerformActivity = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee8(activity) {\n        var estimatedTokens, supabase, _yield$supabase$auth$4, user, userError, validation, planInfo, _args8 = arguments;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee8$(_context8) {\n            while(1)switch(_context8.prev = _context8.next){\n                case 0:\n                    estimatedTokens = _args8.length > 1 && _args8[1] !== undefined ? _args8[1] : 0;\n                    _context8.prev = 1;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context8.next = 5;\n                    return supabase.auth.getUser();\n                case 5:\n                    _yield$supabase$auth$4 = _context8.sent;\n                    user = _yield$supabase$auth$4.data.user;\n                    userError = _yield$supabase$auth$4.error;\n                    if (!(userError || !user)) {\n                        _context8.next = 10;\n                        break;\n                    }\n                    return _context8.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Usuario no autenticado'\n                    });\n                case 10:\n                    _context8.next = 12;\n                    return validateActivityAccess(user.id, activity, estimatedTokens);\n                case 12:\n                    validation = _context8.sent;\n                    if (validation.allowed) {\n                        _context8.next = 18;\n                        break;\n                    }\n                    _context8.next = 16;\n                    return getUserPlanInfo();\n                case 16:\n                    planInfo = _context8.sent;\n                    return _context8.abrupt(\"return\", {\n                        allowed: false,\n                        reason: validation.reason,\n                        planInfo: planInfo\n                    });\n                case 18:\n                    return _context8.abrupt(\"return\", {\n                        allowed: true\n                    });\n                case 21:\n                    _context8.prev = 21;\n                    _context8.t0 = _context8[\"catch\"](1);\n                    console.error('Error checking activity permission:', _context8.t0);\n                    return _context8.abrupt(\"return\", {\n                        allowed: false,\n                        reason: 'Error interno de validación'\n                    });\n                case 25:\n                case \"end\":\n                    return _context8.stop();\n            }\n        }, _callee8, null, [\n            [\n                1,\n                21\n            ]\n        ]);\n    }));\n    return _canPerformActivity.apply(this, arguments);\n}\nfunction getTokenUsageProgress() {\n    return _getTokenUsageProgress.apply(this, arguments);\n}\n/**\n * Obtiene historial de compras de tokens del usuario\n */ function _getTokenUsageProgress() {\n    _getTokenUsageProgress = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee9() {\n        var supabase, _yield$supabase$auth$5, user, userError, profile, percentage, thirtyDaysAgo, _yield$supabase$from$8, dailyUsage, historyError, dailyHistory, dailyMap, i, date, dateStr;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee9$(_context9) {\n            while(1)switch(_context9.prev = _context9.next){\n                case 0:\n                    _context9.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context9.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$5 = _context9.sent;\n                    user = _yield$supabase$auth$5.data.user;\n                    userError = _yield$supabase$auth$5.error;\n                    if (!(userError || !user)) {\n                        _context9.next = 9;\n                        break;\n                    }\n                    return _context9.abrupt(\"return\", null);\n                case 9:\n                    _context9.next = 11;\n                    return getUserProfile();\n                case 11:\n                    profile = _context9.sent;\n                    if (profile) {\n                        _context9.next = 14;\n                        break;\n                    }\n                    return _context9.abrupt(\"return\", null);\n                case 14:\n                    // Calcular porcentaje de uso\n                    percentage = profile.current_month_tokens / profile.monthly_token_limit * 100; // Obtener historial diario de los últimos 30 días\n                    thirtyDaysAgo = new Date();\n                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n                    _context9.next = 19;\n                    return supabase.from('user_token_usage').select('created_at, total_tokens').eq('user_id', user.id).gte('created_at', thirtyDaysAgo.toISOString()).order('created_at', {\n                        ascending: true\n                    });\n                case 19:\n                    _yield$supabase$from$8 = _context9.sent;\n                    dailyUsage = _yield$supabase$from$8.data;\n                    historyError = _yield$supabase$from$8.error;\n                    if (historyError) {\n                        console.error('Error al obtener historial diario:', historyError);\n                    }\n                    // Agrupar por día\n                    dailyHistory = [];\n                    dailyMap = new Map();\n                    if (dailyUsage) {\n                        dailyUsage.forEach(function(record) {\n                            var date = new Date(record.created_at).toISOString().split('T')[0];\n                            var currentTokens = dailyMap.get(date) || 0;\n                            dailyMap.set(date, currentTokens + record.total_tokens);\n                        });\n                        // Convertir a array ordenado\n                        for(i = 29; i >= 0; i--){\n                            date = new Date();\n                            date.setDate(date.getDate() - i);\n                            dateStr = date.toISOString().split('T')[0];\n                            dailyHistory.push({\n                                date: dateStr,\n                                tokens: dailyMap.get(dateStr) || 0\n                            });\n                        }\n                    }\n                    return _context9.abrupt(\"return\", {\n                        percentage: Math.round(percentage),\n                        limit: profile.monthly_token_limit,\n                        used: profile.current_month_tokens,\n                        remaining: profile.monthly_token_limit - profile.current_month_tokens,\n                        dailyHistory: dailyHistory\n                    });\n                case 29:\n                    _context9.prev = 29;\n                    _context9.t0 = _context9[\"catch\"](0);\n                    console.error('Error en getTokenUsageProgress:', _context9.t0);\n                    return _context9.abrupt(\"return\", null);\n                case 33:\n                case \"end\":\n                    return _context9.stop();\n            }\n        }, _callee9, null, [\n            [\n                0,\n                29\n            ]\n        ]);\n    }));\n    return _getTokenUsageProgress.apply(this, arguments);\n}\nfunction getTokenPurchaseHistory() {\n    return _getTokenPurchaseHistory.apply(this, arguments);\n}\nfunction _getTokenPurchaseHistory() {\n    _getTokenPurchaseHistory = (0,C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_next_dist_compiled_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(/*#__PURE__*/ C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().mark(function _callee10() {\n        var supabase, _yield$supabase$auth$6, user, userError, _yield$supabase$from$9, purchases, error;\n        return C_Users_naata_Documents_augment_projects_OposI_v16_node_modules_babel_runtime_regenerator_index_js__WEBPACK_IMPORTED_MODULE_1___default().wrap(function _callee10$(_context10) {\n            while(1)switch(_context10.prev = _context10.next){\n                case 0:\n                    _context10.prev = 0;\n                    supabase = (0,_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n                    _context10.next = 4;\n                    return supabase.auth.getUser();\n                case 4:\n                    _yield$supabase$auth$6 = _context10.sent;\n                    user = _yield$supabase$auth$6.data.user;\n                    userError = _yield$supabase$auth$6.error;\n                    if (!(userError || !user)) {\n                        _context10.next = 9;\n                        break;\n                    }\n                    return _context10.abrupt(\"return\", null);\n                case 9:\n                    _context10.next = 11;\n                    return supabase.from('token_purchases').select('id, amount, price, created_at, status').eq('user_id', user.id).order('created_at', {\n                        ascending: false\n                    });\n                case 11:\n                    _yield$supabase$from$9 = _context10.sent;\n                    purchases = _yield$supabase$from$9.data;\n                    error = _yield$supabase$from$9.error;\n                    if (!error) {\n                        _context10.next = 17;\n                        break;\n                    }\n                    console.error('Error al obtener historial de compras:', error);\n                    return _context10.abrupt(\"return\", null);\n                case 17:\n                    return _context10.abrupt(\"return\", purchases || []);\n                case 20:\n                    _context10.prev = 20;\n                    _context10.t0 = _context10[\"catch\"](0);\n                    console.error('Error en getTokenPurchaseHistory:', _context10.t0);\n                    return _context10.abrupt(\"return\", null);\n                case 24:\n                case \"end\":\n                    return _context10.stop();\n            }\n        }, _callee10, null, [\n            [\n                0,\n                20\n            ]\n        ]);\n    }));\n    return _getTokenPurchaseHistory.apply(this, arguments);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/tokenUsageService.ts\n"));

/***/ })

});